load("//build/bazel_common_rules/exec:exec.bzl", "exec_test")
load(
    "//build/kernel/kleaf:kernel.bzl",
    "kernel_build",
    "ddk_headers",
    "ddk_module",
    "kernel_abi",
    "kernel_abi_dist",
    "kernel_module",
    "kernel_module_group",
)
load("//build/bazel_common_rules/test_mappings:test_mappings.bzl", "test_mappings_dist")
load("@kernel_toolchain_info//:dict.bzl", "CLANG_VERSION")
load("//build/bazel_common_rules/dist:dist.bzl", "copy_to_dist_dir")
load("//build/bazel_common_rules/exec:exec.bzl", "exec")

filegroup(
    name = "imgtech_module_headers_group",
    srcs = glob(
        [
            "generated/rogue/**/*.h",
            "hwdefs/**/*.h",
            "include/**/*.h",
            "kernel/**/*.h",
            "services/*.h",
            "services/include/*.h",
            "services/include/rogue/*.h",
            "services/server/common/*.h",
            "services/server/devices/*.h",
            "services/server/devices/rogue/*.h",
            "services/server/env/linux/*.h",
            "services/server/include/*.h",
            "services/shared/common/*.h",
            "services/shared/devices/rogue/*.h",
            "services/shared/include/*.h",
            "services/system/include/*.h",
            "services/system/rogue/axe_am62/*.h",
            "services/system/rogue/bxs_am62p/*.h",
            "services/system/rogue/include/*.h",
        ],
   ),
)

filegroup(
    name = "am62x_imgtech_module_sources",
    srcs = [
        "generated/rogue/cache_bridge/client_cache_direct_bridge.c",
        "generated/rogue/cache_bridge/server_cache_bridge.c",
        "generated/rogue/cmm_bridge/server_cmm_bridge.c",
        "generated/rogue/devicememhistory_bridge/client_devicememhistory_direct_bridge.c",
        "generated/rogue/devicememhistory_bridge/server_devicememhistory_bridge.c",
        "generated/rogue/di_bridge/server_di_bridge.c",
        "generated/rogue/dmabuf_bridge/server_dmabuf_bridge.c",
        "generated/rogue/mm_bridge/client_mm_direct_bridge.c",
        "generated/rogue/mm_bridge/server_mm_bridge.c",
        "generated/rogue/mmextmem_bridge/server_mmextmem_bridge.c",
        "generated/rogue/pvrtl_bridge/client_pvrtl_direct_bridge.c",
        "generated/rogue/pvrtl_bridge/server_pvrtl_bridge.c",
        "generated/rogue/rgxbreakpoint_bridge/server_rgxbreakpoint_bridge.c",
        "generated/rogue/rgxcmp_bridge/server_rgxcmp_bridge.c",
        "generated/rogue/rgxfwdbg_bridge/server_rgxfwdbg_bridge.c",
        "generated/rogue/rgxhwperf_bridge/server_rgxhwperf_bridge.c",
        "generated/rogue/rgxregconfig_bridge/server_rgxregconfig_bridge.c",
        "generated/rogue/rgxta3d_bridge/server_rgxta3d_bridge.c",
        "generated/rogue/rgxtimerquery_bridge/server_rgxtimerquery_bridge.c",
        "generated/rogue/rgxtq2_bridge/server_rgxtq2_bridge.c",
        "generated/rogue/rgxtq_bridge/server_rgxtq_bridge.c",
        "generated/rogue/srvcore_bridge/server_srvcore_bridge.c",
        "generated/rogue/sync_bridge/client_sync_direct_bridge.c",
        "generated/rogue/sync_bridge/server_sync_bridge.c",
        "generated/rogue/synctracking_bridge/client_synctracking_direct_bridge.c",
        "generated/rogue/synctracking_bridge/server_synctracking_bridge.c",
        "services/server/common/cache_km.c",
        "services/server/common/connection_server.c",
        "services/server/common/debug_common.c",
        "services/server/common/devicemem_heapcfg.c",
        "services/server/common/devicemem_history_server.c",
        "services/server/common/devicemem_server.c",
        "services/server/common/di_impl_brg.c",
        "services/server/common/di_server.c",
        "services/server/common/handle.c",
        "services/server/common/info_page_km.c",
        "services/server/common/lists.c",
        "services/server/common/mmu_common.c",
        "services/server/common/physheap.c",
        "services/server/common/physmem.c",
        "services/server/common/physmem_extmem.c",
        "services/server/common/physmem_hostmem.c",
        "services/server/common/physmem_lma.c",
        "services/server/common/physmem_osmem.c",
        "services/server/common/physmem_ramem.c",
        "services/server/common/pmr.c",
        "services/server/common/power.c",
        "services/server/common/process_stats.c",
        "services/server/common/pvr_notifier.c",
        "services/server/common/pvrsrv.c",
        "services/server/common/pvrsrv_bridge_init.c",
        "services/server/common/pvrsrv_pool.c",
        "services/server/common/srvcore.c",
        "services/server/common/sync_checkpoint.c",
        "services/server/common/sync_server.c",
        "services/server/common/tlintern.c",
        "services/server/common/tlserver.c",
        "services/server/common/tlstream.c",
        "services/server/common/vmm_pvz_client.c",
        "services/server/common/vmm_pvz_server.c",
        "services/server/common/vz_vmm_pvz.c",
        "services/server/common/vz_vmm_vm.c",
        "services/server/devices/rgx_bridge_init.c",
        "services/server/devices/rgxbreakpoint.c",
        "services/server/devices/rgxbvnc.c",
        "services/server/devices/rgxccb.c",
        "services/server/devices/rgxcompute.c",
        "services/server/devices/rgxdebug_common.c",
        "services/server/devices/rgxfwcmnctx.c",
        "services/server/devices/rgxfwdbg.c",
        "services/server/devices/rgxfwimageutils.c",
        "services/server/devices/rgxfwriscv.c",
        "services/server/devices/rgxfwtrace_strings.c",
        "services/server/devices/rgxhwperf_common.c",
        "services/server/devices/rgxlayer_impl_common.c",
        "services/server/devices/rgxmem.c",
        "services/server/devices/rgxmmuinit.c",
        "services/server/devices/rgxpower.c",
        "services/server/devices/rgxregconfig.c",
        "services/server/devices/rgxshader.c",
        "services/server/devices/rgxsyncutils.c",
        "services/server/devices/rgxtdmtransfer.c",
        "services/server/devices/rgxtimecorr.c",
        "services/server/devices/rgxtimerquery.c",
        "services/server/devices/rgxutils.c",
        "services/server/devices/rogue/rgxdebug.c",
        "services/server/devices/rogue/rgxfwutils.c",
        "services/server/devices/rogue/rgxhwperf.c",
        "services/server/devices/rogue/rgxinit.c",
        "services/server/devices/rogue/rgxlayer_impl.c",
        "services/server/devices/rogue/rgxmipsmmuinit.c",
        "services/server/devices/rogue/rgxmulticore.c",
        "services/server/devices/rogue/rgxsrvinit.c",
        "services/server/devices/rogue/rgxstartstop.c",
        "services/server/devices/rogue/rgxta3d.c",
        "services/server/devices/rogue/rgxtransfer.c",
        "services/server/env/linux/allocmem.c",
        "services/server/env/linux/event.c",
        "services/server/env/linux/fwload.c",
        "services/server/env/linux/handle_idr.c",
        "services/server/env/linux/km_apphint.c",
        "services/server/env/linux/module_common.c",
        "services/server/env/linux/osconnection_server.c",
        "services/server/env/linux/osfunc.c",
        "services/server/env/linux/osfunc_arm64.c",
        "services/server/env/linux/osmmap_stub.c",
        "services/server/env/linux/physmem_dmabuf.c",
        "services/server/env/linux/physmem_extmem_linux.c",
        "services/server/env/linux/physmem_osmem_linux.c",
        "services/server/env/linux/physmem_test.c",
        "services/server/env/linux/pmr_env.c",
        "services/server/env/linux/pmr_os.c",
        "services/server/env/linux/pvr_bridge_k.c",
        "services/server/env/linux/pvr_counting_timeline.c",
        "services/server/env/linux/pvr_debug.c",
        "services/server/env/linux/pvr_drm.c",
        "services/server/env/linux/pvr_export_fence.c",
        "services/server/env/linux/pvr_fence.c",
        "services/server/env/linux/pvr_gpufreq.c",
        "services/server/env/linux/pvr_gputrace.c",
        "services/server/env/linux/pvr_gpuwork.c",
        "services/server/env/linux/pvr_platform_drv.c",
        "services/server/env/linux/pvr_procfs.c",
        "services/server/env/linux/pvr_sw_fence.c",
        "services/server/env/linux/pvr_sync_file.c",
        "services/server/env/linux/pvr_sync_ioctl_common.c",
        "services/server/env/linux/pvr_sync_ioctl_dev.c",
        "services/server/env/linux/trace_events.c",
        "services/shared/common/devicemem.c",
        "services/shared/common/devicemem_utils.c",
        "services/shared/common/hash.c",
        "services/shared/common/mem_utils.c",
        "services/shared/common/pvrsrv_error.c",
        "services/shared/common/ra.c",
        "services/shared/common/sync.c",
        "services/shared/common/tlclient.c",
        "services/shared/common/uniq_key_splay_tree.c",
        "services/shared/devices/rogue/rgx_hwperf_table.c",
        "services/system/common/env/linux/interrupt_support.c",
        "services/system/common/env/linux/pci_support.c",
        "services/system/common/sysconfig_cmn.c",
        "services/system/rogue/axe_am62/sysconfig.c",
        "services/system/rogue/common/env/linux/dma_support.c",
        "services/system/rogue/common/vmm_type_stub.c",
    ],
)

kernel_abi(
    name = "ti_abi",
    kernel_build = "//common:ti",
    kernel_modules = [
        "//imgtech-module:ti_ext_module",
    ],
    kmi_symbol_list_add_only = True,
    kmi_enforced = True,
)

kernel_abi_dist(
    name = "ti_abi_dist",
    kernel_abi = "ti_abi",
    kernel_build_add_vmlinux = True,
    data = [
        "//common:ti",
    ],
    flat = True,
    log = "info",
)

ddk_module(
    name = "am62x_imgtech_module",
    srcs = [
        ":am62x_imgtech_module_sources",
        ":imgtech_module_headers_group",
        "include/config_kernel_am62_android.h",
    ],
    deps = [
        ":am62x_imgtech_module_headers",
        "//common:all_headers_aarch64",
    ],
    copts = [
    "-D__linux__",
    "-Wno-missing-field-initializers",
    "-Wdeclaration-after-statement",
    "-Wno-format-zero-length",
    "-Wmissing-prototypes",
    "-Wstrict-prototypes",
    "-Wno-unused-parameter",
    "-Wno-sign-compare",
    "-Wno-type-limits",
    "-Wno-error",
    "-Wno-typedef-redefinition",
    "-include", "$(location include/config_kernel_am62_android.h)"],
    out = "pvrsrvkm.ko",
    kernel_build = "//common:ti",
    visibility = ["//visibility:public"],
)

ddk_headers(
    name = "am62x_imgtech_module_headers",
    hdrs = [
        ":imgtech_module_headers_group",
    ],
    includes = [
        "generated/rogue/cache_bridge",
        "generated/rogue/cmm_bridge",
        "generated/rogue/devicememhistory_bridge",
        "generated/rogue/di_bridge",
        "generated/rogue/dmabuf_bridge",
        "generated/rogue/htbuffer_bridge",
        "generated/rogue/mmextmem_bridge",
        "generated/rogue/mm_bridge",
        "generated/rogue/pvrtl_bridge",
        "generated/rogue/rgxbreakpoint_bridge",
        "generated/rogue/rgxcmp_bridge",
        "generated/rogue/rgxfwdbg_bridge",
        "generated/rogue/rgxhwperf_bridge",
        "generated/rogue/rgxkicksync_bridge",
        "generated/rogue/rgxregconfig_bridge",
        "generated/rogue/rgxsignals_bridge",
        "generated/rogue/rgxta3d_bridge",
        "generated/rogue/rgxtimerquery_bridge",
        "generated/rogue/rgxtq2_bridge",
        "generated/rogue/rgxtq_bridge",
        "generated/rogue/srvcore_bridge",
        "generated/rogue/sync_bridge",
        "generated/rogue/synctracking_bridge",
        "hwdefs/rogue",
        "hwdefs/rogue/km",
        "include",
        "include/drm",
        "include/public",
        "include/rogue",
        "include/system",
        "kernel/drivers/staging/imgtec",
        "services/include",
        "services/include/env/linux",
        "services/include/rogue",
        "services/server/common",
        "services/server/devices",
        "services/server/devices/rogue",
        "services/server/env/linux",
        "services/server/include",
        "services/shared/common",
        "services/shared/devices/rogue",
        "services/shared/include",
        "services/system/include",
        "services/system/rogue/axe_am62",
        "services/system/rogue/include",
    ],
)

filegroup(
    name = "am62p_imgtech_module_sources",
    srcs = [
        "generated/rogue/cache_bridge/client_cache_direct_bridge.c",
        "generated/rogue/cache_bridge/server_cache_bridge.c",
        "generated/rogue/cmm_bridge/server_cmm_bridge.c",
        "generated/rogue/devicememhistory_bridge/client_devicememhistory_direct_bridge.c",
        "generated/rogue/devicememhistory_bridge/server_devicememhistory_bridge.c",
        "generated/rogue/di_bridge/server_di_bridge.c",
        "generated/rogue/dmabuf_bridge/server_dmabuf_bridge.c",
        "generated/rogue/mm_bridge/client_mm_direct_bridge.c",
        "generated/rogue/mm_bridge/server_mm_bridge.c",
        "generated/rogue/mmextmem_bridge/server_mmextmem_bridge.c",
        "generated/rogue/pvrtl_bridge/client_pvrtl_direct_bridge.c",
        "generated/rogue/pvrtl_bridge/server_pvrtl_bridge.c",
        "generated/rogue/rgxbreakpoint_bridge/server_rgxbreakpoint_bridge.c",
        "generated/rogue/rgxcmp_bridge/server_rgxcmp_bridge.c",
        "generated/rogue/rgxfwdbg_bridge/server_rgxfwdbg_bridge.c",
        "generated/rogue/rgxhwperf_bridge/server_rgxhwperf_bridge.c",
        "generated/rogue/rgxregconfig_bridge/server_rgxregconfig_bridge.c",
        "generated/rogue/rgxta3d_bridge/server_rgxta3d_bridge.c",
        "generated/rogue/rgxtimerquery_bridge/server_rgxtimerquery_bridge.c",
        "generated/rogue/rgxtq2_bridge/server_rgxtq2_bridge.c",
        "generated/rogue/rgxtq_bridge/server_rgxtq_bridge.c",
        "generated/rogue/srvcore_bridge/server_srvcore_bridge.c",
        "generated/rogue/sync_bridge/client_sync_direct_bridge.c",
        "generated/rogue/sync_bridge/server_sync_bridge.c",
        "generated/rogue/synctracking_bridge/client_synctracking_direct_bridge.c",
        "generated/rogue/synctracking_bridge/server_synctracking_bridge.c",
        "services/server/common/cache_km.c",
        "services/server/common/connection_server.c",
        "services/server/common/debug_common.c",
        "services/server/common/devicemem_heapcfg.c",
        "services/server/common/devicemem_history_server.c",
        "services/server/common/devicemem_server.c",
        "services/server/common/di_impl_brg.c",
        "services/server/common/di_server.c",
        "services/server/common/handle.c",
        "services/server/common/info_page_km.c",
        "services/server/common/lists.c",
        "services/server/common/mmu_common.c",
        "services/server/common/physheap.c",
        "services/server/common/physmem.c",
        "services/server/common/physmem_extmem.c",
        "services/server/common/physmem_hostmem.c",
        "services/server/common/physmem_lma.c",
        "services/server/common/physmem_osmem.c",
        "services/server/common/physmem_ramem.c",
        "services/server/common/pmr.c",
        "services/server/common/power.c",
        "services/server/common/process_stats.c",
        "services/server/common/pvr_notifier.c",
        "services/server/common/pvrsrv.c",
        "services/server/common/pvrsrv_bridge_init.c",
        "services/server/common/pvrsrv_pool.c",
        "services/server/common/srvcore.c",
        "services/server/common/sync_checkpoint.c",
        "services/server/common/sync_server.c",
        "services/server/common/tlintern.c",
        "services/server/common/tlserver.c",
        "services/server/common/tlstream.c",
        "services/server/common/vmm_pvz_client.c",
        "services/server/common/vmm_pvz_server.c",
        "services/server/common/vz_vmm_pvz.c",
        "services/server/common/vz_vmm_vm.c",
        "services/server/devices/rgx_bridge_init.c",
        "services/server/devices/rgxbreakpoint.c",
        "services/server/devices/rgxbvnc.c",
        "services/server/devices/rgxccb.c",
        "services/server/devices/rgxcompute.c",
        "services/server/devices/rgxdebug_common.c",
        "services/server/devices/rgxfwcmnctx.c",
        "services/server/devices/rgxfwdbg.c",
        "services/server/devices/rgxfwimageutils.c",
        "services/server/devices/rgxfwriscv.c",
        "services/server/devices/rgxfwtrace_strings.c",
        "services/server/devices/rgxhwperf_common.c",
        "services/server/devices/rgxlayer_impl_common.c",
        "services/server/devices/rgxmem.c",
        "services/server/devices/rgxmmuinit.c",
        "services/server/devices/rgxpower.c",
        "services/server/devices/rgxregconfig.c",
        "services/server/devices/rgxshader.c",
        "services/server/devices/rgxsyncutils.c",
        "services/server/devices/rgxtdmtransfer.c",
        "services/server/devices/rgxtimecorr.c",
        "services/server/devices/rgxtimerquery.c",
        "services/server/devices/rgxutils.c",
        "services/server/devices/rogue/rgxdebug.c",
        "services/server/devices/rogue/rgxfwutils.c",
        "services/server/devices/rogue/rgxhwperf.c",
        "services/server/devices/rogue/rgxinit.c",
        "services/server/devices/rogue/rgxlayer_impl.c",
        "services/server/devices/rogue/rgxmipsmmuinit.c",
        "services/server/devices/rogue/rgxmulticore.c",
        "services/server/devices/rogue/rgxsrvinit.c",
        "services/server/devices/rogue/rgxstartstop.c",
        "services/server/devices/rogue/rgxta3d.c",
        "services/server/devices/rogue/rgxtransfer.c",
        "services/server/env/linux/allocmem.c",
        "services/server/env/linux/event.c",
        "services/server/env/linux/fwload.c",
        "services/server/env/linux/handle_idr.c",
        "services/server/env/linux/km_apphint.c",
        "services/server/env/linux/module_common.c",
        "services/server/env/linux/osconnection_server.c",
        "services/server/env/linux/osfunc.c",
        "services/server/env/linux/osfunc_arm64.c",
        "services/server/env/linux/osmmap_stub.c",
        "services/server/env/linux/physmem_dmabuf.c",
        "services/server/env/linux/physmem_extmem_linux.c",
        "services/server/env/linux/physmem_osmem_linux.c",
        "services/server/env/linux/physmem_test.c",
        "services/server/env/linux/pmr_env.c",
        "services/server/env/linux/pmr_os.c",
        "services/server/env/linux/pvr_bridge_k.c",
        "services/server/env/linux/pvr_counting_timeline.c",
        "services/server/env/linux/pvr_debug.c",
        "services/server/env/linux/pvr_drm.c",
        "services/server/env/linux/pvr_export_fence.c",
        "services/server/env/linux/pvr_fence.c",
        "services/server/env/linux/pvr_gpufreq.c",
        "services/server/env/linux/pvr_gputrace.c",
        "services/server/env/linux/pvr_gpuwork.c",
        "services/server/env/linux/pvr_platform_drv.c",
        "services/server/env/linux/pvr_procfs.c",
        "services/server/env/linux/pvr_sw_fence.c",
        "services/server/env/linux/pvr_sync_file.c",
        "services/server/env/linux/pvr_sync_ioctl_common.c",
        "services/server/env/linux/pvr_sync_ioctl_dev.c",
        "services/server/env/linux/trace_events.c",
        "services/shared/common/devicemem.c",
        "services/shared/common/devicemem_utils.c",
        "services/shared/common/hash.c",
        "services/shared/common/mem_utils.c",
        "services/shared/common/pvrsrv_error.c",
        "services/shared/common/ra.c",
        "services/shared/common/sync.c",
        "services/shared/common/tlclient.c",
        "services/shared/common/uniq_key_splay_tree.c",
        "services/shared/devices/rogue/rgx_hwperf_table.c",
        "services/system/common/env/linux/interrupt_support.c",
        "services/system/common/env/linux/pci_support.c",
        "services/system/common/sysconfig_cmn.c",
        "services/system/rogue/bxs_am62p/sysconfig.c",
        "services/system/rogue/common/env/linux/dma_support.c",
        "services/system/rogue/common/vmm_type_stub.c",
    ],
)

ddk_module(
    name = "am62p_imgtech_module",
    srcs = [
        ":am62p_imgtech_module_sources",
        ":imgtech_module_headers_group",
        "include/config_kernel_am62p_android.h",
    ],
    deps = [
        ":am62p_imgtech_module_headers",
        "//common:all_headers_aarch64",
    ],
    copts = [
    "-D__linux__",
    "-Wno-missing-field-initializers",
    "-Wdeclaration-after-statement",
    "-Wno-format-zero-length",
    "-Wmissing-prototypes",
    "-Wstrict-prototypes",
    "-Wno-unused-parameter",
    "-Wno-sign-compare",
    "-Wno-type-limits",
    "-Wno-error",
    "-Wno-typedef-redefinition",
    "-include", "$(location include/config_kernel_am62p_android.h)"],
    out = "pvrsrvkm_am62p.ko",
    kernel_build = "//common:ti",
    visibility = ["//visibility:public"],
)

ddk_headers(
    name = "am62p_imgtech_module_headers",
    hdrs = [
        ":imgtech_module_headers_group",
    ],
    includes = [
        "generated/rogue/cache_bridge",
        "generated/rogue/cmm_bridge",
        "generated/rogue/devicememhistory_bridge",
        "generated/rogue/di_bridge",
        "generated/rogue/dmabuf_bridge",
        "generated/rogue/mmextmem_bridge",
        "generated/rogue/mm_bridge",
        "generated/rogue/pvrtl_bridge",
        "generated/rogue/rgxbreakpoint_bridge",
        "generated/rogue/rgxcmp_bridge",
        "generated/rogue/rgxfwdbg_bridge",
        "generated/rogue/rgxhwperf_bridge",
        "generated/rogue/rgxkicksync_bridge",
        "generated/rogue/rgxregconfig_bridge",
        "generated/rogue/rgxsignals_bridge",
        "generated/rogue/rgxta3d_bridge",
        "generated/rogue/rgxtimerquery_bridge",
        "generated/rogue/rgxtq2_bridge",
        "generated/rogue/rgxtq_bridge",
        "generated/rogue/srvcore_bridge",
        "generated/rogue/sync_bridge",
        "generated/rogue/synctracking_bridge",
        "hwdefs/rogue",
        "hwdefs/rogue/km",
        "include",
        "include/drm",
        "include/public",
        "include/rogue",
        "include/system",
        "kernel/drivers/staging/imgtec",
        "services/include",
        "services/include/env/linux",
        "services/include/rogue",
        "services/server/common",
        "services/server/devices",
        "services/server/devices/rogue",
        "services/server/env/linux",
        "services/server/include",
        "services/shared/common",
        "services/shared/devices/rogue",
        "services/shared/include",
        "services/system/include",
        "services/system/rogue/bxs_am62p",
        "services/system/rogue/include",
    ],
)

kernel_module_group(
    name = "ti_ext_modules",
    srcs = [
        # do not sort
        ":am62x_imgtech_module",
        ":am62p_imgtech_module",
    ],
    visibility = ["//visibility:public"],
)