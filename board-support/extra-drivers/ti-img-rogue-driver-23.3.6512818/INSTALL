Rogue Embedded Systems DDK for the Linux kernel.
Copyright (C) Imagination Technologies Ltd. All rights reserved.
======================================================================

This file covers how to build and install the Imagination Technologies
Rogue DDK for the Linux kernel.  For full details, see the relevant platform
guide.


Build System Environment Variables
-------------------------------------------

The Rogue DDK Build scripts depend on a number of environment variables
being set-up before compilation or installation of DDK software can
commence:

$DISCIMAGE
The DDK Build scripts install files to the location specified by the
DISCIMAGE environment variable.  To do so, they need to know where the
target system image resides:
$ export DISCIMAGE=/path/to/filesystem
If you are building on the target system, you can set this to '/'.

$KERNELDIR
When building the Rogue DDK kernel module, the build needs access
to the headers of the Linux kernel.
If you are building on the target machine, you can set this as follows:
$ export KERNELDIR=/usr/src/linux-headers-`uname -r`

$CROSS_COMPILE
If you intend on targeting a platform that is different from your build
machine (e.g., if you are compiling on an x86 but targeting ARM) you need
to set the CROSS_COMPILE variable so that the build system uses the correct
compiler. For example:
$ export CROSS_COMPILE=arm-linux-gnueabihf-


Build and Install Instructions
-------------------------------------------

The Rogue DDK configures different target builds within directories under
build/linux/.

The most interesting build targets are:

        build   Makes everything
        clobber Removes all binaries for all builds as well.
        install Runs the install script generated by the build.

The following variables may be set on the command line to influence a build.

        BUILD   The type of build being performed.
                Alternatives are release, timing or debug.

To build for, change to the appropriate target directory, for example:
$ cd rogue_km/build/linux/<platform>
$ make BUILD=debug
$ sudo -E make install BUILD=debug
