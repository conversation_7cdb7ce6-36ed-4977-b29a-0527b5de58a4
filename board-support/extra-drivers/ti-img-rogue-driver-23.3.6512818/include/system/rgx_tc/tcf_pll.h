/*************************************************************************/ /*!
@Title          Test Chip Framework PDP register definitions
@Copyright      Copyright (c) Imagination Technologies Ltd. All Rights Reserved
@Description    Autogenerated C -- do not edit
                Generated from tcf_pll.def
@License        Dual MIT/GPLv2

The contents of this file are subject to the MIT license as set out below.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

Alternatively, the contents of this file may be used under the terms of
the GNU General Public License Version 2 ("GPL") in which case the provisions
of GPL are applicable instead of those above.

If you wish to allow use of your version of this file only under the terms of
GPL, and not to allow others to use your version of this file under the terms
of the MIT license, indicate your decision by deleting the provisions above
and replace them with the notice and other provisions required by GPL as set
out in the file called "GPL-COPYING" included in this distribution. If you do
not delete the provisions above, a recipient may use your version of this file
under the terms of either the MIT license or GPL.

This License is also included in this distribution in the file called
"MIT-COPYING".

EXCEPT AS OTHERWISE STATED IN A NEGOTIATED AGREEMENT: (A) THE SOFTWARE IS
PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
PURPOSE AND NONINFRINGEMENT; AND (B) IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ /**************************************************************************/

#if !defined(_TCF_PLL_H_)
#define _TCF_PLL_H_

/*
	Register PLL_DDR2_CLK0
*/
#define TCF_PLL_PLL_DDR2_CLK0 0x0000
#define DDR2_PLL_CLK0_PHS_MASK 0x00300000U
#define DDR2_PLL_CLK0_PHS_SHIFT 20
#define DDR2_PLL_CLK0_PHS_SIGNED 0

#define DDR2_PLL_CLK0_MS_MASK 0x00030000U
#define DDR2_PLL_CLK0_MS_SHIFT 16
#define DDR2_PLL_CLK0_MS_SIGNED 0

#define DDR2_PLL_CLK0_FREQ_MASK 0x000001FFU
#define DDR2_PLL_CLK0_FREQ_SHIFT 0
#define DDR2_PLL_CLK0_FREQ_SIGNED 0

/*
	Register PLL_DDR2_CLK1TO5
*/
#define TCF_PLL_PLL_DDR2_CLK1TO5 0x0008
#define DDR2_PLL_CLK1TO5_PHS_MASK 0x3FF00000U
#define DDR2_PLL_CLK1TO5_PHS_SHIFT 20
#define DDR2_PLL_CLK1TO5_PHS_SIGNED 0

#define DDR2_PLL_CLK1TO5_MS_MASK 0x000FFC00U
#define DDR2_PLL_CLK1TO5_MS_SHIFT 10
#define DDR2_PLL_CLK1TO5_MS_SIGNED 0

#define DDR2_PLL_CLK1TO5_FREQ_MASK 0x000003FFU
#define DDR2_PLL_CLK1TO5_FREQ_SHIFT 0
#define DDR2_PLL_CLK1TO5_FREQ_SIGNED 0

/*
	Register PLL_DDR2_DRP_GO
*/
#define TCF_PLL_PLL_DDR2_DRP_GO 0x0010
#define PLL_DDR2_DRP_GO_MASK 0x00000001U
#define PLL_DDR2_DRP_GO_SHIFT 0
#define PLL_DDR2_DRP_GO_SIGNED 0

/*
	Register PLL_PDP_CLK0
*/
#define TCF_PLL_PLL_PDP_CLK0 0x0018
#define PDP_PLL_CLK0_PHS_MASK 0x00300000U
#define PDP_PLL_CLK0_PHS_SHIFT 20
#define PDP_PLL_CLK0_PHS_SIGNED 0

#define PDP_PLL_CLK0_MS_MASK 0x00030000U
#define PDP_PLL_CLK0_MS_SHIFT 16
#define PDP_PLL_CLK0_MS_SIGNED 0

#define PDP_PLL_CLK0_FREQ_MASK 0x000001FFU
#define PDP_PLL_CLK0_FREQ_SHIFT 0
#define PDP_PLL_CLK0_FREQ_SIGNED 0

/*
	Register PLL_PDP_CLK1TO5
*/
#define TCF_PLL_PLL_PDP_CLK1TO5 0x0020
#define PDP_PLL_CLK1TO5_PHS_MASK 0x3FF00000U
#define PDP_PLL_CLK1TO5_PHS_SHIFT 20
#define PDP_PLL_CLK1TO5_PHS_SIGNED 0

#define PDP_PLL_CLK1TO5_MS_MASK 0x000FFC00U
#define PDP_PLL_CLK1TO5_MS_SHIFT 10
#define PDP_PLL_CLK1TO5_MS_SIGNED 0

#define PDP_PLL_CLK1TO5_FREQ_MASK 0x000003FFU
#define PDP_PLL_CLK1TO5_FREQ_SHIFT 0
#define PDP_PLL_CLK1TO5_FREQ_SIGNED 0

/*
	Register PLL_PDP_DRP_GO
*/
#define TCF_PLL_PLL_PDP_DRP_GO 0x0028
#define PLL_PDP_DRP_GO_MASK 0x00000001U
#define PLL_PDP_DRP_GO_SHIFT 0
#define PLL_PDP_DRP_GO_SIGNED 0

/*
	Register PLL_PDP2_CLK0
*/
#define TCF_PLL_PLL_PDP2_CLK0 0x0030
#define PDP2_PLL_CLK0_PHS_MASK 0x00300000U
#define PDP2_PLL_CLK0_PHS_SHIFT 20
#define PDP2_PLL_CLK0_PHS_SIGNED 0

#define PDP2_PLL_CLK0_MS_MASK 0x00030000U
#define PDP2_PLL_CLK0_MS_SHIFT 16
#define PDP2_PLL_CLK0_MS_SIGNED 0

#define PDP2_PLL_CLK0_FREQ_MASK 0x000001FFU
#define PDP2_PLL_CLK0_FREQ_SHIFT 0
#define PDP2_PLL_CLK0_FREQ_SIGNED 0

/*
	Register PLL_PDP2_CLK1TO5
*/
#define TCF_PLL_PLL_PDP2_CLK1TO5 0x0038
#define PDP2_PLL_CLK1TO5_PHS_MASK 0x3FF00000U
#define PDP2_PLL_CLK1TO5_PHS_SHIFT 20
#define PDP2_PLL_CLK1TO5_PHS_SIGNED 0

#define PDP2_PLL_CLK1TO5_MS_MASK 0x000FFC00U
#define PDP2_PLL_CLK1TO5_MS_SHIFT 10
#define PDP2_PLL_CLK1TO5_MS_SIGNED 0

#define PDP2_PLL_CLK1TO5_FREQ_MASK 0x000003FFU
#define PDP2_PLL_CLK1TO5_FREQ_SHIFT 0
#define PDP2_PLL_CLK1TO5_FREQ_SIGNED 0

/*
	Register PLL_PDP2_DRP_GO
*/
#define TCF_PLL_PLL_PDP2_DRP_GO 0x0040
#define PLL_PDP2_DRP_GO_MASK 0x00000001U
#define PLL_PDP2_DRP_GO_SHIFT 0
#define PLL_PDP2_DRP_GO_SIGNED 0

/*
	Register PLL_CORE_CLK0
*/
#define TCF_PLL_PLL_CORE_CLK0 0x0048
#define CORE_PLL_CLK0_PHS_MASK 0x00300000U
#define CORE_PLL_CLK0_PHS_SHIFT 20
#define CORE_PLL_CLK0_PHS_SIGNED 0

#define CORE_PLL_CLK0_MS_MASK 0x00030000U
#define CORE_PLL_CLK0_MS_SHIFT 16
#define CORE_PLL_CLK0_MS_SIGNED 0

#define CORE_PLL_CLK0_FREQ_MASK 0x000001FFU
#define CORE_PLL_CLK0_FREQ_SHIFT 0
#define CORE_PLL_CLK0_FREQ_SIGNED 0

/*
	Register PLL_CORE_CLK1TO5
*/
#define TCF_PLL_PLL_CORE_CLK1TO5 0x0050
#define CORE_PLL_CLK1TO5_PHS_MASK 0x3FF00000U
#define CORE_PLL_CLK1TO5_PHS_SHIFT 20
#define CORE_PLL_CLK1TO5_PHS_SIGNED 0

#define CORE_PLL_CLK1TO5_MS_MASK 0x000FFC00U
#define CORE_PLL_CLK1TO5_MS_SHIFT 10
#define CORE_PLL_CLK1TO5_MS_SIGNED 0

#define CORE_PLL_CLK1TO5_FREQ_MASK 0x000003FFU
#define CORE_PLL_CLK1TO5_FREQ_SHIFT 0
#define CORE_PLL_CLK1TO5_FREQ_SIGNED 0

/*
	Register PLL_CORE_DRP_GO
*/
#define TCF_PLL_PLL_CORE_DRP_GO 0x0058
#define PLL_CORE_DRP_GO_MASK 0x00000001U
#define PLL_CORE_DRP_GO_SHIFT 0
#define PLL_CORE_DRP_GO_SIGNED 0

/*
	Register PLL_SYSIF_CLK0
*/
#define TCF_PLL_PLL_SYSIF_CLK0 0x0060
#define SYSIF_PLL_CLK0_PHS_MASK 0x00300000U
#define SYSIF_PLL_CLK0_PHS_SHIFT 20
#define SYSIF_PLL_CLK0_PHS_SIGNED 0

#define SYSIF_PLL_CLK0_MS_MASK 0x00030000U
#define SYSIF_PLL_CLK0_MS_SHIFT 16
#define SYSIF_PLL_CLK0_MS_SIGNED 0

#define SYSIF_PLL_CLK0_FREQ_MASK 0x000001FFU
#define SYSIF_PLL_CLK0_FREQ_SHIFT 0
#define SYSIF_PLL_CLK0_FREQ_SIGNED 0

/*
	Register PLL_SYSIF_CLK1TO5
*/
#define TCF_PLL_PLL_SYSIF_CLK1TO5 0x0068
#define SYSIF_PLL_CLK1TO5_PHS_MASK 0x3FF00000U
#define SYSIF_PLL_CLK1TO5_PHS_SHIFT 20
#define SYSIF_PLL_CLK1TO5_PHS_SIGNED 0

#define SYSIF_PLL_CLK1TO5_MS_MASK 0x000FFC00U
#define SYSIF_PLL_CLK1TO5_MS_SHIFT 10
#define SYSIF_PLL_CLK1TO5_MS_SIGNED 0

#define SYSIF_PLL_CLK1TO5_FREQ_MASK 0x000003FFU
#define SYSIF_PLL_CLK1TO5_FREQ_SHIFT 0
#define SYSIF_PLL_CLK1TO5_FREQ_SIGNED 0

/*
	Register PLL_SYS_DRP_GO
*/
#define TCF_PLL_PLL_SYS_DRP_GO 0x0070
#define PLL_SYS_DRP_GO_MASK 0x00000001U
#define PLL_SYS_DRP_GO_SHIFT 0
#define PLL_SYS_DRP_GO_SIGNED 0

/*
	Register PLL_MEMIF_CLK0
*/
#define TCF_PLL_PLL_MEMIF_CLK0 0x0078
#define MEMIF_PLL_CLK0_PHS_MASK 0x00300000U
#define MEMIF_PLL_CLK0_PHS_SHIFT 20
#define MEMIF_PLL_CLK0_PHS_SIGNED 0

#define MEMIF_PLL_CLK0_MS_MASK 0x00030000U
#define MEMIF_PLL_CLK0_MS_SHIFT 16
#define MEMIF_PLL_CLK0_MS_SIGNED 0

#define MEMIF_PLL_CLK0_FREQ_MASK 0x000001FFU
#define MEMIF_PLL_CLK0_FREQ_SHIFT 0
#define MEMIF_PLL_CLK0_FREQ_SIGNED 0

/*
	Register PLL_MEMIF_CLK1TO5
*/
#define TCF_PLL_PLL_MEMIF_CLK1TO5 0x0080
#define MEMIF_PLL_CLK1TO5_PHS_MASK 0x3FF00000U
#define MEMIF_PLL_CLK1TO5_PHS_SHIFT 20
#define MEMIF_PLL_CLK1TO5_PHS_SIGNED 0

#define MEMIF_PLL_CLK1TO5_MS_MASK 0x000FFC00U
#define MEMIF_PLL_CLK1TO5_MS_SHIFT 10
#define MEMIF_PLL_CLK1TO5_MS_SIGNED 0

#define MEMIF_PLL_CLK1TO5_FREQ_MASK 0x000003FFU
#define MEMIF_PLL_CLK1TO5_FREQ_SHIFT 0
#define MEMIF_PLL_CLK1TO5_FREQ_SIGNED 0

/*
	Register PLL_MEM_DRP_GO
*/
#define TCF_PLL_PLL_MEM_DRP_GO 0x0088
#define PLL_MEM_DRP_GO_MASK 0x00000001U
#define PLL_MEM_DRP_GO_SHIFT 0
#define PLL_MEM_DRP_GO_SIGNED 0

/*
	Register PLL_ALL_DRP_GO
*/
#define TCF_PLL_PLL_ALL_DRP_GO 0x0090
#define PLL_ALL_DRP_GO_MASK 0x00000001U
#define PLL_ALL_DRP_GO_SHIFT 0
#define PLL_ALL_DRP_GO_SIGNED 0

/*
	Register PLL_DRP_STATUS
*/
#define TCF_PLL_PLL_DRP_STATUS 0x0098
#define PLL_LOCKS_MASK 0x00003F00U
#define PLL_LOCKS_SHIFT 8
#define PLL_LOCKS_SIGNED 0

#define PLL_DRP_GOOD_MASK 0x0000003FU
#define PLL_DRP_GOOD_SHIFT 0
#define PLL_DRP_GOOD_SIGNED 0

#endif /* !defined(_TCF_PLL_H_) */

/*****************************************************************************
 End of file (tcf_pll.h)
*****************************************************************************/
