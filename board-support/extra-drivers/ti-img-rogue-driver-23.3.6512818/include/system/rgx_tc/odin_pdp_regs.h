/*************************************************************************/ /*!
@Copyright      Copyright (c) Imagination Technologies Ltd. All Rights Reserved
@License        Dual MIT/GPLv2

The contents of this file are subject to the MIT license as set out below.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

Alternatively, the contents of this file may be used under the terms of
the GNU General Public License Version 2 ("GPL") in which case the provisions
of GPL are applicable instead of those above.

If you wish to allow use of your version of this file only under the terms of
GPL, and not to allow others to use your version of this file under the terms
of the MIT license, indicate your decision by deleting the provisions above
and replace them with the notice and other provisions required by GPL as set
out in the file called "GPL-COPYING" included in this distribution. If you do
not delete the provisions above, a recipient may use your version of this file
under the terms of either the MIT license or GPL.

This License is also included in this distribution in the file called
"MIT-COPYING".

EXCEPT AS OTHERWISE STATED IN A NEGOTIATED AGREEMENT: (A) THE SOFTWARE IS
PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
PURPOSE AND NONINFRINGEMENT; AND (B) IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ /**************************************************************************/

/* tab size 4 */

#ifndef ODN_PDP_REGS_H
#define ODN_PDP_REGS_H

/* Odin-PDP hardware register definitions */

#define ODN_PDP_GRPH1SURF_OFFSET (0x0000)

/* PDP, GRPH1SURF, GRPH1PIXFMT
*/
#define ODN_PDP_GRPH1SURF_GRPH1PIXFMT_MASK (0xF8000000)
#define ODN_PDP_GRPH1SURF_GRPH1PIXFMT_LSBMASK (0x0000001F)
#define ODN_PDP_GRPH1SURF_GRPH1PIXFMT_SHIFT (27)
#define ODN_PDP_GRPH1SURF_GRPH1PIXFMT_LENGTH (5)
#define ODN_PDP_GRPH1SURF_GRPH1PIXFMT_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1SURF, GRPH1USEGAMMA
*/
#define ODN_PDP_GRPH1SURF_GRPH1USEGAMMA_MASK (0x04000000)
#define ODN_PDP_GRPH1SURF_GRPH1USEGAMMA_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1SURF_GRPH1USEGAMMA_SHIFT (26)
#define ODN_PDP_GRPH1SURF_GRPH1USEGAMMA_LENGTH (1)
#define ODN_PDP_GRPH1SURF_GRPH1USEGAMMA_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1SURF, GRPH1USECSC
*/
#define ODN_PDP_GRPH1SURF_GRPH1USECSC_MASK (0x02000000)
#define ODN_PDP_GRPH1SURF_GRPH1USECSC_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1SURF_GRPH1USECSC_SHIFT (25)
#define ODN_PDP_GRPH1SURF_GRPH1USECSC_LENGTH (1)
#define ODN_PDP_GRPH1SURF_GRPH1USECSC_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1SURF, GRPH1LUTRWCHOICE
*/
#define ODN_PDP_GRPH1SURF_GRPH1LUTRWCHOICE_MASK (0x01000000)
#define ODN_PDP_GRPH1SURF_GRPH1LUTRWCHOICE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1SURF_GRPH1LUTRWCHOICE_SHIFT (24)
#define ODN_PDP_GRPH1SURF_GRPH1LUTRWCHOICE_LENGTH (1)
#define ODN_PDP_GRPH1SURF_GRPH1LUTRWCHOICE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1SURF, GRPH1USELUT
*/
#define ODN_PDP_GRPH1SURF_GRPH1USELUT_MASK (0x00800000)
#define ODN_PDP_GRPH1SURF_GRPH1USELUT_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1SURF_GRPH1USELUT_SHIFT (23)
#define ODN_PDP_GRPH1SURF_GRPH1USELUT_LENGTH (1)
#define ODN_PDP_GRPH1SURF_GRPH1USELUT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2SURF_OFFSET (0x0004)

/* PDP, GRPH2SURF, GRPH2PIXFMT
*/
#define ODN_PDP_GRPH2SURF_GRPH2PIXFMT_MASK (0xF8000000)
#define ODN_PDP_GRPH2SURF_GRPH2PIXFMT_LSBMASK (0x0000001F)
#define ODN_PDP_GRPH2SURF_GRPH2PIXFMT_SHIFT (27)
#define ODN_PDP_GRPH2SURF_GRPH2PIXFMT_LENGTH (5)
#define ODN_PDP_GRPH2SURF_GRPH2PIXFMT_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2SURF, GRPH2USEGAMMA
*/
#define ODN_PDP_GRPH2SURF_GRPH2USEGAMMA_MASK (0x04000000)
#define ODN_PDP_GRPH2SURF_GRPH2USEGAMMA_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2SURF_GRPH2USEGAMMA_SHIFT (26)
#define ODN_PDP_GRPH2SURF_GRPH2USEGAMMA_LENGTH (1)
#define ODN_PDP_GRPH2SURF_GRPH2USEGAMMA_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2SURF, GRPH2USECSC
*/
#define ODN_PDP_GRPH2SURF_GRPH2USECSC_MASK (0x02000000)
#define ODN_PDP_GRPH2SURF_GRPH2USECSC_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2SURF_GRPH2USECSC_SHIFT (25)
#define ODN_PDP_GRPH2SURF_GRPH2USECSC_LENGTH (1)
#define ODN_PDP_GRPH2SURF_GRPH2USECSC_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2SURF, GRPH2LUTRWCHOICE
*/
#define ODN_PDP_GRPH2SURF_GRPH2LUTRWCHOICE_MASK (0x01000000)
#define ODN_PDP_GRPH2SURF_GRPH2LUTRWCHOICE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2SURF_GRPH2LUTRWCHOICE_SHIFT (24)
#define ODN_PDP_GRPH2SURF_GRPH2LUTRWCHOICE_LENGTH (1)
#define ODN_PDP_GRPH2SURF_GRPH2LUTRWCHOICE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2SURF, GRPH2USELUT
*/
#define ODN_PDP_GRPH2SURF_GRPH2USELUT_MASK (0x00800000)
#define ODN_PDP_GRPH2SURF_GRPH2USELUT_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2SURF_GRPH2USELUT_SHIFT (23)
#define ODN_PDP_GRPH2SURF_GRPH2USELUT_LENGTH (1)
#define ODN_PDP_GRPH2SURF_GRPH2USELUT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3SURF_OFFSET (0x0008)

/* PDP, GRPH3SURF, GRPH3PIXFMT
*/
#define ODN_PDP_GRPH3SURF_GRPH3PIXFMT_MASK (0xF8000000)
#define ODN_PDP_GRPH3SURF_GRPH3PIXFMT_LSBMASK (0x0000001F)
#define ODN_PDP_GRPH3SURF_GRPH3PIXFMT_SHIFT (27)
#define ODN_PDP_GRPH3SURF_GRPH3PIXFMT_LENGTH (5)
#define ODN_PDP_GRPH3SURF_GRPH3PIXFMT_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3SURF, GRPH3USEGAMMA
*/
#define ODN_PDP_GRPH3SURF_GRPH3USEGAMMA_MASK (0x04000000)
#define ODN_PDP_GRPH3SURF_GRPH3USEGAMMA_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3SURF_GRPH3USEGAMMA_SHIFT (26)
#define ODN_PDP_GRPH3SURF_GRPH3USEGAMMA_LENGTH (1)
#define ODN_PDP_GRPH3SURF_GRPH3USEGAMMA_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3SURF, GRPH3USECSC
*/
#define ODN_PDP_GRPH3SURF_GRPH3USECSC_MASK (0x02000000)
#define ODN_PDP_GRPH3SURF_GRPH3USECSC_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3SURF_GRPH3USECSC_SHIFT (25)
#define ODN_PDP_GRPH3SURF_GRPH3USECSC_LENGTH (1)
#define ODN_PDP_GRPH3SURF_GRPH3USECSC_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3SURF, GRPH3LUTRWCHOICE
*/
#define ODN_PDP_GRPH3SURF_GRPH3LUTRWCHOICE_MASK (0x01000000)
#define ODN_PDP_GRPH3SURF_GRPH3LUTRWCHOICE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3SURF_GRPH3LUTRWCHOICE_SHIFT (24)
#define ODN_PDP_GRPH3SURF_GRPH3LUTRWCHOICE_LENGTH (1)
#define ODN_PDP_GRPH3SURF_GRPH3LUTRWCHOICE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3SURF, GRPH3USELUT
*/
#define ODN_PDP_GRPH3SURF_GRPH3USELUT_MASK (0x00800000)
#define ODN_PDP_GRPH3SURF_GRPH3USELUT_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3SURF_GRPH3USELUT_SHIFT (23)
#define ODN_PDP_GRPH3SURF_GRPH3USELUT_LENGTH (1)
#define ODN_PDP_GRPH3SURF_GRPH3USELUT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4SURF_OFFSET (0x000C)

/* PDP, GRPH4SURF, GRPH4PIXFMT
*/
#define ODN_PDP_GRPH4SURF_GRPH4PIXFMT_MASK (0xF8000000)
#define ODN_PDP_GRPH4SURF_GRPH4PIXFMT_LSBMASK (0x0000001F)
#define ODN_PDP_GRPH4SURF_GRPH4PIXFMT_SHIFT (27)
#define ODN_PDP_GRPH4SURF_GRPH4PIXFMT_LENGTH (5)
#define ODN_PDP_GRPH4SURF_GRPH4PIXFMT_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4SURF, GRPH4USEGAMMA
*/
#define ODN_PDP_GRPH4SURF_GRPH4USEGAMMA_MASK (0x04000000)
#define ODN_PDP_GRPH4SURF_GRPH4USEGAMMA_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4SURF_GRPH4USEGAMMA_SHIFT (26)
#define ODN_PDP_GRPH4SURF_GRPH4USEGAMMA_LENGTH (1)
#define ODN_PDP_GRPH4SURF_GRPH4USEGAMMA_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4SURF, GRPH4USECSC
*/
#define ODN_PDP_GRPH4SURF_GRPH4USECSC_MASK (0x02000000)
#define ODN_PDP_GRPH4SURF_GRPH4USECSC_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4SURF_GRPH4USECSC_SHIFT (25)
#define ODN_PDP_GRPH4SURF_GRPH4USECSC_LENGTH (1)
#define ODN_PDP_GRPH4SURF_GRPH4USECSC_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4SURF, GRPH4LUTRWCHOICE
*/
#define ODN_PDP_GRPH4SURF_GRPH4LUTRWCHOICE_MASK (0x01000000)
#define ODN_PDP_GRPH4SURF_GRPH4LUTRWCHOICE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4SURF_GRPH4LUTRWCHOICE_SHIFT (24)
#define ODN_PDP_GRPH4SURF_GRPH4LUTRWCHOICE_LENGTH (1)
#define ODN_PDP_GRPH4SURF_GRPH4LUTRWCHOICE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4SURF, GRPH4USELUT
*/
#define ODN_PDP_GRPH4SURF_GRPH4USELUT_MASK (0x00800000)
#define ODN_PDP_GRPH4SURF_GRPH4USELUT_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4SURF_GRPH4USELUT_SHIFT (23)
#define ODN_PDP_GRPH4SURF_GRPH4USELUT_LENGTH (1)
#define ODN_PDP_GRPH4SURF_GRPH4USELUT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1SURF_OFFSET (0x0010)

/* PDP, VID1SURF, VID1PIXFMT
*/
#define ODN_PDP_VID1SURF_VID1PIXFMT_MASK (0xF8000000)
#define ODN_PDP_VID1SURF_VID1PIXFMT_LSBMASK (0x0000001F)
#define ODN_PDP_VID1SURF_VID1PIXFMT_SHIFT (27)
#define ODN_PDP_VID1SURF_VID1PIXFMT_LENGTH (5)
#define ODN_PDP_VID1SURF_VID1PIXFMT_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SURF, VID1USEGAMMA
*/
#define ODN_PDP_VID1SURF_VID1USEGAMMA_MASK (0x04000000)
#define ODN_PDP_VID1SURF_VID1USEGAMMA_LSBMASK (0x00000001)
#define ODN_PDP_VID1SURF_VID1USEGAMMA_SHIFT (26)
#define ODN_PDP_VID1SURF_VID1USEGAMMA_LENGTH (1)
#define ODN_PDP_VID1SURF_VID1USEGAMMA_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SURF, VID1USECSC
*/
#define ODN_PDP_VID1SURF_VID1USECSC_MASK (0x02000000)
#define ODN_PDP_VID1SURF_VID1USECSC_LSBMASK (0x00000001)
#define ODN_PDP_VID1SURF_VID1USECSC_SHIFT (25)
#define ODN_PDP_VID1SURF_VID1USECSC_LENGTH (1)
#define ODN_PDP_VID1SURF_VID1USECSC_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SURF, VID1USEI2P
*/
#define ODN_PDP_VID1SURF_VID1USEI2P_MASK (0x01000000)
#define ODN_PDP_VID1SURF_VID1USEI2P_LSBMASK (0x00000001)
#define ODN_PDP_VID1SURF_VID1USEI2P_SHIFT (24)
#define ODN_PDP_VID1SURF_VID1USEI2P_LENGTH (1)
#define ODN_PDP_VID1SURF_VID1USEI2P_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SURF, VID1COSITED
*/
#define ODN_PDP_VID1SURF_VID1COSITED_MASK (0x00800000)
#define ODN_PDP_VID1SURF_VID1COSITED_LSBMASK (0x00000001)
#define ODN_PDP_VID1SURF_VID1COSITED_SHIFT (23)
#define ODN_PDP_VID1SURF_VID1COSITED_LENGTH (1)
#define ODN_PDP_VID1SURF_VID1COSITED_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SURF, VID1USEHQCD
*/
#define ODN_PDP_VID1SURF_VID1USEHQCD_MASK (0x00400000)
#define ODN_PDP_VID1SURF_VID1USEHQCD_LSBMASK (0x00000001)
#define ODN_PDP_VID1SURF_VID1USEHQCD_SHIFT (22)
#define ODN_PDP_VID1SURF_VID1USEHQCD_LENGTH (1)
#define ODN_PDP_VID1SURF_VID1USEHQCD_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SURF, VID1USEINSTREAM
*/
#define ODN_PDP_VID1SURF_VID1USEINSTREAM_MASK (0x00200000)
#define ODN_PDP_VID1SURF_VID1USEINSTREAM_LSBMASK (0x00000001)
#define ODN_PDP_VID1SURF_VID1USEINSTREAM_SHIFT (21)
#define ODN_PDP_VID1SURF_VID1USEINSTREAM_LENGTH (1)
#define ODN_PDP_VID1SURF_VID1USEINSTREAM_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2SURF_OFFSET (0x0014)

/* PDP, VID2SURF, VID2PIXFMT
*/
#define ODN_PDP_VID2SURF_VID2PIXFMT_MASK (0xF8000000)
#define ODN_PDP_VID2SURF_VID2PIXFMT_LSBMASK (0x0000001F)
#define ODN_PDP_VID2SURF_VID2PIXFMT_SHIFT (27)
#define ODN_PDP_VID2SURF_VID2PIXFMT_LENGTH (5)
#define ODN_PDP_VID2SURF_VID2PIXFMT_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SURF, VID2COSITED
*/
#define ODN_PDP_VID2SURF_VID2COSITED_MASK (0x00800000)
#define ODN_PDP_VID2SURF_VID2COSITED_LSBMASK (0x00000001)
#define ODN_PDP_VID2SURF_VID2COSITED_SHIFT (23)
#define ODN_PDP_VID2SURF_VID2COSITED_LENGTH (1)
#define ODN_PDP_VID2SURF_VID2COSITED_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SURF, VID2USEGAMMA
*/
#define ODN_PDP_VID2SURF_VID2USEGAMMA_MASK (0x04000000)
#define ODN_PDP_VID2SURF_VID2USEGAMMA_LSBMASK (0x00000001)
#define ODN_PDP_VID2SURF_VID2USEGAMMA_SHIFT (26)
#define ODN_PDP_VID2SURF_VID2USEGAMMA_LENGTH (1)
#define ODN_PDP_VID2SURF_VID2USEGAMMA_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SURF, VID2USECSC
*/
#define ODN_PDP_VID2SURF_VID2USECSC_MASK (0x02000000)
#define ODN_PDP_VID2SURF_VID2USECSC_LSBMASK (0x00000001)
#define ODN_PDP_VID2SURF_VID2USECSC_SHIFT (25)
#define ODN_PDP_VID2SURF_VID2USECSC_LENGTH (1)
#define ODN_PDP_VID2SURF_VID2USECSC_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3SURF_OFFSET (0x0018)

/* PDP, VID3SURF, VID3PIXFMT
*/
#define ODN_PDP_VID3SURF_VID3PIXFMT_MASK (0xF8000000)
#define ODN_PDP_VID3SURF_VID3PIXFMT_LSBMASK (0x0000001F)
#define ODN_PDP_VID3SURF_VID3PIXFMT_SHIFT (27)
#define ODN_PDP_VID3SURF_VID3PIXFMT_LENGTH (5)
#define ODN_PDP_VID3SURF_VID3PIXFMT_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SURF, VID3COSITED
*/
#define ODN_PDP_VID3SURF_VID3COSITED_MASK (0x00800000)
#define ODN_PDP_VID3SURF_VID3COSITED_LSBMASK (0x00000001)
#define ODN_PDP_VID3SURF_VID3COSITED_SHIFT (23)
#define ODN_PDP_VID3SURF_VID3COSITED_LENGTH (1)
#define ODN_PDP_VID3SURF_VID3COSITED_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SURF, VID3USEGAMMA
*/
#define ODN_PDP_VID3SURF_VID3USEGAMMA_MASK (0x04000000)
#define ODN_PDP_VID3SURF_VID3USEGAMMA_LSBMASK (0x00000001)
#define ODN_PDP_VID3SURF_VID3USEGAMMA_SHIFT (26)
#define ODN_PDP_VID3SURF_VID3USEGAMMA_LENGTH (1)
#define ODN_PDP_VID3SURF_VID3USEGAMMA_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SURF, VID3USECSC
*/
#define ODN_PDP_VID3SURF_VID3USECSC_MASK (0x02000000)
#define ODN_PDP_VID3SURF_VID3USECSC_LSBMASK (0x00000001)
#define ODN_PDP_VID3SURF_VID3USECSC_SHIFT (25)
#define ODN_PDP_VID3SURF_VID3USECSC_LENGTH (1)
#define ODN_PDP_VID3SURF_VID3USECSC_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4SURF_OFFSET (0x001C)

/* PDP, VID4SURF, VID4PIXFMT
*/
#define ODN_PDP_VID4SURF_VID4PIXFMT_MASK (0xF8000000)
#define ODN_PDP_VID4SURF_VID4PIXFMT_LSBMASK (0x0000001F)
#define ODN_PDP_VID4SURF_VID4PIXFMT_SHIFT (27)
#define ODN_PDP_VID4SURF_VID4PIXFMT_LENGTH (5)
#define ODN_PDP_VID4SURF_VID4PIXFMT_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SURF, VID4COSITED
*/
#define ODN_PDP_VID4SURF_VID4COSITED_MASK (0x00800000)
#define ODN_PDP_VID4SURF_VID4COSITED_LSBMASK (0x00000001)
#define ODN_PDP_VID4SURF_VID4COSITED_SHIFT (23)
#define ODN_PDP_VID4SURF_VID4COSITED_LENGTH (1)
#define ODN_PDP_VID4SURF_VID4COSITED_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SURF, VID4USEGAMMA
*/
#define ODN_PDP_VID4SURF_VID4USEGAMMA_MASK (0x04000000)
#define ODN_PDP_VID4SURF_VID4USEGAMMA_LSBMASK (0x00000001)
#define ODN_PDP_VID4SURF_VID4USEGAMMA_SHIFT (26)
#define ODN_PDP_VID4SURF_VID4USEGAMMA_LENGTH (1)
#define ODN_PDP_VID4SURF_VID4USEGAMMA_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SURF, VID4USECSC
*/
#define ODN_PDP_VID4SURF_VID4USECSC_MASK (0x02000000)
#define ODN_PDP_VID4SURF_VID4USECSC_LSBMASK (0x00000001)
#define ODN_PDP_VID4SURF_VID4USECSC_SHIFT (25)
#define ODN_PDP_VID4SURF_VID4USECSC_LENGTH (1)
#define ODN_PDP_VID4SURF_VID4USECSC_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1CTRL_OFFSET (0x0020)

/* PDP, GRPH1CTRL, GRPH1STREN
*/
#define ODN_PDP_GRPH1CTRL_GRPH1STREN_MASK (0x80000000)
#define ODN_PDP_GRPH1CTRL_GRPH1STREN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1CTRL_GRPH1STREN_SHIFT (31)
#define ODN_PDP_GRPH1CTRL_GRPH1STREN_LENGTH (1)
#define ODN_PDP_GRPH1CTRL_GRPH1STREN_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1CTRL, GRPH1CKEYEN
*/
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYEN_MASK (0x40000000)
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYEN_SHIFT (30)
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYEN_LENGTH (1)
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1CTRL, GRPH1CKEYSRC
*/
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYSRC_MASK (0x20000000)
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYSRC_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYSRC_SHIFT (29)
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYSRC_LENGTH (1)
#define ODN_PDP_GRPH1CTRL_GRPH1CKEYSRC_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1CTRL, GRPH1BLEND
*/
#define ODN_PDP_GRPH1CTRL_GRPH1BLEND_MASK (0x18000000)
#define ODN_PDP_GRPH1CTRL_GRPH1BLEND_LSBMASK (0x00000003)
#define ODN_PDP_GRPH1CTRL_GRPH1BLEND_SHIFT (27)
#define ODN_PDP_GRPH1CTRL_GRPH1BLEND_LENGTH (2)
#define ODN_PDP_GRPH1CTRL_GRPH1BLEND_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1CTRL, GRPH1BLENDPOS
*/
#define ODN_PDP_GRPH1CTRL_GRPH1BLENDPOS_MASK (0x07000000)
#define ODN_PDP_GRPH1CTRL_GRPH1BLENDPOS_LSBMASK (0x00000007)
#define ODN_PDP_GRPH1CTRL_GRPH1BLENDPOS_SHIFT (24)
#define ODN_PDP_GRPH1CTRL_GRPH1BLENDPOS_LENGTH (3)
#define ODN_PDP_GRPH1CTRL_GRPH1BLENDPOS_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1CTRL, GRPH1DITHEREN
*/
#define ODN_PDP_GRPH1CTRL_GRPH1DITHEREN_MASK (0x00800000)
#define ODN_PDP_GRPH1CTRL_GRPH1DITHEREN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1CTRL_GRPH1DITHEREN_SHIFT (23)
#define ODN_PDP_GRPH1CTRL_GRPH1DITHEREN_LENGTH (1)
#define ODN_PDP_GRPH1CTRL_GRPH1DITHEREN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2CTRL_OFFSET (0x0024)

/* PDP, GRPH2CTRL, GRPH2STREN
*/
#define ODN_PDP_GRPH2CTRL_GRPH2STREN_MASK (0x80000000)
#define ODN_PDP_GRPH2CTRL_GRPH2STREN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2CTRL_GRPH2STREN_SHIFT (31)
#define ODN_PDP_GRPH2CTRL_GRPH2STREN_LENGTH (1)
#define ODN_PDP_GRPH2CTRL_GRPH2STREN_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2CTRL, GRPH2CKEYEN
*/
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYEN_MASK (0x40000000)
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYEN_SHIFT (30)
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYEN_LENGTH (1)
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2CTRL, GRPH2CKEYSRC
*/
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYSRC_MASK (0x20000000)
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYSRC_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYSRC_SHIFT (29)
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYSRC_LENGTH (1)
#define ODN_PDP_GRPH2CTRL_GRPH2CKEYSRC_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2CTRL, GRPH2BLEND
*/
#define ODN_PDP_GRPH2CTRL_GRPH2BLEND_MASK (0x18000000)
#define ODN_PDP_GRPH2CTRL_GRPH2BLEND_LSBMASK (0x00000003)
#define ODN_PDP_GRPH2CTRL_GRPH2BLEND_SHIFT (27)
#define ODN_PDP_GRPH2CTRL_GRPH2BLEND_LENGTH (2)
#define ODN_PDP_GRPH2CTRL_GRPH2BLEND_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2CTRL, GRPH2BLENDPOS
*/
#define ODN_PDP_GRPH2CTRL_GRPH2BLENDPOS_MASK (0x07000000)
#define ODN_PDP_GRPH2CTRL_GRPH2BLENDPOS_LSBMASK (0x00000007)
#define ODN_PDP_GRPH2CTRL_GRPH2BLENDPOS_SHIFT (24)
#define ODN_PDP_GRPH2CTRL_GRPH2BLENDPOS_LENGTH (3)
#define ODN_PDP_GRPH2CTRL_GRPH2BLENDPOS_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2CTRL, GRPH2DITHEREN
*/
#define ODN_PDP_GRPH2CTRL_GRPH2DITHEREN_MASK (0x00800000)
#define ODN_PDP_GRPH2CTRL_GRPH2DITHEREN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2CTRL_GRPH2DITHEREN_SHIFT (23)
#define ODN_PDP_GRPH2CTRL_GRPH2DITHEREN_LENGTH (1)
#define ODN_PDP_GRPH2CTRL_GRPH2DITHEREN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3CTRL_OFFSET (0x0028)

/* PDP, GRPH3CTRL, GRPH3STREN
*/
#define ODN_PDP_GRPH3CTRL_GRPH3STREN_MASK (0x80000000)
#define ODN_PDP_GRPH3CTRL_GRPH3STREN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3CTRL_GRPH3STREN_SHIFT (31)
#define ODN_PDP_GRPH3CTRL_GRPH3STREN_LENGTH (1)
#define ODN_PDP_GRPH3CTRL_GRPH3STREN_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3CTRL, GRPH3CKEYEN
*/
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYEN_MASK (0x40000000)
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYEN_SHIFT (30)
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYEN_LENGTH (1)
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3CTRL, GRPH3CKEYSRC
*/
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYSRC_MASK (0x20000000)
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYSRC_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYSRC_SHIFT (29)
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYSRC_LENGTH (1)
#define ODN_PDP_GRPH3CTRL_GRPH3CKEYSRC_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3CTRL, GRPH3BLEND
*/
#define ODN_PDP_GRPH3CTRL_GRPH3BLEND_MASK (0x18000000)
#define ODN_PDP_GRPH3CTRL_GRPH3BLEND_LSBMASK (0x00000003)
#define ODN_PDP_GRPH3CTRL_GRPH3BLEND_SHIFT (27)
#define ODN_PDP_GRPH3CTRL_GRPH3BLEND_LENGTH (2)
#define ODN_PDP_GRPH3CTRL_GRPH3BLEND_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3CTRL, GRPH3BLENDPOS
*/
#define ODN_PDP_GRPH3CTRL_GRPH3BLENDPOS_MASK (0x07000000)
#define ODN_PDP_GRPH3CTRL_GRPH3BLENDPOS_LSBMASK (0x00000007)
#define ODN_PDP_GRPH3CTRL_GRPH3BLENDPOS_SHIFT (24)
#define ODN_PDP_GRPH3CTRL_GRPH3BLENDPOS_LENGTH (3)
#define ODN_PDP_GRPH3CTRL_GRPH3BLENDPOS_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3CTRL, GRPH3DITHEREN
*/
#define ODN_PDP_GRPH3CTRL_GRPH3DITHEREN_MASK (0x00800000)
#define ODN_PDP_GRPH3CTRL_GRPH3DITHEREN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3CTRL_GRPH3DITHEREN_SHIFT (23)
#define ODN_PDP_GRPH3CTRL_GRPH3DITHEREN_LENGTH (1)
#define ODN_PDP_GRPH3CTRL_GRPH3DITHEREN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4CTRL_OFFSET (0x002C)

/* PDP, GRPH4CTRL, GRPH4STREN
*/
#define ODN_PDP_GRPH4CTRL_GRPH4STREN_MASK (0x80000000)
#define ODN_PDP_GRPH4CTRL_GRPH4STREN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4CTRL_GRPH4STREN_SHIFT (31)
#define ODN_PDP_GRPH4CTRL_GRPH4STREN_LENGTH (1)
#define ODN_PDP_GRPH4CTRL_GRPH4STREN_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4CTRL, GRPH4CKEYEN
*/
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYEN_MASK (0x40000000)
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYEN_SHIFT (30)
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYEN_LENGTH (1)
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4CTRL, GRPH4CKEYSRC
*/
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYSRC_MASK (0x20000000)
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYSRC_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYSRC_SHIFT (29)
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYSRC_LENGTH (1)
#define ODN_PDP_GRPH4CTRL_GRPH4CKEYSRC_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4CTRL, GRPH4BLEND
*/
#define ODN_PDP_GRPH4CTRL_GRPH4BLEND_MASK (0x18000000)
#define ODN_PDP_GRPH4CTRL_GRPH4BLEND_LSBMASK (0x00000003)
#define ODN_PDP_GRPH4CTRL_GRPH4BLEND_SHIFT (27)
#define ODN_PDP_GRPH4CTRL_GRPH4BLEND_LENGTH (2)
#define ODN_PDP_GRPH4CTRL_GRPH4BLEND_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4CTRL, GRPH4BLENDPOS
*/
#define ODN_PDP_GRPH4CTRL_GRPH4BLENDPOS_MASK (0x07000000)
#define ODN_PDP_GRPH4CTRL_GRPH4BLENDPOS_LSBMASK (0x00000007)
#define ODN_PDP_GRPH4CTRL_GRPH4BLENDPOS_SHIFT (24)
#define ODN_PDP_GRPH4CTRL_GRPH4BLENDPOS_LENGTH (3)
#define ODN_PDP_GRPH4CTRL_GRPH4BLENDPOS_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4CTRL, GRPH4DITHEREN
*/
#define ODN_PDP_GRPH4CTRL_GRPH4DITHEREN_MASK (0x00800000)
#define ODN_PDP_GRPH4CTRL_GRPH4DITHEREN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4CTRL_GRPH4DITHEREN_SHIFT (23)
#define ODN_PDP_GRPH4CTRL_GRPH4DITHEREN_LENGTH (1)
#define ODN_PDP_GRPH4CTRL_GRPH4DITHEREN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1CTRL_OFFSET (0x0030)

/* PDP, VID1CTRL, VID1STREN
*/
#define ODN_PDP_VID1CTRL_VID1STREN_MASK (0x80000000)
#define ODN_PDP_VID1CTRL_VID1STREN_LSBMASK (0x00000001)
#define ODN_PDP_VID1CTRL_VID1STREN_SHIFT (31)
#define ODN_PDP_VID1CTRL_VID1STREN_LENGTH (1)
#define ODN_PDP_VID1CTRL_VID1STREN_SIGNED_FIELD IMG_FALSE

/* PDP, VID1CTRL, VID1CKEYEN
*/
#define ODN_PDP_VID1CTRL_VID1CKEYEN_MASK (0x40000000)
#define ODN_PDP_VID1CTRL_VID1CKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_VID1CTRL_VID1CKEYEN_SHIFT (30)
#define ODN_PDP_VID1CTRL_VID1CKEYEN_LENGTH (1)
#define ODN_PDP_VID1CTRL_VID1CKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, VID1CTRL, VID1CKEYSRC
*/
#define ODN_PDP_VID1CTRL_VID1CKEYSRC_MASK (0x20000000)
#define ODN_PDP_VID1CTRL_VID1CKEYSRC_LSBMASK (0x00000001)
#define ODN_PDP_VID1CTRL_VID1CKEYSRC_SHIFT (29)
#define ODN_PDP_VID1CTRL_VID1CKEYSRC_LENGTH (1)
#define ODN_PDP_VID1CTRL_VID1CKEYSRC_SIGNED_FIELD IMG_FALSE

/* PDP, VID1CTRL, VID1BLEND
*/
#define ODN_PDP_VID1CTRL_VID1BLEND_MASK (0x18000000)
#define ODN_PDP_VID1CTRL_VID1BLEND_LSBMASK (0x00000003)
#define ODN_PDP_VID1CTRL_VID1BLEND_SHIFT (27)
#define ODN_PDP_VID1CTRL_VID1BLEND_LENGTH (2)
#define ODN_PDP_VID1CTRL_VID1BLEND_SIGNED_FIELD IMG_FALSE

/* PDP, VID1CTRL, VID1BLENDPOS
*/
#define ODN_PDP_VID1CTRL_VID1BLENDPOS_MASK (0x07000000)
#define ODN_PDP_VID1CTRL_VID1BLENDPOS_LSBMASK (0x00000007)
#define ODN_PDP_VID1CTRL_VID1BLENDPOS_SHIFT (24)
#define ODN_PDP_VID1CTRL_VID1BLENDPOS_LENGTH (3)
#define ODN_PDP_VID1CTRL_VID1BLENDPOS_SIGNED_FIELD IMG_FALSE

/* PDP, VID1CTRL, VID1DITHEREN
*/
#define ODN_PDP_VID1CTRL_VID1DITHEREN_MASK (0x00800000)
#define ODN_PDP_VID1CTRL_VID1DITHEREN_LSBMASK (0x00000001)
#define ODN_PDP_VID1CTRL_VID1DITHEREN_SHIFT (23)
#define ODN_PDP_VID1CTRL_VID1DITHEREN_LENGTH (1)
#define ODN_PDP_VID1CTRL_VID1DITHEREN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2CTRL_OFFSET (0x0034)

/* PDP, VID2CTRL, VID2STREN
*/
#define ODN_PDP_VID2CTRL_VID2STREN_MASK (0x80000000)
#define ODN_PDP_VID2CTRL_VID2STREN_LSBMASK (0x00000001)
#define ODN_PDP_VID2CTRL_VID2STREN_SHIFT (31)
#define ODN_PDP_VID2CTRL_VID2STREN_LENGTH (1)
#define ODN_PDP_VID2CTRL_VID2STREN_SIGNED_FIELD IMG_FALSE

/* PDP, VID2CTRL, VID2CKEYEN
*/
#define ODN_PDP_VID2CTRL_VID2CKEYEN_MASK (0x40000000)
#define ODN_PDP_VID2CTRL_VID2CKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_VID2CTRL_VID2CKEYEN_SHIFT (30)
#define ODN_PDP_VID2CTRL_VID2CKEYEN_LENGTH (1)
#define ODN_PDP_VID2CTRL_VID2CKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, VID2CTRL, VID2CKEYSRC
*/
#define ODN_PDP_VID2CTRL_VID2CKEYSRC_MASK (0x20000000)
#define ODN_PDP_VID2CTRL_VID2CKEYSRC_LSBMASK (0x00000001)
#define ODN_PDP_VID2CTRL_VID2CKEYSRC_SHIFT (29)
#define ODN_PDP_VID2CTRL_VID2CKEYSRC_LENGTH (1)
#define ODN_PDP_VID2CTRL_VID2CKEYSRC_SIGNED_FIELD IMG_FALSE

/* PDP, VID2CTRL, VID2BLEND
*/
#define ODN_PDP_VID2CTRL_VID2BLEND_MASK (0x18000000)
#define ODN_PDP_VID2CTRL_VID2BLEND_LSBMASK (0x00000003)
#define ODN_PDP_VID2CTRL_VID2BLEND_SHIFT (27)
#define ODN_PDP_VID2CTRL_VID2BLEND_LENGTH (2)
#define ODN_PDP_VID2CTRL_VID2BLEND_SIGNED_FIELD IMG_FALSE

/* PDP, VID2CTRL, VID2BLENDPOS
*/
#define ODN_PDP_VID2CTRL_VID2BLENDPOS_MASK (0x07000000)
#define ODN_PDP_VID2CTRL_VID2BLENDPOS_LSBMASK (0x00000007)
#define ODN_PDP_VID2CTRL_VID2BLENDPOS_SHIFT (24)
#define ODN_PDP_VID2CTRL_VID2BLENDPOS_LENGTH (3)
#define ODN_PDP_VID2CTRL_VID2BLENDPOS_SIGNED_FIELD IMG_FALSE

/* PDP, VID2CTRL, VID2DITHEREN
*/
#define ODN_PDP_VID2CTRL_VID2DITHEREN_MASK (0x00800000)
#define ODN_PDP_VID2CTRL_VID2DITHEREN_LSBMASK (0x00000001)
#define ODN_PDP_VID2CTRL_VID2DITHEREN_SHIFT (23)
#define ODN_PDP_VID2CTRL_VID2DITHEREN_LENGTH (1)
#define ODN_PDP_VID2CTRL_VID2DITHEREN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3CTRL_OFFSET (0x0038)

/* PDP, VID3CTRL, VID3STREN
*/
#define ODN_PDP_VID3CTRL_VID3STREN_MASK (0x80000000)
#define ODN_PDP_VID3CTRL_VID3STREN_LSBMASK (0x00000001)
#define ODN_PDP_VID3CTRL_VID3STREN_SHIFT (31)
#define ODN_PDP_VID3CTRL_VID3STREN_LENGTH (1)
#define ODN_PDP_VID3CTRL_VID3STREN_SIGNED_FIELD IMG_FALSE

/* PDP, VID3CTRL, VID3CKEYEN
*/
#define ODN_PDP_VID3CTRL_VID3CKEYEN_MASK (0x40000000)
#define ODN_PDP_VID3CTRL_VID3CKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_VID3CTRL_VID3CKEYEN_SHIFT (30)
#define ODN_PDP_VID3CTRL_VID3CKEYEN_LENGTH (1)
#define ODN_PDP_VID3CTRL_VID3CKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, VID3CTRL, VID3CKEYSRC
*/
#define ODN_PDP_VID3CTRL_VID3CKEYSRC_MASK (0x20000000)
#define ODN_PDP_VID3CTRL_VID3CKEYSRC_LSBMASK (0x00000001)
#define ODN_PDP_VID3CTRL_VID3CKEYSRC_SHIFT (29)
#define ODN_PDP_VID3CTRL_VID3CKEYSRC_LENGTH (1)
#define ODN_PDP_VID3CTRL_VID3CKEYSRC_SIGNED_FIELD IMG_FALSE

/* PDP, VID3CTRL, VID3BLEND
*/
#define ODN_PDP_VID3CTRL_VID3BLEND_MASK (0x18000000)
#define ODN_PDP_VID3CTRL_VID3BLEND_LSBMASK (0x00000003)
#define ODN_PDP_VID3CTRL_VID3BLEND_SHIFT (27)
#define ODN_PDP_VID3CTRL_VID3BLEND_LENGTH (2)
#define ODN_PDP_VID3CTRL_VID3BLEND_SIGNED_FIELD IMG_FALSE

/* PDP, VID3CTRL, VID3BLENDPOS
*/
#define ODN_PDP_VID3CTRL_VID3BLENDPOS_MASK (0x07000000)
#define ODN_PDP_VID3CTRL_VID3BLENDPOS_LSBMASK (0x00000007)
#define ODN_PDP_VID3CTRL_VID3BLENDPOS_SHIFT (24)
#define ODN_PDP_VID3CTRL_VID3BLENDPOS_LENGTH (3)
#define ODN_PDP_VID3CTRL_VID3BLENDPOS_SIGNED_FIELD IMG_FALSE

/* PDP, VID3CTRL, VID3DITHEREN
*/
#define ODN_PDP_VID3CTRL_VID3DITHEREN_MASK (0x00800000)
#define ODN_PDP_VID3CTRL_VID3DITHEREN_LSBMASK (0x00000001)
#define ODN_PDP_VID3CTRL_VID3DITHEREN_SHIFT (23)
#define ODN_PDP_VID3CTRL_VID3DITHEREN_LENGTH (1)
#define ODN_PDP_VID3CTRL_VID3DITHEREN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4CTRL_OFFSET (0x003C)

/* PDP, VID4CTRL, VID4STREN
*/
#define ODN_PDP_VID4CTRL_VID4STREN_MASK (0x80000000)
#define ODN_PDP_VID4CTRL_VID4STREN_LSBMASK (0x00000001)
#define ODN_PDP_VID4CTRL_VID4STREN_SHIFT (31)
#define ODN_PDP_VID4CTRL_VID4STREN_LENGTH (1)
#define ODN_PDP_VID4CTRL_VID4STREN_SIGNED_FIELD IMG_FALSE

/* PDP, VID4CTRL, VID4CKEYEN
*/
#define ODN_PDP_VID4CTRL_VID4CKEYEN_MASK (0x40000000)
#define ODN_PDP_VID4CTRL_VID4CKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_VID4CTRL_VID4CKEYEN_SHIFT (30)
#define ODN_PDP_VID4CTRL_VID4CKEYEN_LENGTH (1)
#define ODN_PDP_VID4CTRL_VID4CKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, VID4CTRL, VID4CKEYSRC
*/
#define ODN_PDP_VID4CTRL_VID4CKEYSRC_MASK (0x20000000)
#define ODN_PDP_VID4CTRL_VID4CKEYSRC_LSBMASK (0x00000001)
#define ODN_PDP_VID4CTRL_VID4CKEYSRC_SHIFT (29)
#define ODN_PDP_VID4CTRL_VID4CKEYSRC_LENGTH (1)
#define ODN_PDP_VID4CTRL_VID4CKEYSRC_SIGNED_FIELD IMG_FALSE

/* PDP, VID4CTRL, VID4BLEND
*/
#define ODN_PDP_VID4CTRL_VID4BLEND_MASK (0x18000000)
#define ODN_PDP_VID4CTRL_VID4BLEND_LSBMASK (0x00000003)
#define ODN_PDP_VID4CTRL_VID4BLEND_SHIFT (27)
#define ODN_PDP_VID4CTRL_VID4BLEND_LENGTH (2)
#define ODN_PDP_VID4CTRL_VID4BLEND_SIGNED_FIELD IMG_FALSE

/* PDP, VID4CTRL, VID4BLENDPOS
*/
#define ODN_PDP_VID4CTRL_VID4BLENDPOS_MASK (0x07000000)
#define ODN_PDP_VID4CTRL_VID4BLENDPOS_LSBMASK (0x00000007)
#define ODN_PDP_VID4CTRL_VID4BLENDPOS_SHIFT (24)
#define ODN_PDP_VID4CTRL_VID4BLENDPOS_LENGTH (3)
#define ODN_PDP_VID4CTRL_VID4BLENDPOS_SIGNED_FIELD IMG_FALSE

/* PDP, VID4CTRL, VID4DITHEREN
*/
#define ODN_PDP_VID4CTRL_VID4DITHEREN_MASK (0x00800000)
#define ODN_PDP_VID4CTRL_VID4DITHEREN_LSBMASK (0x00000001)
#define ODN_PDP_VID4CTRL_VID4DITHEREN_SHIFT (23)
#define ODN_PDP_VID4CTRL_VID4DITHEREN_LENGTH (1)
#define ODN_PDP_VID4CTRL_VID4DITHEREN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1UCTRL_OFFSET (0x0050)

/* PDP, VID1UCTRL, VID1UVHALFSTR
*/
#define ODN_PDP_VID1UCTRL_VID1UVHALFSTR_MASK (0xC0000000)
#define ODN_PDP_VID1UCTRL_VID1UVHALFSTR_LSBMASK (0x00000003)
#define ODN_PDP_VID1UCTRL_VID1UVHALFSTR_SHIFT (30)
#define ODN_PDP_VID1UCTRL_VID1UVHALFSTR_LENGTH (2)
#define ODN_PDP_VID1UCTRL_VID1UVHALFSTR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2UCTRL_OFFSET (0x0054)

/* PDP, VID2UCTRL, VID2UVHALFSTR
*/
#define ODN_PDP_VID2UCTRL_VID2UVHALFSTR_MASK (0xC0000000)
#define ODN_PDP_VID2UCTRL_VID2UVHALFSTR_LSBMASK (0x00000003)
#define ODN_PDP_VID2UCTRL_VID2UVHALFSTR_SHIFT (30)
#define ODN_PDP_VID2UCTRL_VID2UVHALFSTR_LENGTH (2)
#define ODN_PDP_VID2UCTRL_VID2UVHALFSTR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3UCTRL_OFFSET (0x0058)

/* PDP, VID3UCTRL, VID3UVHALFSTR
*/
#define ODN_PDP_VID3UCTRL_VID3UVHALFSTR_MASK (0xC0000000)
#define ODN_PDP_VID3UCTRL_VID3UVHALFSTR_LSBMASK (0x00000003)
#define ODN_PDP_VID3UCTRL_VID3UVHALFSTR_SHIFT (30)
#define ODN_PDP_VID3UCTRL_VID3UVHALFSTR_LENGTH (2)
#define ODN_PDP_VID3UCTRL_VID3UVHALFSTR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4UCTRL_OFFSET (0x005C)

/* PDP, VID4UCTRL, VID4UVHALFSTR
*/
#define ODN_PDP_VID4UCTRL_VID4UVHALFSTR_MASK (0xC0000000)
#define ODN_PDP_VID4UCTRL_VID4UVHALFSTR_LSBMASK (0x00000003)
#define ODN_PDP_VID4UCTRL_VID4UVHALFSTR_SHIFT (30)
#define ODN_PDP_VID4UCTRL_VID4UVHALFSTR_LENGTH (2)
#define ODN_PDP_VID4UCTRL_VID4UVHALFSTR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1STRIDE_OFFSET (0x0060)

/* PDP, GRPH1STRIDE, GRPH1STRIDE
*/
#define ODN_PDP_GRPH1STRIDE_GRPH1STRIDE_MASK (0xFFC00000)
#define ODN_PDP_GRPH1STRIDE_GRPH1STRIDE_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH1STRIDE_GRPH1STRIDE_SHIFT (22)
#define ODN_PDP_GRPH1STRIDE_GRPH1STRIDE_LENGTH (10)
#define ODN_PDP_GRPH1STRIDE_GRPH1STRIDE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2STRIDE_OFFSET (0x0064)

/* PDP, GRPH2STRIDE, GRPH2STRIDE
*/
#define ODN_PDP_GRPH2STRIDE_GRPH2STRIDE_MASK (0xFFC00000)
#define ODN_PDP_GRPH2STRIDE_GRPH2STRIDE_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH2STRIDE_GRPH2STRIDE_SHIFT (22)
#define ODN_PDP_GRPH2STRIDE_GRPH2STRIDE_LENGTH (10)
#define ODN_PDP_GRPH2STRIDE_GRPH2STRIDE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3STRIDE_OFFSET (0x0068)

/* PDP, GRPH3STRIDE, GRPH3STRIDE
*/
#define ODN_PDP_GRPH3STRIDE_GRPH3STRIDE_MASK (0xFFC00000)
#define ODN_PDP_GRPH3STRIDE_GRPH3STRIDE_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH3STRIDE_GRPH3STRIDE_SHIFT (22)
#define ODN_PDP_GRPH3STRIDE_GRPH3STRIDE_LENGTH (10)
#define ODN_PDP_GRPH3STRIDE_GRPH3STRIDE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4STRIDE_OFFSET (0x006C)

/* PDP, GRPH4STRIDE, GRPH4STRIDE
*/
#define ODN_PDP_GRPH4STRIDE_GRPH4STRIDE_MASK (0xFFC00000)
#define ODN_PDP_GRPH4STRIDE_GRPH4STRIDE_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH4STRIDE_GRPH4STRIDE_SHIFT (22)
#define ODN_PDP_GRPH4STRIDE_GRPH4STRIDE_LENGTH (10)
#define ODN_PDP_GRPH4STRIDE_GRPH4STRIDE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1STRIDE_OFFSET (0x0070)

/* PDP, VID1STRIDE, VID1STRIDE
*/
#define ODN_PDP_VID1STRIDE_VID1STRIDE_MASK (0xFFC00000)
#define ODN_PDP_VID1STRIDE_VID1STRIDE_LSBMASK (0x000003FF)
#define ODN_PDP_VID1STRIDE_VID1STRIDE_SHIFT (22)
#define ODN_PDP_VID1STRIDE_VID1STRIDE_LENGTH (10)
#define ODN_PDP_VID1STRIDE_VID1STRIDE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2STRIDE_OFFSET (0x0074)

/* PDP, VID2STRIDE, VID2STRIDE
*/
#define ODN_PDP_VID2STRIDE_VID2STRIDE_MASK (0xFFC00000)
#define ODN_PDP_VID2STRIDE_VID2STRIDE_LSBMASK (0x000003FF)
#define ODN_PDP_VID2STRIDE_VID2STRIDE_SHIFT (22)
#define ODN_PDP_VID2STRIDE_VID2STRIDE_LENGTH (10)
#define ODN_PDP_VID2STRIDE_VID2STRIDE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3STRIDE_OFFSET (0x0078)

/* PDP, VID3STRIDE, VID3STRIDE
*/
#define ODN_PDP_VID3STRIDE_VID3STRIDE_MASK (0xFFC00000)
#define ODN_PDP_VID3STRIDE_VID3STRIDE_LSBMASK (0x000003FF)
#define ODN_PDP_VID3STRIDE_VID3STRIDE_SHIFT (22)
#define ODN_PDP_VID3STRIDE_VID3STRIDE_LENGTH (10)
#define ODN_PDP_VID3STRIDE_VID3STRIDE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4STRIDE_OFFSET (0x007C)

/* PDP, VID4STRIDE, VID4STRIDE
*/
#define ODN_PDP_VID4STRIDE_VID4STRIDE_MASK (0xFFC00000)
#define ODN_PDP_VID4STRIDE_VID4STRIDE_LSBMASK (0x000003FF)
#define ODN_PDP_VID4STRIDE_VID4STRIDE_SHIFT (22)
#define ODN_PDP_VID4STRIDE_VID4STRIDE_LENGTH (10)
#define ODN_PDP_VID4STRIDE_VID4STRIDE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1SIZE_OFFSET (0x0080)

/* PDP, GRPH1SIZE, GRPH1WIDTH
*/
#define ODN_PDP_GRPH1SIZE_GRPH1WIDTH_MASK (0x0FFF0000)
#define ODN_PDP_GRPH1SIZE_GRPH1WIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH1SIZE_GRPH1WIDTH_SHIFT (16)
#define ODN_PDP_GRPH1SIZE_GRPH1WIDTH_LENGTH (12)
#define ODN_PDP_GRPH1SIZE_GRPH1WIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1SIZE, GRPH1HEIGHT
*/
#define ODN_PDP_GRPH1SIZE_GRPH1HEIGHT_MASK (0x00000FFF)
#define ODN_PDP_GRPH1SIZE_GRPH1HEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH1SIZE_GRPH1HEIGHT_SHIFT (0)
#define ODN_PDP_GRPH1SIZE_GRPH1HEIGHT_LENGTH (12)
#define ODN_PDP_GRPH1SIZE_GRPH1HEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2SIZE_OFFSET (0x0084)

/* PDP, GRPH2SIZE, GRPH2WIDTH
*/
#define ODN_PDP_GRPH2SIZE_GRPH2WIDTH_MASK (0x0FFF0000)
#define ODN_PDP_GRPH2SIZE_GRPH2WIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH2SIZE_GRPH2WIDTH_SHIFT (16)
#define ODN_PDP_GRPH2SIZE_GRPH2WIDTH_LENGTH (12)
#define ODN_PDP_GRPH2SIZE_GRPH2WIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2SIZE, GRPH2HEIGHT
*/
#define ODN_PDP_GRPH2SIZE_GRPH2HEIGHT_MASK (0x00000FFF)
#define ODN_PDP_GRPH2SIZE_GRPH2HEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH2SIZE_GRPH2HEIGHT_SHIFT (0)
#define ODN_PDP_GRPH2SIZE_GRPH2HEIGHT_LENGTH (12)
#define ODN_PDP_GRPH2SIZE_GRPH2HEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3SIZE_OFFSET (0x0088)

/* PDP, GRPH3SIZE, GRPH3WIDTH
*/
#define ODN_PDP_GRPH3SIZE_GRPH3WIDTH_MASK (0x0FFF0000)
#define ODN_PDP_GRPH3SIZE_GRPH3WIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH3SIZE_GRPH3WIDTH_SHIFT (16)
#define ODN_PDP_GRPH3SIZE_GRPH3WIDTH_LENGTH (12)
#define ODN_PDP_GRPH3SIZE_GRPH3WIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3SIZE, GRPH3HEIGHT
*/
#define ODN_PDP_GRPH3SIZE_GRPH3HEIGHT_MASK (0x00000FFF)
#define ODN_PDP_GRPH3SIZE_GRPH3HEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH3SIZE_GRPH3HEIGHT_SHIFT (0)
#define ODN_PDP_GRPH3SIZE_GRPH3HEIGHT_LENGTH (12)
#define ODN_PDP_GRPH3SIZE_GRPH3HEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4SIZE_OFFSET (0x008C)

/* PDP, GRPH4SIZE, GRPH4WIDTH
*/
#define ODN_PDP_GRPH4SIZE_GRPH4WIDTH_MASK (0x0FFF0000)
#define ODN_PDP_GRPH4SIZE_GRPH4WIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH4SIZE_GRPH4WIDTH_SHIFT (16)
#define ODN_PDP_GRPH4SIZE_GRPH4WIDTH_LENGTH (12)
#define ODN_PDP_GRPH4SIZE_GRPH4WIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4SIZE, GRPH4HEIGHT
*/
#define ODN_PDP_GRPH4SIZE_GRPH4HEIGHT_MASK (0x00000FFF)
#define ODN_PDP_GRPH4SIZE_GRPH4HEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH4SIZE_GRPH4HEIGHT_SHIFT (0)
#define ODN_PDP_GRPH4SIZE_GRPH4HEIGHT_LENGTH (12)
#define ODN_PDP_GRPH4SIZE_GRPH4HEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1SIZE_OFFSET (0x0090)

/* PDP, VID1SIZE, VID1WIDTH
*/
#define ODN_PDP_VID1SIZE_VID1WIDTH_MASK (0x0FFF0000)
#define ODN_PDP_VID1SIZE_VID1WIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1SIZE_VID1WIDTH_SHIFT (16)
#define ODN_PDP_VID1SIZE_VID1WIDTH_LENGTH (12)
#define ODN_PDP_VID1SIZE_VID1WIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SIZE, VID1HEIGHT
*/
#define ODN_PDP_VID1SIZE_VID1HEIGHT_MASK (0x00000FFF)
#define ODN_PDP_VID1SIZE_VID1HEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1SIZE_VID1HEIGHT_SHIFT (0)
#define ODN_PDP_VID1SIZE_VID1HEIGHT_LENGTH (12)
#define ODN_PDP_VID1SIZE_VID1HEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2SIZE_OFFSET (0x0094)

/* PDP, VID2SIZE, VID2WIDTH
*/
#define ODN_PDP_VID2SIZE_VID2WIDTH_MASK (0x0FFF0000)
#define ODN_PDP_VID2SIZE_VID2WIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2SIZE_VID2WIDTH_SHIFT (16)
#define ODN_PDP_VID2SIZE_VID2WIDTH_LENGTH (12)
#define ODN_PDP_VID2SIZE_VID2WIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SIZE, VID2HEIGHT
*/
#define ODN_PDP_VID2SIZE_VID2HEIGHT_MASK (0x00000FFF)
#define ODN_PDP_VID2SIZE_VID2HEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2SIZE_VID2HEIGHT_SHIFT (0)
#define ODN_PDP_VID2SIZE_VID2HEIGHT_LENGTH (12)
#define ODN_PDP_VID2SIZE_VID2HEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3SIZE_OFFSET (0x0098)

/* PDP, VID3SIZE, VID3WIDTH
*/
#define ODN_PDP_VID3SIZE_VID3WIDTH_MASK (0x0FFF0000)
#define ODN_PDP_VID3SIZE_VID3WIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3SIZE_VID3WIDTH_SHIFT (16)
#define ODN_PDP_VID3SIZE_VID3WIDTH_LENGTH (12)
#define ODN_PDP_VID3SIZE_VID3WIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SIZE, VID3HEIGHT
*/
#define ODN_PDP_VID3SIZE_VID3HEIGHT_MASK (0x00000FFF)
#define ODN_PDP_VID3SIZE_VID3HEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3SIZE_VID3HEIGHT_SHIFT (0)
#define ODN_PDP_VID3SIZE_VID3HEIGHT_LENGTH (12)
#define ODN_PDP_VID3SIZE_VID3HEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4SIZE_OFFSET (0x009C)

/* PDP, VID4SIZE, VID4WIDTH
*/
#define ODN_PDP_VID4SIZE_VID4WIDTH_MASK (0x0FFF0000)
#define ODN_PDP_VID4SIZE_VID4WIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4SIZE_VID4WIDTH_SHIFT (16)
#define ODN_PDP_VID4SIZE_VID4WIDTH_LENGTH (12)
#define ODN_PDP_VID4SIZE_VID4WIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SIZE, VID4HEIGHT
*/
#define ODN_PDP_VID4SIZE_VID4HEIGHT_MASK (0x00000FFF)
#define ODN_PDP_VID4SIZE_VID4HEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4SIZE_VID4HEIGHT_SHIFT (0)
#define ODN_PDP_VID4SIZE_VID4HEIGHT_LENGTH (12)
#define ODN_PDP_VID4SIZE_VID4HEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1POSN_OFFSET (0x00A0)

/* PDP, GRPH1POSN, GRPH1XSTART
*/
#define ODN_PDP_GRPH1POSN_GRPH1XSTART_MASK (0x0FFF0000)
#define ODN_PDP_GRPH1POSN_GRPH1XSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH1POSN_GRPH1XSTART_SHIFT (16)
#define ODN_PDP_GRPH1POSN_GRPH1XSTART_LENGTH (12)
#define ODN_PDP_GRPH1POSN_GRPH1XSTART_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1POSN, GRPH1YSTART
*/
#define ODN_PDP_GRPH1POSN_GRPH1YSTART_MASK (0x00000FFF)
#define ODN_PDP_GRPH1POSN_GRPH1YSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH1POSN_GRPH1YSTART_SHIFT (0)
#define ODN_PDP_GRPH1POSN_GRPH1YSTART_LENGTH (12)
#define ODN_PDP_GRPH1POSN_GRPH1YSTART_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2POSN_OFFSET (0x00A4)

/* PDP, GRPH2POSN, GRPH2XSTART
*/
#define ODN_PDP_GRPH2POSN_GRPH2XSTART_MASK (0x0FFF0000)
#define ODN_PDP_GRPH2POSN_GRPH2XSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH2POSN_GRPH2XSTART_SHIFT (16)
#define ODN_PDP_GRPH2POSN_GRPH2XSTART_LENGTH (12)
#define ODN_PDP_GRPH2POSN_GRPH2XSTART_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2POSN, GRPH2YSTART
*/
#define ODN_PDP_GRPH2POSN_GRPH2YSTART_MASK (0x00000FFF)
#define ODN_PDP_GRPH2POSN_GRPH2YSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH2POSN_GRPH2YSTART_SHIFT (0)
#define ODN_PDP_GRPH2POSN_GRPH2YSTART_LENGTH (12)
#define ODN_PDP_GRPH2POSN_GRPH2YSTART_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3POSN_OFFSET (0x00A8)

/* PDP, GRPH3POSN, GRPH3XSTART
*/
#define ODN_PDP_GRPH3POSN_GRPH3XSTART_MASK (0x0FFF0000)
#define ODN_PDP_GRPH3POSN_GRPH3XSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH3POSN_GRPH3XSTART_SHIFT (16)
#define ODN_PDP_GRPH3POSN_GRPH3XSTART_LENGTH (12)
#define ODN_PDP_GRPH3POSN_GRPH3XSTART_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3POSN, GRPH3YSTART
*/
#define ODN_PDP_GRPH3POSN_GRPH3YSTART_MASK (0x00000FFF)
#define ODN_PDP_GRPH3POSN_GRPH3YSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH3POSN_GRPH3YSTART_SHIFT (0)
#define ODN_PDP_GRPH3POSN_GRPH3YSTART_LENGTH (12)
#define ODN_PDP_GRPH3POSN_GRPH3YSTART_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4POSN_OFFSET (0x00AC)

/* PDP, GRPH4POSN, GRPH4XSTART
*/
#define ODN_PDP_GRPH4POSN_GRPH4XSTART_MASK (0x0FFF0000)
#define ODN_PDP_GRPH4POSN_GRPH4XSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH4POSN_GRPH4XSTART_SHIFT (16)
#define ODN_PDP_GRPH4POSN_GRPH4XSTART_LENGTH (12)
#define ODN_PDP_GRPH4POSN_GRPH4XSTART_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4POSN, GRPH4YSTART
*/
#define ODN_PDP_GRPH4POSN_GRPH4YSTART_MASK (0x00000FFF)
#define ODN_PDP_GRPH4POSN_GRPH4YSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_GRPH4POSN_GRPH4YSTART_SHIFT (0)
#define ODN_PDP_GRPH4POSN_GRPH4YSTART_LENGTH (12)
#define ODN_PDP_GRPH4POSN_GRPH4YSTART_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1POSN_OFFSET (0x00B0)

/* PDP, VID1POSN, VID1XSTART
*/
#define ODN_PDP_VID1POSN_VID1XSTART_MASK (0x0FFF0000)
#define ODN_PDP_VID1POSN_VID1XSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1POSN_VID1XSTART_SHIFT (16)
#define ODN_PDP_VID1POSN_VID1XSTART_LENGTH (12)
#define ODN_PDP_VID1POSN_VID1XSTART_SIGNED_FIELD IMG_FALSE

/* PDP, VID1POSN, VID1YSTART
*/
#define ODN_PDP_VID1POSN_VID1YSTART_MASK (0x00000FFF)
#define ODN_PDP_VID1POSN_VID1YSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1POSN_VID1YSTART_SHIFT (0)
#define ODN_PDP_VID1POSN_VID1YSTART_LENGTH (12)
#define ODN_PDP_VID1POSN_VID1YSTART_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2POSN_OFFSET (0x00B4)

/* PDP, VID2POSN, VID2XSTART
*/
#define ODN_PDP_VID2POSN_VID2XSTART_MASK (0x0FFF0000)
#define ODN_PDP_VID2POSN_VID2XSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2POSN_VID2XSTART_SHIFT (16)
#define ODN_PDP_VID2POSN_VID2XSTART_LENGTH (12)
#define ODN_PDP_VID2POSN_VID2XSTART_SIGNED_FIELD IMG_FALSE

/* PDP, VID2POSN, VID2YSTART
*/
#define ODN_PDP_VID2POSN_VID2YSTART_MASK (0x00000FFF)
#define ODN_PDP_VID2POSN_VID2YSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2POSN_VID2YSTART_SHIFT (0)
#define ODN_PDP_VID2POSN_VID2YSTART_LENGTH (12)
#define ODN_PDP_VID2POSN_VID2YSTART_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3POSN_OFFSET (0x00B8)

/* PDP, VID3POSN, VID3XSTART
*/
#define ODN_PDP_VID3POSN_VID3XSTART_MASK (0x0FFF0000)
#define ODN_PDP_VID3POSN_VID3XSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3POSN_VID3XSTART_SHIFT (16)
#define ODN_PDP_VID3POSN_VID3XSTART_LENGTH (12)
#define ODN_PDP_VID3POSN_VID3XSTART_SIGNED_FIELD IMG_FALSE

/* PDP, VID3POSN, VID3YSTART
*/
#define ODN_PDP_VID3POSN_VID3YSTART_MASK (0x00000FFF)
#define ODN_PDP_VID3POSN_VID3YSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3POSN_VID3YSTART_SHIFT (0)
#define ODN_PDP_VID3POSN_VID3YSTART_LENGTH (12)
#define ODN_PDP_VID3POSN_VID3YSTART_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4POSN_OFFSET (0x00BC)

/* PDP, VID4POSN, VID4XSTART
*/
#define ODN_PDP_VID4POSN_VID4XSTART_MASK (0x0FFF0000)
#define ODN_PDP_VID4POSN_VID4XSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4POSN_VID4XSTART_SHIFT (16)
#define ODN_PDP_VID4POSN_VID4XSTART_LENGTH (12)
#define ODN_PDP_VID4POSN_VID4XSTART_SIGNED_FIELD IMG_FALSE

/* PDP, VID4POSN, VID4YSTART
*/
#define ODN_PDP_VID4POSN_VID4YSTART_MASK (0x00000FFF)
#define ODN_PDP_VID4POSN_VID4YSTART_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4POSN_VID4YSTART_SHIFT (0)
#define ODN_PDP_VID4POSN_VID4YSTART_LENGTH (12)
#define ODN_PDP_VID4POSN_VID4YSTART_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1GALPHA_OFFSET (0x00C0)

/* PDP, GRPH1GALPHA, GRPH1GALPHA
*/
#define ODN_PDP_GRPH1GALPHA_GRPH1GALPHA_MASK (0x000003FF)
#define ODN_PDP_GRPH1GALPHA_GRPH1GALPHA_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH1GALPHA_GRPH1GALPHA_SHIFT (0)
#define ODN_PDP_GRPH1GALPHA_GRPH1GALPHA_LENGTH (10)
#define ODN_PDP_GRPH1GALPHA_GRPH1GALPHA_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2GALPHA_OFFSET (0x00C4)

/* PDP, GRPH2GALPHA, GRPH2GALPHA
*/
#define ODN_PDP_GRPH2GALPHA_GRPH2GALPHA_MASK (0x000003FF)
#define ODN_PDP_GRPH2GALPHA_GRPH2GALPHA_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH2GALPHA_GRPH2GALPHA_SHIFT (0)
#define ODN_PDP_GRPH2GALPHA_GRPH2GALPHA_LENGTH (10)
#define ODN_PDP_GRPH2GALPHA_GRPH2GALPHA_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3GALPHA_OFFSET (0x00C8)

/* PDP, GRPH3GALPHA, GRPH3GALPHA
*/
#define ODN_PDP_GRPH3GALPHA_GRPH3GALPHA_MASK (0x000003FF)
#define ODN_PDP_GRPH3GALPHA_GRPH3GALPHA_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH3GALPHA_GRPH3GALPHA_SHIFT (0)
#define ODN_PDP_GRPH3GALPHA_GRPH3GALPHA_LENGTH (10)
#define ODN_PDP_GRPH3GALPHA_GRPH3GALPHA_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4GALPHA_OFFSET (0x00CC)

/* PDP, GRPH4GALPHA, GRPH4GALPHA
*/
#define ODN_PDP_GRPH4GALPHA_GRPH4GALPHA_MASK (0x000003FF)
#define ODN_PDP_GRPH4GALPHA_GRPH4GALPHA_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH4GALPHA_GRPH4GALPHA_SHIFT (0)
#define ODN_PDP_GRPH4GALPHA_GRPH4GALPHA_LENGTH (10)
#define ODN_PDP_GRPH4GALPHA_GRPH4GALPHA_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1GALPHA_OFFSET (0x00D0)

/* PDP, VID1GALPHA, VID1GALPHA
*/
#define ODN_PDP_VID1GALPHA_VID1GALPHA_MASK (0x000003FF)
#define ODN_PDP_VID1GALPHA_VID1GALPHA_LSBMASK (0x000003FF)
#define ODN_PDP_VID1GALPHA_VID1GALPHA_SHIFT (0)
#define ODN_PDP_VID1GALPHA_VID1GALPHA_LENGTH (10)
#define ODN_PDP_VID1GALPHA_VID1GALPHA_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2GALPHA_OFFSET (0x00D4)

/* PDP, VID2GALPHA, VID2GALPHA
*/
#define ODN_PDP_VID2GALPHA_VID2GALPHA_MASK (0x000003FF)
#define ODN_PDP_VID2GALPHA_VID2GALPHA_LSBMASK (0x000003FF)
#define ODN_PDP_VID2GALPHA_VID2GALPHA_SHIFT (0)
#define ODN_PDP_VID2GALPHA_VID2GALPHA_LENGTH (10)
#define ODN_PDP_VID2GALPHA_VID2GALPHA_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3GALPHA_OFFSET (0x00D8)

/* PDP, VID3GALPHA, VID3GALPHA
*/
#define ODN_PDP_VID3GALPHA_VID3GALPHA_MASK (0x000003FF)
#define ODN_PDP_VID3GALPHA_VID3GALPHA_LSBMASK (0x000003FF)
#define ODN_PDP_VID3GALPHA_VID3GALPHA_SHIFT (0)
#define ODN_PDP_VID3GALPHA_VID3GALPHA_LENGTH (10)
#define ODN_PDP_VID3GALPHA_VID3GALPHA_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4GALPHA_OFFSET (0x00DC)

/* PDP, VID4GALPHA, VID4GALPHA
*/
#define ODN_PDP_VID4GALPHA_VID4GALPHA_MASK (0x000003FF)
#define ODN_PDP_VID4GALPHA_VID4GALPHA_LSBMASK (0x000003FF)
#define ODN_PDP_VID4GALPHA_VID4GALPHA_SHIFT (0)
#define ODN_PDP_VID4GALPHA_VID4GALPHA_LENGTH (10)
#define ODN_PDP_VID4GALPHA_VID4GALPHA_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1CKEY_R_OFFSET (0x00E0)

/* PDP, GRPH1CKEY_R, GRPH1CKEY_R
*/
#define ODN_PDP_GRPH1CKEY_R_GRPH1CKEY_R_MASK (0x000003FF)
#define ODN_PDP_GRPH1CKEY_R_GRPH1CKEY_R_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH1CKEY_R_GRPH1CKEY_R_SHIFT (0)
#define ODN_PDP_GRPH1CKEY_R_GRPH1CKEY_R_LENGTH (10)
#define ODN_PDP_GRPH1CKEY_R_GRPH1CKEY_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1CKEY_GB_OFFSET (0x00E4)

/* PDP, GRPH1CKEY_GB, GRPH1CKEY_G
*/
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_G_MASK (0x03FF0000)
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_G_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_G_SHIFT (16)
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_G_LENGTH (10)
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_G_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1CKEY_GB, GRPH1CKEY_B
*/
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_B_MASK (0x000003FF)
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_B_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_B_SHIFT (0)
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_B_LENGTH (10)
#define ODN_PDP_GRPH1CKEY_GB_GRPH1CKEY_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2CKEY_R_OFFSET (0x00E8)

/* PDP, GRPH2CKEY_R, GRPH2CKEY_R
*/
#define ODN_PDP_GRPH2CKEY_R_GRPH2CKEY_R_MASK (0x000003FF)
#define ODN_PDP_GRPH2CKEY_R_GRPH2CKEY_R_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH2CKEY_R_GRPH2CKEY_R_SHIFT (0)
#define ODN_PDP_GRPH2CKEY_R_GRPH2CKEY_R_LENGTH (10)
#define ODN_PDP_GRPH2CKEY_R_GRPH2CKEY_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2CKEY_GB_OFFSET (0x00EC)

/* PDP, GRPH2CKEY_GB, GRPH2CKEY_G
*/
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_G_MASK (0x03FF0000)
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_G_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_G_SHIFT (16)
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_G_LENGTH (10)
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_G_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2CKEY_GB, GRPH2CKEY_B
*/
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_B_MASK (0x000003FF)
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_B_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_B_SHIFT (0)
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_B_LENGTH (10)
#define ODN_PDP_GRPH2CKEY_GB_GRPH2CKEY_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3CKEY_R_OFFSET (0x00F0)

/* PDP, GRPH3CKEY_R, GRPH3CKEY_R
*/
#define ODN_PDP_GRPH3CKEY_R_GRPH3CKEY_R_MASK (0x000003FF)
#define ODN_PDP_GRPH3CKEY_R_GRPH3CKEY_R_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH3CKEY_R_GRPH3CKEY_R_SHIFT (0)
#define ODN_PDP_GRPH3CKEY_R_GRPH3CKEY_R_LENGTH (10)
#define ODN_PDP_GRPH3CKEY_R_GRPH3CKEY_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3CKEY_GB_OFFSET (0x00F4)

/* PDP, GRPH3CKEY_GB, GRPH3CKEY_G
*/
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_G_MASK (0x03FF0000)
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_G_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_G_SHIFT (16)
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_G_LENGTH (10)
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_G_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3CKEY_GB, GRPH3CKEY_B
*/
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_B_MASK (0x000003FF)
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_B_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_B_SHIFT (0)
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_B_LENGTH (10)
#define ODN_PDP_GRPH3CKEY_GB_GRPH3CKEY_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4CKEY_R_OFFSET (0x00F8)

/* PDP, GRPH4CKEY_R, GRPH4CKEY_R
*/
#define ODN_PDP_GRPH4CKEY_R_GRPH4CKEY_R_MASK (0x000003FF)
#define ODN_PDP_GRPH4CKEY_R_GRPH4CKEY_R_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH4CKEY_R_GRPH4CKEY_R_SHIFT (0)
#define ODN_PDP_GRPH4CKEY_R_GRPH4CKEY_R_LENGTH (10)
#define ODN_PDP_GRPH4CKEY_R_GRPH4CKEY_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4CKEY_GB_OFFSET (0x00FC)

/* PDP, GRPH4CKEY_GB, GRPH4CKEY_G
*/
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_G_MASK (0x03FF0000)
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_G_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_G_SHIFT (16)
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_G_LENGTH (10)
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_G_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4CKEY_GB, GRPH4CKEY_B
*/
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_B_MASK (0x000003FF)
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_B_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_B_SHIFT (0)
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_B_LENGTH (10)
#define ODN_PDP_GRPH4CKEY_GB_GRPH4CKEY_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1CKEY_R_OFFSET (0x0100)

/* PDP, VID1CKEY_R, VID1CKEY_R
*/
#define ODN_PDP_VID1CKEY_R_VID1CKEY_R_MASK (0x000003FF)
#define ODN_PDP_VID1CKEY_R_VID1CKEY_R_LSBMASK (0x000003FF)
#define ODN_PDP_VID1CKEY_R_VID1CKEY_R_SHIFT (0)
#define ODN_PDP_VID1CKEY_R_VID1CKEY_R_LENGTH (10)
#define ODN_PDP_VID1CKEY_R_VID1CKEY_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1CKEY_GB_OFFSET (0x0104)

/* PDP, VID1CKEY_GB, VID1CKEY_G
*/
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_G_MASK (0x03FF0000)
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_G_LSBMASK (0x000003FF)
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_G_SHIFT (16)
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_G_LENGTH (10)
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_G_SIGNED_FIELD IMG_FALSE

/* PDP, VID1CKEY_GB, VID1CKEY_B
*/
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_B_MASK (0x000003FF)
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_B_LSBMASK (0x000003FF)
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_B_SHIFT (0)
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_B_LENGTH (10)
#define ODN_PDP_VID1CKEY_GB_VID1CKEY_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2CKEY_R_OFFSET (0x0108)

/* PDP, VID2CKEY_R, VID2CKEY_R
*/
#define ODN_PDP_VID2CKEY_R_VID2CKEY_R_MASK (0x000003FF)
#define ODN_PDP_VID2CKEY_R_VID2CKEY_R_LSBMASK (0x000003FF)
#define ODN_PDP_VID2CKEY_R_VID2CKEY_R_SHIFT (0)
#define ODN_PDP_VID2CKEY_R_VID2CKEY_R_LENGTH (10)
#define ODN_PDP_VID2CKEY_R_VID2CKEY_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2CKEY_GB_OFFSET (0x010C)

/* PDP, VID2CKEY_GB, VID2CKEY_G
*/
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_G_MASK (0x03FF0000)
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_G_LSBMASK (0x000003FF)
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_G_SHIFT (16)
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_G_LENGTH (10)
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_G_SIGNED_FIELD IMG_FALSE

/* PDP, VID2CKEY_GB, VID2CKEY_B
*/
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_B_MASK (0x000003FF)
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_B_LSBMASK (0x000003FF)
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_B_SHIFT (0)
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_B_LENGTH (10)
#define ODN_PDP_VID2CKEY_GB_VID2CKEY_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3CKEY_R_OFFSET (0x0110)

/* PDP, VID3CKEY_R, VID3CKEY_R
*/
#define ODN_PDP_VID3CKEY_R_VID3CKEY_R_MASK (0x000003FF)
#define ODN_PDP_VID3CKEY_R_VID3CKEY_R_LSBMASK (0x000003FF)
#define ODN_PDP_VID3CKEY_R_VID3CKEY_R_SHIFT (0)
#define ODN_PDP_VID3CKEY_R_VID3CKEY_R_LENGTH (10)
#define ODN_PDP_VID3CKEY_R_VID3CKEY_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3CKEY_GB_OFFSET (0x0114)

/* PDP, VID3CKEY_GB, VID3CKEY_G
*/
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_G_MASK (0x03FF0000)
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_G_LSBMASK (0x000003FF)
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_G_SHIFT (16)
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_G_LENGTH (10)
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_G_SIGNED_FIELD IMG_FALSE

/* PDP, VID3CKEY_GB, VID3CKEY_B
*/
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_B_MASK (0x000003FF)
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_B_LSBMASK (0x000003FF)
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_B_SHIFT (0)
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_B_LENGTH (10)
#define ODN_PDP_VID3CKEY_GB_VID3CKEY_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4CKEY_R_OFFSET (0x0118)

/* PDP, VID4CKEY_R, VID4CKEY_R
*/
#define ODN_PDP_VID4CKEY_R_VID4CKEY_R_MASK (0x000003FF)
#define ODN_PDP_VID4CKEY_R_VID4CKEY_R_LSBMASK (0x000003FF)
#define ODN_PDP_VID4CKEY_R_VID4CKEY_R_SHIFT (0)
#define ODN_PDP_VID4CKEY_R_VID4CKEY_R_LENGTH (10)
#define ODN_PDP_VID4CKEY_R_VID4CKEY_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4CKEY_GB_OFFSET (0x011C)

/* PDP, VID4CKEY_GB, VID4CKEY_G
*/
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_G_MASK (0x03FF0000)
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_G_LSBMASK (0x000003FF)
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_G_SHIFT (16)
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_G_LENGTH (10)
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_G_SIGNED_FIELD IMG_FALSE

/* PDP, VID4CKEY_GB, VID4CKEY_B
*/
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_B_MASK (0x000003FF)
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_B_LSBMASK (0x000003FF)
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_B_SHIFT (0)
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_B_LENGTH (10)
#define ODN_PDP_VID4CKEY_GB_VID4CKEY_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1BLND2_R_OFFSET (0x0120)

/* PDP, GRPH1BLND2_R, GRPH1PIXDBL
*/
#define ODN_PDP_GRPH1BLND2_R_GRPH1PIXDBL_MASK (0x80000000)
#define ODN_PDP_GRPH1BLND2_R_GRPH1PIXDBL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1BLND2_R_GRPH1PIXDBL_SHIFT (31)
#define ODN_PDP_GRPH1BLND2_R_GRPH1PIXDBL_LENGTH (1)
#define ODN_PDP_GRPH1BLND2_R_GRPH1PIXDBL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1BLND2_R, GRPH1LINDBL
*/
#define ODN_PDP_GRPH1BLND2_R_GRPH1LINDBL_MASK (0x20000000)
#define ODN_PDP_GRPH1BLND2_R_GRPH1LINDBL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1BLND2_R_GRPH1LINDBL_SHIFT (29)
#define ODN_PDP_GRPH1BLND2_R_GRPH1LINDBL_LENGTH (1)
#define ODN_PDP_GRPH1BLND2_R_GRPH1LINDBL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1BLND2_R, GRPH1CKEYMASK_R
*/
#define ODN_PDP_GRPH1BLND2_R_GRPH1CKEYMASK_R_MASK (0x000003FF)
#define ODN_PDP_GRPH1BLND2_R_GRPH1CKEYMASK_R_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH1BLND2_R_GRPH1CKEYMASK_R_SHIFT (0)
#define ODN_PDP_GRPH1BLND2_R_GRPH1CKEYMASK_R_LENGTH (10)
#define ODN_PDP_GRPH1BLND2_R_GRPH1CKEYMASK_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1BLND2_GB_OFFSET (0x0124)

/* PDP, GRPH1BLND2_GB, GRPH1CKEYMASK_G
*/
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_G_MASK (0x03FF0000)
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_G_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_G_SHIFT (16)
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_G_LENGTH (10)
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_G_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1BLND2_GB, GRPH1CKEYMASK_B
*/
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_B_MASK (0x000003FF)
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_B_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_B_SHIFT (0)
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_B_LENGTH (10)
#define ODN_PDP_GRPH1BLND2_GB_GRPH1CKEYMASK_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2BLND2_R_OFFSET (0x0128)

/* PDP, GRPH2BLND2_R, GRPH2PIXDBL
*/
#define ODN_PDP_GRPH2BLND2_R_GRPH2PIXDBL_MASK (0x80000000)
#define ODN_PDP_GRPH2BLND2_R_GRPH2PIXDBL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2BLND2_R_GRPH2PIXDBL_SHIFT (31)
#define ODN_PDP_GRPH2BLND2_R_GRPH2PIXDBL_LENGTH (1)
#define ODN_PDP_GRPH2BLND2_R_GRPH2PIXDBL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2BLND2_R, GRPH2LINDBL
*/
#define ODN_PDP_GRPH2BLND2_R_GRPH2LINDBL_MASK (0x20000000)
#define ODN_PDP_GRPH2BLND2_R_GRPH2LINDBL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2BLND2_R_GRPH2LINDBL_SHIFT (29)
#define ODN_PDP_GRPH2BLND2_R_GRPH2LINDBL_LENGTH (1)
#define ODN_PDP_GRPH2BLND2_R_GRPH2LINDBL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2BLND2_R, GRPH2CKEYMASK_R
*/
#define ODN_PDP_GRPH2BLND2_R_GRPH2CKEYMASK_R_MASK (0x000003FF)
#define ODN_PDP_GRPH2BLND2_R_GRPH2CKEYMASK_R_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH2BLND2_R_GRPH2CKEYMASK_R_SHIFT (0)
#define ODN_PDP_GRPH2BLND2_R_GRPH2CKEYMASK_R_LENGTH (10)
#define ODN_PDP_GRPH2BLND2_R_GRPH2CKEYMASK_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2BLND2_GB_OFFSET (0x012C)

/* PDP, GRPH2BLND2_GB, GRPH2CKEYMASK_G
*/
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_G_MASK (0x03FF0000)
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_G_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_G_SHIFT (16)
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_G_LENGTH (10)
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_G_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2BLND2_GB, GRPH2CKEYMASK_B
*/
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_B_MASK (0x000003FF)
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_B_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_B_SHIFT (0)
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_B_LENGTH (10)
#define ODN_PDP_GRPH2BLND2_GB_GRPH2CKEYMASK_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3BLND2_R_OFFSET (0x0130)

/* PDP, GRPH3BLND2_R, GRPH3PIXDBL
*/
#define ODN_PDP_GRPH3BLND2_R_GRPH3PIXDBL_MASK (0x80000000)
#define ODN_PDP_GRPH3BLND2_R_GRPH3PIXDBL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3BLND2_R_GRPH3PIXDBL_SHIFT (31)
#define ODN_PDP_GRPH3BLND2_R_GRPH3PIXDBL_LENGTH (1)
#define ODN_PDP_GRPH3BLND2_R_GRPH3PIXDBL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3BLND2_R, GRPH3LINDBL
*/
#define ODN_PDP_GRPH3BLND2_R_GRPH3LINDBL_MASK (0x20000000)
#define ODN_PDP_GRPH3BLND2_R_GRPH3LINDBL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3BLND2_R_GRPH3LINDBL_SHIFT (29)
#define ODN_PDP_GRPH3BLND2_R_GRPH3LINDBL_LENGTH (1)
#define ODN_PDP_GRPH3BLND2_R_GRPH3LINDBL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3BLND2_R, GRPH3CKEYMASK_R
*/
#define ODN_PDP_GRPH3BLND2_R_GRPH3CKEYMASK_R_MASK (0x000003FF)
#define ODN_PDP_GRPH3BLND2_R_GRPH3CKEYMASK_R_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH3BLND2_R_GRPH3CKEYMASK_R_SHIFT (0)
#define ODN_PDP_GRPH3BLND2_R_GRPH3CKEYMASK_R_LENGTH (10)
#define ODN_PDP_GRPH3BLND2_R_GRPH3CKEYMASK_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3BLND2_GB_OFFSET (0x0134)

/* PDP, GRPH3BLND2_GB, GRPH3CKEYMASK_G
*/
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_G_MASK (0x03FF0000)
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_G_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_G_SHIFT (16)
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_G_LENGTH (10)
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_G_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3BLND2_GB, GRPH3CKEYMASK_B
*/
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_B_MASK (0x000003FF)
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_B_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_B_SHIFT (0)
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_B_LENGTH (10)
#define ODN_PDP_GRPH3BLND2_GB_GRPH3CKEYMASK_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4BLND2_R_OFFSET (0x0138)

/* PDP, GRPH4BLND2_R, GRPH4PIXDBL
*/
#define ODN_PDP_GRPH4BLND2_R_GRPH4PIXDBL_MASK (0x80000000)
#define ODN_PDP_GRPH4BLND2_R_GRPH4PIXDBL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4BLND2_R_GRPH4PIXDBL_SHIFT (31)
#define ODN_PDP_GRPH4BLND2_R_GRPH4PIXDBL_LENGTH (1)
#define ODN_PDP_GRPH4BLND2_R_GRPH4PIXDBL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4BLND2_R, GRPH4LINDBL
*/
#define ODN_PDP_GRPH4BLND2_R_GRPH4LINDBL_MASK (0x20000000)
#define ODN_PDP_GRPH4BLND2_R_GRPH4LINDBL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4BLND2_R_GRPH4LINDBL_SHIFT (29)
#define ODN_PDP_GRPH4BLND2_R_GRPH4LINDBL_LENGTH (1)
#define ODN_PDP_GRPH4BLND2_R_GRPH4LINDBL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4BLND2_R, GRPH4CKEYMASK_R
*/
#define ODN_PDP_GRPH4BLND2_R_GRPH4CKEYMASK_R_MASK (0x000003FF)
#define ODN_PDP_GRPH4BLND2_R_GRPH4CKEYMASK_R_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH4BLND2_R_GRPH4CKEYMASK_R_SHIFT (0)
#define ODN_PDP_GRPH4BLND2_R_GRPH4CKEYMASK_R_LENGTH (10)
#define ODN_PDP_GRPH4BLND2_R_GRPH4CKEYMASK_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4BLND2_GB_OFFSET (0x013C)

/* PDP, GRPH4BLND2_GB, GRPH4CKEYMASK_G
*/
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_G_MASK (0x03FF0000)
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_G_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_G_SHIFT (16)
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_G_LENGTH (10)
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_G_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4BLND2_GB, GRPH4CKEYMASK_B
*/
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_B_MASK (0x000003FF)
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_B_LSBMASK (0x000003FF)
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_B_SHIFT (0)
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_B_LENGTH (10)
#define ODN_PDP_GRPH4BLND2_GB_GRPH4CKEYMASK_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1BLND2_R_OFFSET (0x0140)

/* PDP, VID1BLND2_R, VID1CKEYMASK_R
*/
#define ODN_PDP_VID1BLND2_R_VID1CKEYMASK_R_MASK (0x000003FF)
#define ODN_PDP_VID1BLND2_R_VID1CKEYMASK_R_LSBMASK (0x000003FF)
#define ODN_PDP_VID1BLND2_R_VID1CKEYMASK_R_SHIFT (0)
#define ODN_PDP_VID1BLND2_R_VID1CKEYMASK_R_LENGTH (10)
#define ODN_PDP_VID1BLND2_R_VID1CKEYMASK_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1BLND2_GB_OFFSET (0x0144)

/* PDP, VID1BLND2_GB, VID1CKEYMASK_G
*/
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_G_MASK (0x03FF0000)
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_G_LSBMASK (0x000003FF)
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_G_SHIFT (16)
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_G_LENGTH (10)
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_G_SIGNED_FIELD IMG_FALSE

/* PDP, VID1BLND2_GB, VID1CKEYMASK_B
*/
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_B_MASK (0x000003FF)
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_B_LSBMASK (0x000003FF)
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_B_SHIFT (0)
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_B_LENGTH (10)
#define ODN_PDP_VID1BLND2_GB_VID1CKEYMASK_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2BLND2_R_OFFSET (0x0148)

/* PDP, VID2BLND2_R, VID2CKEYMASK_R
*/
#define ODN_PDP_VID2BLND2_R_VID2CKEYMASK_R_MASK (0x000003FF)
#define ODN_PDP_VID2BLND2_R_VID2CKEYMASK_R_LSBMASK (0x000003FF)
#define ODN_PDP_VID2BLND2_R_VID2CKEYMASK_R_SHIFT (0)
#define ODN_PDP_VID2BLND2_R_VID2CKEYMASK_R_LENGTH (10)
#define ODN_PDP_VID2BLND2_R_VID2CKEYMASK_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2BLND2_GB_OFFSET (0x014C)

/* PDP, VID2BLND2_GB, VID2CKEYMASK_G
*/
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_G_MASK (0x03FF0000)
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_G_LSBMASK (0x000003FF)
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_G_SHIFT (16)
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_G_LENGTH (10)
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_G_SIGNED_FIELD IMG_FALSE

/* PDP, VID2BLND2_GB, VID2CKEYMASK_B
*/
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_B_MASK (0x000003FF)
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_B_LSBMASK (0x000003FF)
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_B_SHIFT (0)
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_B_LENGTH (10)
#define ODN_PDP_VID2BLND2_GB_VID2CKEYMASK_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3BLND2_R_OFFSET (0x0150)

/* PDP, VID3BLND2_R, VID3CKEYMASK_R
*/
#define ODN_PDP_VID3BLND2_R_VID3CKEYMASK_R_MASK (0x000003FF)
#define ODN_PDP_VID3BLND2_R_VID3CKEYMASK_R_LSBMASK (0x000003FF)
#define ODN_PDP_VID3BLND2_R_VID3CKEYMASK_R_SHIFT (0)
#define ODN_PDP_VID3BLND2_R_VID3CKEYMASK_R_LENGTH (10)
#define ODN_PDP_VID3BLND2_R_VID3CKEYMASK_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3BLND2_GB_OFFSET (0x0154)

/* PDP, VID3BLND2_GB, VID3CKEYMASK_G
*/
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_G_MASK (0x03FF0000)
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_G_LSBMASK (0x000003FF)
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_G_SHIFT (16)
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_G_LENGTH (10)
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_G_SIGNED_FIELD IMG_FALSE

/* PDP, VID3BLND2_GB, VID3CKEYMASK_B
*/
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_B_MASK (0x000003FF)
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_B_LSBMASK (0x000003FF)
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_B_SHIFT (0)
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_B_LENGTH (10)
#define ODN_PDP_VID3BLND2_GB_VID3CKEYMASK_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4BLND2_R_OFFSET (0x0158)

/* PDP, VID4BLND2_R, VID4CKEYMASK_R
*/
#define ODN_PDP_VID4BLND2_R_VID4CKEYMASK_R_MASK (0x000003FF)
#define ODN_PDP_VID4BLND2_R_VID4CKEYMASK_R_LSBMASK (0x000003FF)
#define ODN_PDP_VID4BLND2_R_VID4CKEYMASK_R_SHIFT (0)
#define ODN_PDP_VID4BLND2_R_VID4CKEYMASK_R_LENGTH (10)
#define ODN_PDP_VID4BLND2_R_VID4CKEYMASK_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4BLND2_GB_OFFSET (0x015C)

/* PDP, VID4BLND2_GB, VID4CKEYMASK_G
*/
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_G_MASK (0x03FF0000)
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_G_LSBMASK (0x000003FF)
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_G_SHIFT (16)
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_G_LENGTH (10)
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_G_SIGNED_FIELD IMG_FALSE

/* PDP, VID4BLND2_GB, VID4CKEYMASK_B
*/
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_B_MASK (0x000003FF)
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_B_LSBMASK (0x000003FF)
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_B_SHIFT (0)
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_B_LENGTH (10)
#define ODN_PDP_VID4BLND2_GB_VID4CKEYMASK_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1INTERLEAVE_CTRL_OFFSET (0x0160)

/* PDP, GRPH1INTERLEAVE_CTRL, GRPH1INTFIELD
*/
#define ODN_PDP_GRPH1INTERLEAVE_CTRL_GRPH1INTFIELD_MASK (0x00000001)
#define ODN_PDP_GRPH1INTERLEAVE_CTRL_GRPH1INTFIELD_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1INTERLEAVE_CTRL_GRPH1INTFIELD_SHIFT (0)
#define ODN_PDP_GRPH1INTERLEAVE_CTRL_GRPH1INTFIELD_LENGTH (1)
#define ODN_PDP_GRPH1INTERLEAVE_CTRL_GRPH1INTFIELD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2INTERLEAVE_CTRL_OFFSET (0x0164)

/* PDP, GRPH2INTERLEAVE_CTRL, GRPH2INTFIELD
*/
#define ODN_PDP_GRPH2INTERLEAVE_CTRL_GRPH2INTFIELD_MASK (0x00000001)
#define ODN_PDP_GRPH2INTERLEAVE_CTRL_GRPH2INTFIELD_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2INTERLEAVE_CTRL_GRPH2INTFIELD_SHIFT (0)
#define ODN_PDP_GRPH2INTERLEAVE_CTRL_GRPH2INTFIELD_LENGTH (1)
#define ODN_PDP_GRPH2INTERLEAVE_CTRL_GRPH2INTFIELD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3INTERLEAVE_CTRL_OFFSET (0x0168)

/* PDP, GRPH3INTERLEAVE_CTRL, GRPH3INTFIELD
*/
#define ODN_PDP_GRPH3INTERLEAVE_CTRL_GRPH3INTFIELD_MASK (0x00000001)
#define ODN_PDP_GRPH3INTERLEAVE_CTRL_GRPH3INTFIELD_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3INTERLEAVE_CTRL_GRPH3INTFIELD_SHIFT (0)
#define ODN_PDP_GRPH3INTERLEAVE_CTRL_GRPH3INTFIELD_LENGTH (1)
#define ODN_PDP_GRPH3INTERLEAVE_CTRL_GRPH3INTFIELD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4INTERLEAVE_CTRL_OFFSET (0x016C)

/* PDP, GRPH4INTERLEAVE_CTRL, GRPH4INTFIELD
*/
#define ODN_PDP_GRPH4INTERLEAVE_CTRL_GRPH4INTFIELD_MASK (0x00000001)
#define ODN_PDP_GRPH4INTERLEAVE_CTRL_GRPH4INTFIELD_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4INTERLEAVE_CTRL_GRPH4INTFIELD_SHIFT (0)
#define ODN_PDP_GRPH4INTERLEAVE_CTRL_GRPH4INTFIELD_LENGTH (1)
#define ODN_PDP_GRPH4INTERLEAVE_CTRL_GRPH4INTFIELD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1INTERLEAVE_CTRL_OFFSET (0x0170)

/* PDP, VID1INTERLEAVE_CTRL, VID1INTFIELD
*/
#define ODN_PDP_VID1INTERLEAVE_CTRL_VID1INTFIELD_MASK (0x00000001)
#define ODN_PDP_VID1INTERLEAVE_CTRL_VID1INTFIELD_LSBMASK (0x00000001)
#define ODN_PDP_VID1INTERLEAVE_CTRL_VID1INTFIELD_SHIFT (0)
#define ODN_PDP_VID1INTERLEAVE_CTRL_VID1INTFIELD_LENGTH (1)
#define ODN_PDP_VID1INTERLEAVE_CTRL_VID1INTFIELD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2INTERLEAVE_CTRL_OFFSET (0x0174)

/* PDP, VID2INTERLEAVE_CTRL, VID2INTFIELD
*/
#define ODN_PDP_VID2INTERLEAVE_CTRL_VID2INTFIELD_MASK (0x00000001)
#define ODN_PDP_VID2INTERLEAVE_CTRL_VID2INTFIELD_LSBMASK (0x00000001)
#define ODN_PDP_VID2INTERLEAVE_CTRL_VID2INTFIELD_SHIFT (0)
#define ODN_PDP_VID2INTERLEAVE_CTRL_VID2INTFIELD_LENGTH (1)
#define ODN_PDP_VID2INTERLEAVE_CTRL_VID2INTFIELD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3INTERLEAVE_CTRL_OFFSET (0x0178)

/* PDP, VID3INTERLEAVE_CTRL, VID3INTFIELD
*/
#define ODN_PDP_VID3INTERLEAVE_CTRL_VID3INTFIELD_MASK (0x00000001)
#define ODN_PDP_VID3INTERLEAVE_CTRL_VID3INTFIELD_LSBMASK (0x00000001)
#define ODN_PDP_VID3INTERLEAVE_CTRL_VID3INTFIELD_SHIFT (0)
#define ODN_PDP_VID3INTERLEAVE_CTRL_VID3INTFIELD_LENGTH (1)
#define ODN_PDP_VID3INTERLEAVE_CTRL_VID3INTFIELD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4INTERLEAVE_CTRL_OFFSET (0x017C)

/* PDP, VID4INTERLEAVE_CTRL, VID4INTFIELD
*/
#define ODN_PDP_VID4INTERLEAVE_CTRL_VID4INTFIELD_MASK (0x00000001)
#define ODN_PDP_VID4INTERLEAVE_CTRL_VID4INTFIELD_LSBMASK (0x00000001)
#define ODN_PDP_VID4INTERLEAVE_CTRL_VID4INTFIELD_SHIFT (0)
#define ODN_PDP_VID4INTERLEAVE_CTRL_VID4INTFIELD_LENGTH (1)
#define ODN_PDP_VID4INTERLEAVE_CTRL_VID4INTFIELD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1BASEADDR_OFFSET (0x0180)

/* PDP, GRPH1BASEADDR, GRPH1BASEADDR
*/
#define ODN_PDP_GRPH1BASEADDR_GRPH1BASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_GRPH1BASEADDR_GRPH1BASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_GRPH1BASEADDR_GRPH1BASEADDR_SHIFT (5)
#define ODN_PDP_GRPH1BASEADDR_GRPH1BASEADDR_LENGTH (27)
#define ODN_PDP_GRPH1BASEADDR_GRPH1BASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2BASEADDR_OFFSET (0x0184)

/* PDP, GRPH2BASEADDR, GRPH2BASEADDR
*/
#define ODN_PDP_GRPH2BASEADDR_GRPH2BASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_GRPH2BASEADDR_GRPH2BASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_GRPH2BASEADDR_GRPH2BASEADDR_SHIFT (5)
#define ODN_PDP_GRPH2BASEADDR_GRPH2BASEADDR_LENGTH (27)
#define ODN_PDP_GRPH2BASEADDR_GRPH2BASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3BASEADDR_OFFSET (0x0188)

/* PDP, GRPH3BASEADDR, GRPH3BASEADDR
*/
#define ODN_PDP_GRPH3BASEADDR_GRPH3BASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_GRPH3BASEADDR_GRPH3BASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_GRPH3BASEADDR_GRPH3BASEADDR_SHIFT (5)
#define ODN_PDP_GRPH3BASEADDR_GRPH3BASEADDR_LENGTH (27)
#define ODN_PDP_GRPH3BASEADDR_GRPH3BASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4BASEADDR_OFFSET (0x018C)

/* PDP, GRPH4BASEADDR, GRPH4BASEADDR
*/
#define ODN_PDP_GRPH4BASEADDR_GRPH4BASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_GRPH4BASEADDR_GRPH4BASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_GRPH4BASEADDR_GRPH4BASEADDR_SHIFT (5)
#define ODN_PDP_GRPH4BASEADDR_GRPH4BASEADDR_LENGTH (27)
#define ODN_PDP_GRPH4BASEADDR_GRPH4BASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1BASEADDR_OFFSET (0x0190)

/* PDP, VID1BASEADDR, VID1BASEADDR
*/
#define ODN_PDP_VID1BASEADDR_VID1BASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID1BASEADDR_VID1BASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID1BASEADDR_VID1BASEADDR_SHIFT (5)
#define ODN_PDP_VID1BASEADDR_VID1BASEADDR_LENGTH (27)
#define ODN_PDP_VID1BASEADDR_VID1BASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2BASEADDR_OFFSET (0x0194)

/* PDP, VID2BASEADDR, VID2BASEADDR
*/
#define ODN_PDP_VID2BASEADDR_VID2BASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID2BASEADDR_VID2BASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID2BASEADDR_VID2BASEADDR_SHIFT (5)
#define ODN_PDP_VID2BASEADDR_VID2BASEADDR_LENGTH (27)
#define ODN_PDP_VID2BASEADDR_VID2BASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3BASEADDR_OFFSET (0x0198)

/* PDP, VID3BASEADDR, VID3BASEADDR
*/
#define ODN_PDP_VID3BASEADDR_VID3BASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID3BASEADDR_VID3BASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID3BASEADDR_VID3BASEADDR_SHIFT (5)
#define ODN_PDP_VID3BASEADDR_VID3BASEADDR_LENGTH (27)
#define ODN_PDP_VID3BASEADDR_VID3BASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4BASEADDR_OFFSET (0x019C)

/* PDP, VID4BASEADDR, VID4BASEADDR
*/
#define ODN_PDP_VID4BASEADDR_VID4BASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID4BASEADDR_VID4BASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID4BASEADDR_VID4BASEADDR_SHIFT (5)
#define ODN_PDP_VID4BASEADDR_VID4BASEADDR_LENGTH (27)
#define ODN_PDP_VID4BASEADDR_VID4BASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1UBASEADDR_OFFSET (0x01B0)

/* PDP, VID1UBASEADDR, VID1UBASEADDR
*/
#define ODN_PDP_VID1UBASEADDR_VID1UBASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID1UBASEADDR_VID1UBASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID1UBASEADDR_VID1UBASEADDR_SHIFT (5)
#define ODN_PDP_VID1UBASEADDR_VID1UBASEADDR_LENGTH (27)
#define ODN_PDP_VID1UBASEADDR_VID1UBASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2UBASEADDR_OFFSET (0x01B4)

/* PDP, VID2UBASEADDR, VID2UBASEADDR
*/
#define ODN_PDP_VID2UBASEADDR_VID2UBASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID2UBASEADDR_VID2UBASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID2UBASEADDR_VID2UBASEADDR_SHIFT (5)
#define ODN_PDP_VID2UBASEADDR_VID2UBASEADDR_LENGTH (27)
#define ODN_PDP_VID2UBASEADDR_VID2UBASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3UBASEADDR_OFFSET (0x01B8)

/* PDP, VID3UBASEADDR, VID3UBASEADDR
*/
#define ODN_PDP_VID3UBASEADDR_VID3UBASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID3UBASEADDR_VID3UBASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID3UBASEADDR_VID3UBASEADDR_SHIFT (5)
#define ODN_PDP_VID3UBASEADDR_VID3UBASEADDR_LENGTH (27)
#define ODN_PDP_VID3UBASEADDR_VID3UBASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4UBASEADDR_OFFSET (0x01BC)

/* PDP, VID4UBASEADDR, VID4UBASEADDR
*/
#define ODN_PDP_VID4UBASEADDR_VID4UBASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID4UBASEADDR_VID4UBASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID4UBASEADDR_VID4UBASEADDR_SHIFT (5)
#define ODN_PDP_VID4UBASEADDR_VID4UBASEADDR_LENGTH (27)
#define ODN_PDP_VID4UBASEADDR_VID4UBASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VBASEADDR_OFFSET (0x01D0)

/* PDP, VID1VBASEADDR, VID1VBASEADDR
*/
#define ODN_PDP_VID1VBASEADDR_VID1VBASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID1VBASEADDR_VID1VBASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID1VBASEADDR_VID1VBASEADDR_SHIFT (5)
#define ODN_PDP_VID1VBASEADDR_VID1VBASEADDR_LENGTH (27)
#define ODN_PDP_VID1VBASEADDR_VID1VBASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VBASEADDR_OFFSET (0x01D4)

/* PDP, VID2VBASEADDR, VID2VBASEADDR
*/
#define ODN_PDP_VID2VBASEADDR_VID2VBASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID2VBASEADDR_VID2VBASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID2VBASEADDR_VID2VBASEADDR_SHIFT (5)
#define ODN_PDP_VID2VBASEADDR_VID2VBASEADDR_LENGTH (27)
#define ODN_PDP_VID2VBASEADDR_VID2VBASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VBASEADDR_OFFSET (0x01D8)

/* PDP, VID3VBASEADDR, VID3VBASEADDR
*/
#define ODN_PDP_VID3VBASEADDR_VID3VBASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID3VBASEADDR_VID3VBASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID3VBASEADDR_VID3VBASEADDR_SHIFT (5)
#define ODN_PDP_VID3VBASEADDR_VID3VBASEADDR_LENGTH (27)
#define ODN_PDP_VID3VBASEADDR_VID3VBASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VBASEADDR_OFFSET (0x01DC)

/* PDP, VID4VBASEADDR, VID4VBASEADDR
*/
#define ODN_PDP_VID4VBASEADDR_VID4VBASEADDR_MASK (0xFFFFFFE0)
#define ODN_PDP_VID4VBASEADDR_VID4VBASEADDR_LSBMASK (0x07FFFFFF)
#define ODN_PDP_VID4VBASEADDR_VID4VBASEADDR_SHIFT (5)
#define ODN_PDP_VID4VBASEADDR_VID4VBASEADDR_LENGTH (27)
#define ODN_PDP_VID4VBASEADDR_VID4VBASEADDR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1POSTSKIPCTRL_OFFSET (0x0230)

/* PDP, VID1POSTSKIPCTRL, VID1HPOSTCLIP
*/
#define ODN_PDP_VID1POSTSKIPCTRL_VID1HPOSTCLIP_MASK (0x007F0000)
#define ODN_PDP_VID1POSTSKIPCTRL_VID1HPOSTCLIP_LSBMASK (0x0000007F)
#define ODN_PDP_VID1POSTSKIPCTRL_VID1HPOSTCLIP_SHIFT (16)
#define ODN_PDP_VID1POSTSKIPCTRL_VID1HPOSTCLIP_LENGTH (7)
#define ODN_PDP_VID1POSTSKIPCTRL_VID1HPOSTCLIP_SIGNED_FIELD IMG_FALSE

/* PDP, VID1POSTSKIPCTRL, VID1VPOSTCLIP
*/
#define ODN_PDP_VID1POSTSKIPCTRL_VID1VPOSTCLIP_MASK (0x0000003F)
#define ODN_PDP_VID1POSTSKIPCTRL_VID1VPOSTCLIP_LSBMASK (0x0000003F)
#define ODN_PDP_VID1POSTSKIPCTRL_VID1VPOSTCLIP_SHIFT (0)
#define ODN_PDP_VID1POSTSKIPCTRL_VID1VPOSTCLIP_LENGTH (6)
#define ODN_PDP_VID1POSTSKIPCTRL_VID1VPOSTCLIP_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2POSTSKIPCTRL_OFFSET (0x0234)

/* PDP, VID2POSTSKIPCTRL, VID2HPOSTCLIP
*/
#define ODN_PDP_VID2POSTSKIPCTRL_VID2HPOSTCLIP_MASK (0x007F0000)
#define ODN_PDP_VID2POSTSKIPCTRL_VID2HPOSTCLIP_LSBMASK (0x0000007F)
#define ODN_PDP_VID2POSTSKIPCTRL_VID2HPOSTCLIP_SHIFT (16)
#define ODN_PDP_VID2POSTSKIPCTRL_VID2HPOSTCLIP_LENGTH (7)
#define ODN_PDP_VID2POSTSKIPCTRL_VID2HPOSTCLIP_SIGNED_FIELD IMG_FALSE

/* PDP, VID2POSTSKIPCTRL, VID2VPOSTCLIP
*/
#define ODN_PDP_VID2POSTSKIPCTRL_VID2VPOSTCLIP_MASK (0x0000003F)
#define ODN_PDP_VID2POSTSKIPCTRL_VID2VPOSTCLIP_LSBMASK (0x0000003F)
#define ODN_PDP_VID2POSTSKIPCTRL_VID2VPOSTCLIP_SHIFT (0)
#define ODN_PDP_VID2POSTSKIPCTRL_VID2VPOSTCLIP_LENGTH (6)
#define ODN_PDP_VID2POSTSKIPCTRL_VID2VPOSTCLIP_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3POSTSKIPCTRL_OFFSET (0x0238)

/* PDP, VID3POSTSKIPCTRL, VID3HPOSTCLIP
*/
#define ODN_PDP_VID3POSTSKIPCTRL_VID3HPOSTCLIP_MASK (0x007F0000)
#define ODN_PDP_VID3POSTSKIPCTRL_VID3HPOSTCLIP_LSBMASK (0x0000007F)
#define ODN_PDP_VID3POSTSKIPCTRL_VID3HPOSTCLIP_SHIFT (16)
#define ODN_PDP_VID3POSTSKIPCTRL_VID3HPOSTCLIP_LENGTH (7)
#define ODN_PDP_VID3POSTSKIPCTRL_VID3HPOSTCLIP_SIGNED_FIELD IMG_FALSE

/* PDP, VID3POSTSKIPCTRL, VID3VPOSTCLIP
*/
#define ODN_PDP_VID3POSTSKIPCTRL_VID3VPOSTCLIP_MASK (0x0000003F)
#define ODN_PDP_VID3POSTSKIPCTRL_VID3VPOSTCLIP_LSBMASK (0x0000003F)
#define ODN_PDP_VID3POSTSKIPCTRL_VID3VPOSTCLIP_SHIFT (0)
#define ODN_PDP_VID3POSTSKIPCTRL_VID3VPOSTCLIP_LENGTH (6)
#define ODN_PDP_VID3POSTSKIPCTRL_VID3VPOSTCLIP_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4POSTSKIPCTRL_OFFSET (0x023C)

/* PDP, VID4POSTSKIPCTRL, VID4HPOSTCLIP
*/
#define ODN_PDP_VID4POSTSKIPCTRL_VID4HPOSTCLIP_MASK (0x007F0000)
#define ODN_PDP_VID4POSTSKIPCTRL_VID4HPOSTCLIP_LSBMASK (0x0000007F)
#define ODN_PDP_VID4POSTSKIPCTRL_VID4HPOSTCLIP_SHIFT (16)
#define ODN_PDP_VID4POSTSKIPCTRL_VID4HPOSTCLIP_LENGTH (7)
#define ODN_PDP_VID4POSTSKIPCTRL_VID4HPOSTCLIP_SIGNED_FIELD IMG_FALSE

/* PDP, VID4POSTSKIPCTRL, VID4VPOSTCLIP
*/
#define ODN_PDP_VID4POSTSKIPCTRL_VID4VPOSTCLIP_MASK (0x0000003F)
#define ODN_PDP_VID4POSTSKIPCTRL_VID4VPOSTCLIP_LSBMASK (0x0000003F)
#define ODN_PDP_VID4POSTSKIPCTRL_VID4VPOSTCLIP_SHIFT (0)
#define ODN_PDP_VID4POSTSKIPCTRL_VID4VPOSTCLIP_LENGTH (6)
#define ODN_PDP_VID4POSTSKIPCTRL_VID4VPOSTCLIP_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1DECIMATE_CTRL_OFFSET (0x0240)

/* PDP, GRPH1DECIMATE_CTRL, GRPH1DECIMATE_LINE_DISCARD_COUNT
*/
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_COUNT_MASK \
	(0x000000F0)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_COUNT_LSBMASK \
	(0x0000000F)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_COUNT_SHIFT (4)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_COUNT_LENGTH (4)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_COUNT_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH1DECIMATE_CTRL, GRPH1DECIMATE_LINE_DISCARD_MODE
*/
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_MODE_MASK \
	(0x00000008)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_MODE_LSBMASK \
	(0x00000001)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_MODE_SHIFT (3)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_MODE_LENGTH (1)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_LINE_DISCARD_MODE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH1DECIMATE_CTRL, GRPH1DECIMATE_PIXEL_HALVE
*/
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_PIXEL_HALVE_MASK (0x00000004)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_PIXEL_HALVE_LSBMASK \
	(0x00000001)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_PIXEL_HALVE_SHIFT (2)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_PIXEL_HALVE_LENGTH (1)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_PIXEL_HALVE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH1DECIMATE_CTRL, GRPH1DECIMATE_EN
*/
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_EN_MASK (0x00000001)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_EN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_EN_SHIFT (0)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_EN_LENGTH (1)
#define ODN_PDP_GRPH1DECIMATE_CTRL_GRPH1DECIMATE_EN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2DECIMATE_CTRL_OFFSET (0x0244)

/* PDP, GRPH2DECIMATE_CTRL, GRPH2DECIMATE_LINE_DISCARD_COUNT
*/
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_COUNT_MASK \
	(0x000000F0)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_COUNT_LSBMASK \
	(0x0000000F)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_COUNT_SHIFT (4)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_COUNT_LENGTH (4)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_COUNT_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH2DECIMATE_CTRL, GRPH2DECIMATE_LINE_DISCARD_MODE
*/
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_MODE_MASK \
	(0x00000008)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_MODE_LSBMASK \
	(0x00000001)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_MODE_SHIFT (3)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_MODE_LENGTH (1)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_LINE_DISCARD_MODE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH2DECIMATE_CTRL, GRPH2DECIMATE_PIXEL_HALVE
*/
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_PIXEL_HALVE_MASK (0x00000004)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_PIXEL_HALVE_LSBMASK \
	(0x00000001)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_PIXEL_HALVE_SHIFT (2)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_PIXEL_HALVE_LENGTH (1)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_PIXEL_HALVE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH2DECIMATE_CTRL, GRPH2DECIMATE_EN
*/
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_EN_MASK (0x00000001)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_EN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_EN_SHIFT (0)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_EN_LENGTH (1)
#define ODN_PDP_GRPH2DECIMATE_CTRL_GRPH2DECIMATE_EN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3DECIMATE_CTRL_OFFSET (0x0248)

/* PDP, GRPH3DECIMATE_CTRL, GRPH3DECIMATE_LINE_DISCARD_COUNT
*/
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_COUNT_MASK \
	(0x000000F0)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_COUNT_LSBMASK \
	(0x0000000F)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_COUNT_SHIFT (4)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_COUNT_LENGTH (4)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_COUNT_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH3DECIMATE_CTRL, GRPH3DECIMATE_LINE_DISCARD_MODE
*/
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_MODE_MASK \
	(0x00000008)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_MODE_LSBMASK \
	(0x00000001)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_MODE_SHIFT (3)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_MODE_LENGTH (1)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_LINE_DISCARD_MODE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH3DECIMATE_CTRL, GRPH3DECIMATE_PIXEL_HALVE
*/
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_PIXEL_HALVE_MASK (0x00000004)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_PIXEL_HALVE_LSBMASK \
	(0x00000001)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_PIXEL_HALVE_SHIFT (2)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_PIXEL_HALVE_LENGTH (1)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_PIXEL_HALVE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH3DECIMATE_CTRL, GRPH3DECIMATE_EN
*/
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_EN_MASK (0x00000001)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_EN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_EN_SHIFT (0)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_EN_LENGTH (1)
#define ODN_PDP_GRPH3DECIMATE_CTRL_GRPH3DECIMATE_EN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4DECIMATE_CTRL_OFFSET (0x024C)

/* PDP, GRPH4DECIMATE_CTRL, GRPH4DECIMATE_LINE_DISCARD_COUNT
*/
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_COUNT_MASK \
	(0x000000F0)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_COUNT_LSBMASK \
	(0x0000000F)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_COUNT_SHIFT (4)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_COUNT_LENGTH (4)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_COUNT_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH4DECIMATE_CTRL, GRPH4DECIMATE_LINE_DISCARD_MODE
*/
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_MODE_MASK \
	(0x00000008)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_MODE_LSBMASK \
	(0x00000001)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_MODE_SHIFT (3)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_MODE_LENGTH (1)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_LINE_DISCARD_MODE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH4DECIMATE_CTRL, GRPH4DECIMATE_PIXEL_HALVE
*/
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_PIXEL_HALVE_MASK (0x00000004)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_PIXEL_HALVE_LSBMASK \
	(0x00000001)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_PIXEL_HALVE_SHIFT (2)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_PIXEL_HALVE_LENGTH (1)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_PIXEL_HALVE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH4DECIMATE_CTRL, GRPH4DECIMATE_EN
*/
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_EN_MASK (0x00000001)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_EN_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_EN_SHIFT (0)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_EN_LENGTH (1)
#define ODN_PDP_GRPH4DECIMATE_CTRL_GRPH4DECIMATE_EN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1DECIMATE_CTRL_OFFSET (0x0250)

/* PDP, VID1DECIMATE_CTRL, VID1DECIMATE_LINE_DISCARD_COUNT
*/
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_COUNT_MASK \
	(0x000000F0)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_COUNT_LSBMASK \
	(0x0000000F)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_COUNT_SHIFT (4)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_COUNT_LENGTH (4)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_COUNT_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID1DECIMATE_CTRL, VID1DECIMATE_LINE_DISCARD_MODE
*/
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_MODE_MASK \
	(0x00000008)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_MODE_LSBMASK \
	(0x00000001)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_MODE_SHIFT (3)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_MODE_LENGTH (1)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_LINE_DISCARD_MODE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID1DECIMATE_CTRL, VID1DECIMATE_PIXEL_HALVE
*/
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_PIXEL_HALVE_MASK (0x00000004)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_PIXEL_HALVE_LSBMASK (0x00000001)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_PIXEL_HALVE_SHIFT (2)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_PIXEL_HALVE_LENGTH (1)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_PIXEL_HALVE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID1DECIMATE_CTRL, VID1DECIMATE_EN
*/
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_EN_MASK (0x00000001)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_EN_LSBMASK (0x00000001)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_EN_SHIFT (0)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_EN_LENGTH (1)
#define ODN_PDP_VID1DECIMATE_CTRL_VID1DECIMATE_EN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2DECIMATE_CTRL_OFFSET (0x0254)

/* PDP, VID2DECIMATE_CTRL, VID2DECIMATE_LINE_DISCARD_COUNT
*/
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_COUNT_MASK \
	(0x000000F0)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_COUNT_LSBMASK \
	(0x0000000F)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_COUNT_SHIFT (4)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_COUNT_LENGTH (4)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_COUNT_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID2DECIMATE_CTRL, VID2DECIMATE_LINE_DISCARD_MODE
*/
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_MODE_MASK \
	(0x00000008)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_MODE_LSBMASK \
	(0x00000001)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_MODE_SHIFT (3)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_MODE_LENGTH (1)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_LINE_DISCARD_MODE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID2DECIMATE_CTRL, VID2DECIMATE_PIXEL_HALVE
*/
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_PIXEL_HALVE_MASK (0x00000004)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_PIXEL_HALVE_LSBMASK (0x00000001)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_PIXEL_HALVE_SHIFT (2)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_PIXEL_HALVE_LENGTH (1)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_PIXEL_HALVE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID2DECIMATE_CTRL, VID2DECIMATE_EN
*/
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_EN_MASK (0x00000001)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_EN_LSBMASK (0x00000001)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_EN_SHIFT (0)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_EN_LENGTH (1)
#define ODN_PDP_VID2DECIMATE_CTRL_VID2DECIMATE_EN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3DECIMATE_CTRL_OFFSET (0x0258)

/* PDP, VID3DECIMATE_CTRL, VID3DECIMATE_LINE_DISCARD_COUNT
*/
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_COUNT_MASK \
	(0x000000F0)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_COUNT_LSBMASK \
	(0x0000000F)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_COUNT_SHIFT (4)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_COUNT_LENGTH (4)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_COUNT_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID3DECIMATE_CTRL, VID3DECIMATE_LINE_DISCARD_MODE
*/
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_MODE_MASK \
	(0x00000008)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_MODE_LSBMASK \
	(0x00000001)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_MODE_SHIFT (3)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_MODE_LENGTH (1)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_LINE_DISCARD_MODE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID3DECIMATE_CTRL, VID3DECIMATE_PIXEL_HALVE
*/
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_PIXEL_HALVE_MASK (0x00000004)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_PIXEL_HALVE_LSBMASK (0x00000001)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_PIXEL_HALVE_SHIFT (2)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_PIXEL_HALVE_LENGTH (1)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_PIXEL_HALVE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID3DECIMATE_CTRL, VID3DECIMATE_EN
*/
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_EN_MASK (0x00000001)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_EN_LSBMASK (0x00000001)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_EN_SHIFT (0)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_EN_LENGTH (1)
#define ODN_PDP_VID3DECIMATE_CTRL_VID3DECIMATE_EN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4DECIMATE_CTRL_OFFSET (0x025C)

/* PDP, VID4DECIMATE_CTRL, VID4DECIMATE_LINE_DISCARD_COUNT
*/
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_COUNT_MASK \
	(0x000000F0)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_COUNT_LSBMASK \
	(0x0000000F)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_COUNT_SHIFT (4)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_COUNT_LENGTH (4)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_COUNT_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID4DECIMATE_CTRL, VID4DECIMATE_LINE_DISCARD_MODE
*/
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_MODE_MASK \
	(0x00000008)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_MODE_LSBMASK \
	(0x00000001)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_MODE_SHIFT (3)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_MODE_LENGTH (1)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_LINE_DISCARD_MODE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID4DECIMATE_CTRL, VID4DECIMATE_PIXEL_HALVE
*/
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_PIXEL_HALVE_MASK (0x00000004)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_PIXEL_HALVE_LSBMASK (0x00000001)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_PIXEL_HALVE_SHIFT (2)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_PIXEL_HALVE_LENGTH (1)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_PIXEL_HALVE_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID4DECIMATE_CTRL, VID4DECIMATE_EN
*/
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_EN_MASK (0x00000001)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_EN_LSBMASK (0x00000001)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_EN_SHIFT (0)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_EN_LENGTH (1)
#define ODN_PDP_VID4DECIMATE_CTRL_VID4DECIMATE_EN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1SKIPCTRL_OFFSET (0x0270)

/* PDP, VID1SKIPCTRL, VID1HSKIP
*/
#define ODN_PDP_VID1SKIPCTRL_VID1HSKIP_MASK (0x0FFF0000)
#define ODN_PDP_VID1SKIPCTRL_VID1HSKIP_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1SKIPCTRL_VID1HSKIP_SHIFT (16)
#define ODN_PDP_VID1SKIPCTRL_VID1HSKIP_LENGTH (12)
#define ODN_PDP_VID1SKIPCTRL_VID1HSKIP_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SKIPCTRL, VID1VSKIP
*/
#define ODN_PDP_VID1SKIPCTRL_VID1VSKIP_MASK (0x00000FFF)
#define ODN_PDP_VID1SKIPCTRL_VID1VSKIP_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1SKIPCTRL_VID1VSKIP_SHIFT (0)
#define ODN_PDP_VID1SKIPCTRL_VID1VSKIP_LENGTH (12)
#define ODN_PDP_VID1SKIPCTRL_VID1VSKIP_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2SKIPCTRL_OFFSET (0x0274)

/* PDP, VID2SKIPCTRL, VID2HSKIP
*/
#define ODN_PDP_VID2SKIPCTRL_VID2HSKIP_MASK (0x0FFF0000)
#define ODN_PDP_VID2SKIPCTRL_VID2HSKIP_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2SKIPCTRL_VID2HSKIP_SHIFT (16)
#define ODN_PDP_VID2SKIPCTRL_VID2HSKIP_LENGTH (12)
#define ODN_PDP_VID2SKIPCTRL_VID2HSKIP_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SKIPCTRL, VID2VSKIP
*/
#define ODN_PDP_VID2SKIPCTRL_VID2VSKIP_MASK (0x00000FFF)
#define ODN_PDP_VID2SKIPCTRL_VID2VSKIP_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2SKIPCTRL_VID2VSKIP_SHIFT (0)
#define ODN_PDP_VID2SKIPCTRL_VID2VSKIP_LENGTH (12)
#define ODN_PDP_VID2SKIPCTRL_VID2VSKIP_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3SKIPCTRL_OFFSET (0x0278)

/* PDP, VID3SKIPCTRL, VID3HSKIP
*/
#define ODN_PDP_VID3SKIPCTRL_VID3HSKIP_MASK (0x0FFF0000)
#define ODN_PDP_VID3SKIPCTRL_VID3HSKIP_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3SKIPCTRL_VID3HSKIP_SHIFT (16)
#define ODN_PDP_VID3SKIPCTRL_VID3HSKIP_LENGTH (12)
#define ODN_PDP_VID3SKIPCTRL_VID3HSKIP_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SKIPCTRL, VID3VSKIP
*/
#define ODN_PDP_VID3SKIPCTRL_VID3VSKIP_MASK (0x00000FFF)
#define ODN_PDP_VID3SKIPCTRL_VID3VSKIP_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3SKIPCTRL_VID3VSKIP_SHIFT (0)
#define ODN_PDP_VID3SKIPCTRL_VID3VSKIP_LENGTH (12)
#define ODN_PDP_VID3SKIPCTRL_VID3VSKIP_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4SKIPCTRL_OFFSET (0x027C)

/* PDP, VID4SKIPCTRL, VID4HSKIP
*/
#define ODN_PDP_VID4SKIPCTRL_VID4HSKIP_MASK (0x0FFF0000)
#define ODN_PDP_VID4SKIPCTRL_VID4HSKIP_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4SKIPCTRL_VID4HSKIP_SHIFT (16)
#define ODN_PDP_VID4SKIPCTRL_VID4HSKIP_LENGTH (12)
#define ODN_PDP_VID4SKIPCTRL_VID4HSKIP_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SKIPCTRL, VID4VSKIP
*/
#define ODN_PDP_VID4SKIPCTRL_VID4VSKIP_MASK (0x00000FFF)
#define ODN_PDP_VID4SKIPCTRL_VID4VSKIP_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4SKIPCTRL_VID4VSKIP_SHIFT (0)
#define ODN_PDP_VID4SKIPCTRL_VID4VSKIP_LENGTH (12)
#define ODN_PDP_VID4SKIPCTRL_VID4VSKIP_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1SCALECTRL_OFFSET (0x0460)

/* PDP, VID1SCALECTRL, VID1HSCALEBP
*/
#define ODN_PDP_VID1SCALECTRL_VID1HSCALEBP_MASK (0x80000000)
#define ODN_PDP_VID1SCALECTRL_VID1HSCALEBP_LSBMASK (0x00000001)
#define ODN_PDP_VID1SCALECTRL_VID1HSCALEBP_SHIFT (31)
#define ODN_PDP_VID1SCALECTRL_VID1HSCALEBP_LENGTH (1)
#define ODN_PDP_VID1SCALECTRL_VID1HSCALEBP_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SCALECTRL, VID1VSCALEBP
*/
#define ODN_PDP_VID1SCALECTRL_VID1VSCALEBP_MASK (0x40000000)
#define ODN_PDP_VID1SCALECTRL_VID1VSCALEBP_LSBMASK (0x00000001)
#define ODN_PDP_VID1SCALECTRL_VID1VSCALEBP_SHIFT (30)
#define ODN_PDP_VID1SCALECTRL_VID1VSCALEBP_LENGTH (1)
#define ODN_PDP_VID1SCALECTRL_VID1VSCALEBP_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SCALECTRL, VID1HSBEFOREVS
*/
#define ODN_PDP_VID1SCALECTRL_VID1HSBEFOREVS_MASK (0x20000000)
#define ODN_PDP_VID1SCALECTRL_VID1HSBEFOREVS_LSBMASK (0x00000001)
#define ODN_PDP_VID1SCALECTRL_VID1HSBEFOREVS_SHIFT (29)
#define ODN_PDP_VID1SCALECTRL_VID1HSBEFOREVS_LENGTH (1)
#define ODN_PDP_VID1SCALECTRL_VID1HSBEFOREVS_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SCALECTRL, VID1VSURUNCTRL
*/
#define ODN_PDP_VID1SCALECTRL_VID1VSURUNCTRL_MASK (0x08000000)
#define ODN_PDP_VID1SCALECTRL_VID1VSURUNCTRL_LSBMASK (0x00000001)
#define ODN_PDP_VID1SCALECTRL_VID1VSURUNCTRL_SHIFT (27)
#define ODN_PDP_VID1SCALECTRL_VID1VSURUNCTRL_LENGTH (1)
#define ODN_PDP_VID1SCALECTRL_VID1VSURUNCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SCALECTRL, VID1PAN_EN
*/
#define ODN_PDP_VID1SCALECTRL_VID1PAN_EN_MASK (0x00040000)
#define ODN_PDP_VID1SCALECTRL_VID1PAN_EN_LSBMASK (0x00000001)
#define ODN_PDP_VID1SCALECTRL_VID1PAN_EN_SHIFT (18)
#define ODN_PDP_VID1SCALECTRL_VID1PAN_EN_LENGTH (1)
#define ODN_PDP_VID1SCALECTRL_VID1PAN_EN_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SCALECTRL, VID1VORDER
*/
#define ODN_PDP_VID1SCALECTRL_VID1VORDER_MASK (0x00030000)
#define ODN_PDP_VID1SCALECTRL_VID1VORDER_LSBMASK (0x00000003)
#define ODN_PDP_VID1SCALECTRL_VID1VORDER_SHIFT (16)
#define ODN_PDP_VID1SCALECTRL_VID1VORDER_LENGTH (2)
#define ODN_PDP_VID1SCALECTRL_VID1VORDER_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SCALECTRL, VID1VPITCH
*/
#define ODN_PDP_VID1SCALECTRL_VID1VPITCH_MASK (0x0000FFFF)
#define ODN_PDP_VID1SCALECTRL_VID1VPITCH_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID1SCALECTRL_VID1VPITCH_SHIFT (0)
#define ODN_PDP_VID1SCALECTRL_VID1VPITCH_LENGTH (16)
#define ODN_PDP_VID1SCALECTRL_VID1VPITCH_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VSINIT_OFFSET (0x0464)

/* PDP, VID1VSINIT, VID1VINITIAL1
*/
#define ODN_PDP_VID1VSINIT_VID1VINITIAL1_MASK (0xFFFF0000)
#define ODN_PDP_VID1VSINIT_VID1VINITIAL1_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID1VSINIT_VID1VINITIAL1_SHIFT (16)
#define ODN_PDP_VID1VSINIT_VID1VINITIAL1_LENGTH (16)
#define ODN_PDP_VID1VSINIT_VID1VINITIAL1_SIGNED_FIELD IMG_FALSE

/* PDP, VID1VSINIT, VID1VINITIAL0
*/
#define ODN_PDP_VID1VSINIT_VID1VINITIAL0_MASK (0x0000FFFF)
#define ODN_PDP_VID1VSINIT_VID1VINITIAL0_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID1VSINIT_VID1VINITIAL0_SHIFT (0)
#define ODN_PDP_VID1VSINIT_VID1VINITIAL0_LENGTH (16)
#define ODN_PDP_VID1VSINIT_VID1VINITIAL0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VCOEFF0_OFFSET (0x0468)

/* PDP, VID1VCOEFF0, VID1VCOEFF0
*/
#define ODN_PDP_VID1VCOEFF0_VID1VCOEFF0_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF0_VID1VCOEFF0_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF0_VID1VCOEFF0_SHIFT (0)
#define ODN_PDP_VID1VCOEFF0_VID1VCOEFF0_LENGTH (32)
#define ODN_PDP_VID1VCOEFF0_VID1VCOEFF0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VCOEFF1_OFFSET (0x046C)

/* PDP, VID1VCOEFF1, VID1VCOEFF1
*/
#define ODN_PDP_VID1VCOEFF1_VID1VCOEFF1_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF1_VID1VCOEFF1_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF1_VID1VCOEFF1_SHIFT (0)
#define ODN_PDP_VID1VCOEFF1_VID1VCOEFF1_LENGTH (32)
#define ODN_PDP_VID1VCOEFF1_VID1VCOEFF1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VCOEFF2_OFFSET (0x0470)

/* PDP, VID1VCOEFF2, VID1VCOEFF2
*/
#define ODN_PDP_VID1VCOEFF2_VID1VCOEFF2_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF2_VID1VCOEFF2_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF2_VID1VCOEFF2_SHIFT (0)
#define ODN_PDP_VID1VCOEFF2_VID1VCOEFF2_LENGTH (32)
#define ODN_PDP_VID1VCOEFF2_VID1VCOEFF2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VCOEFF3_OFFSET (0x0474)

/* PDP, VID1VCOEFF3, VID1VCOEFF3
*/
#define ODN_PDP_VID1VCOEFF3_VID1VCOEFF3_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF3_VID1VCOEFF3_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF3_VID1VCOEFF3_SHIFT (0)
#define ODN_PDP_VID1VCOEFF3_VID1VCOEFF3_LENGTH (32)
#define ODN_PDP_VID1VCOEFF3_VID1VCOEFF3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VCOEFF4_OFFSET (0x0478)

/* PDP, VID1VCOEFF4, VID1VCOEFF4
*/
#define ODN_PDP_VID1VCOEFF4_VID1VCOEFF4_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF4_VID1VCOEFF4_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF4_VID1VCOEFF4_SHIFT (0)
#define ODN_PDP_VID1VCOEFF4_VID1VCOEFF4_LENGTH (32)
#define ODN_PDP_VID1VCOEFF4_VID1VCOEFF4_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VCOEFF5_OFFSET (0x047C)

/* PDP, VID1VCOEFF5, VID1VCOEFF5
*/
#define ODN_PDP_VID1VCOEFF5_VID1VCOEFF5_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF5_VID1VCOEFF5_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF5_VID1VCOEFF5_SHIFT (0)
#define ODN_PDP_VID1VCOEFF5_VID1VCOEFF5_LENGTH (32)
#define ODN_PDP_VID1VCOEFF5_VID1VCOEFF5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VCOEFF6_OFFSET (0x0480)

/* PDP, VID1VCOEFF6, VID1VCOEFF6
*/
#define ODN_PDP_VID1VCOEFF6_VID1VCOEFF6_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF6_VID1VCOEFF6_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF6_VID1VCOEFF6_SHIFT (0)
#define ODN_PDP_VID1VCOEFF6_VID1VCOEFF6_LENGTH (32)
#define ODN_PDP_VID1VCOEFF6_VID1VCOEFF6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VCOEFF7_OFFSET (0x0484)

/* PDP, VID1VCOEFF7, VID1VCOEFF7
*/
#define ODN_PDP_VID1VCOEFF7_VID1VCOEFF7_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF7_VID1VCOEFF7_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1VCOEFF7_VID1VCOEFF7_SHIFT (0)
#define ODN_PDP_VID1VCOEFF7_VID1VCOEFF7_LENGTH (32)
#define ODN_PDP_VID1VCOEFF7_VID1VCOEFF7_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1VCOEFF8_OFFSET (0x0488)

/* PDP, VID1VCOEFF8, VID1VCOEFF8
*/
#define ODN_PDP_VID1VCOEFF8_VID1VCOEFF8_MASK (0x000000FF)
#define ODN_PDP_VID1VCOEFF8_VID1VCOEFF8_LSBMASK (0x000000FF)
#define ODN_PDP_VID1VCOEFF8_VID1VCOEFF8_SHIFT (0)
#define ODN_PDP_VID1VCOEFF8_VID1VCOEFF8_LENGTH (8)
#define ODN_PDP_VID1VCOEFF8_VID1VCOEFF8_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HSINIT_OFFSET (0x048C)

/* PDP, VID1HSINIT, VID1HINITIAL
*/
#define ODN_PDP_VID1HSINIT_VID1HINITIAL_MASK (0xFFFF0000)
#define ODN_PDP_VID1HSINIT_VID1HINITIAL_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID1HSINIT_VID1HINITIAL_SHIFT (16)
#define ODN_PDP_VID1HSINIT_VID1HINITIAL_LENGTH (16)
#define ODN_PDP_VID1HSINIT_VID1HINITIAL_SIGNED_FIELD IMG_FALSE

/* PDP, VID1HSINIT, VID1HPITCH
*/
#define ODN_PDP_VID1HSINIT_VID1HPITCH_MASK (0x0000FFFF)
#define ODN_PDP_VID1HSINIT_VID1HPITCH_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID1HSINIT_VID1HPITCH_SHIFT (0)
#define ODN_PDP_VID1HSINIT_VID1HPITCH_LENGTH (16)
#define ODN_PDP_VID1HSINIT_VID1HPITCH_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF0_OFFSET (0x0490)

/* PDP, VID1HCOEFF0, VID1HCOEFF0
*/
#define ODN_PDP_VID1HCOEFF0_VID1HCOEFF0_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF0_VID1HCOEFF0_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF0_VID1HCOEFF0_SHIFT (0)
#define ODN_PDP_VID1HCOEFF0_VID1HCOEFF0_LENGTH (32)
#define ODN_PDP_VID1HCOEFF0_VID1HCOEFF0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF1_OFFSET (0x0494)

/* PDP, VID1HCOEFF1, VID1HCOEFF1
*/
#define ODN_PDP_VID1HCOEFF1_VID1HCOEFF1_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF1_VID1HCOEFF1_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF1_VID1HCOEFF1_SHIFT (0)
#define ODN_PDP_VID1HCOEFF1_VID1HCOEFF1_LENGTH (32)
#define ODN_PDP_VID1HCOEFF1_VID1HCOEFF1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF2_OFFSET (0x0498)

/* PDP, VID1HCOEFF2, VID1HCOEFF2
*/
#define ODN_PDP_VID1HCOEFF2_VID1HCOEFF2_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF2_VID1HCOEFF2_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF2_VID1HCOEFF2_SHIFT (0)
#define ODN_PDP_VID1HCOEFF2_VID1HCOEFF2_LENGTH (32)
#define ODN_PDP_VID1HCOEFF2_VID1HCOEFF2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF3_OFFSET (0x049C)

/* PDP, VID1HCOEFF3, VID1HCOEFF3
*/
#define ODN_PDP_VID1HCOEFF3_VID1HCOEFF3_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF3_VID1HCOEFF3_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF3_VID1HCOEFF3_SHIFT (0)
#define ODN_PDP_VID1HCOEFF3_VID1HCOEFF3_LENGTH (32)
#define ODN_PDP_VID1HCOEFF3_VID1HCOEFF3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF4_OFFSET (0x04A0)

/* PDP, VID1HCOEFF4, VID1HCOEFF4
*/
#define ODN_PDP_VID1HCOEFF4_VID1HCOEFF4_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF4_VID1HCOEFF4_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF4_VID1HCOEFF4_SHIFT (0)
#define ODN_PDP_VID1HCOEFF4_VID1HCOEFF4_LENGTH (32)
#define ODN_PDP_VID1HCOEFF4_VID1HCOEFF4_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF5_OFFSET (0x04A4)

/* PDP, VID1HCOEFF5, VID1HCOEFF5
*/
#define ODN_PDP_VID1HCOEFF5_VID1HCOEFF5_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF5_VID1HCOEFF5_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF5_VID1HCOEFF5_SHIFT (0)
#define ODN_PDP_VID1HCOEFF5_VID1HCOEFF5_LENGTH (32)
#define ODN_PDP_VID1HCOEFF5_VID1HCOEFF5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF6_OFFSET (0x04A8)

/* PDP, VID1HCOEFF6, VID1HCOEFF6
*/
#define ODN_PDP_VID1HCOEFF6_VID1HCOEFF6_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF6_VID1HCOEFF6_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF6_VID1HCOEFF6_SHIFT (0)
#define ODN_PDP_VID1HCOEFF6_VID1HCOEFF6_LENGTH (32)
#define ODN_PDP_VID1HCOEFF6_VID1HCOEFF6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF7_OFFSET (0x04AC)

/* PDP, VID1HCOEFF7, VID1HCOEFF7
*/
#define ODN_PDP_VID1HCOEFF7_VID1HCOEFF7_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF7_VID1HCOEFF7_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF7_VID1HCOEFF7_SHIFT (0)
#define ODN_PDP_VID1HCOEFF7_VID1HCOEFF7_LENGTH (32)
#define ODN_PDP_VID1HCOEFF7_VID1HCOEFF7_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF8_OFFSET (0x04B0)

/* PDP, VID1HCOEFF8, VID1HCOEFF8
*/
#define ODN_PDP_VID1HCOEFF8_VID1HCOEFF8_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF8_VID1HCOEFF8_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF8_VID1HCOEFF8_SHIFT (0)
#define ODN_PDP_VID1HCOEFF8_VID1HCOEFF8_LENGTH (32)
#define ODN_PDP_VID1HCOEFF8_VID1HCOEFF8_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF9_OFFSET (0x04B4)

/* PDP, VID1HCOEFF9, VID1HCOEFF9
*/
#define ODN_PDP_VID1HCOEFF9_VID1HCOEFF9_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF9_VID1HCOEFF9_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF9_VID1HCOEFF9_SHIFT (0)
#define ODN_PDP_VID1HCOEFF9_VID1HCOEFF9_LENGTH (32)
#define ODN_PDP_VID1HCOEFF9_VID1HCOEFF9_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF10_OFFSET (0x04B8)

/* PDP, VID1HCOEFF10, VID1HCOEFF10
*/
#define ODN_PDP_VID1HCOEFF10_VID1HCOEFF10_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF10_VID1HCOEFF10_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF10_VID1HCOEFF10_SHIFT (0)
#define ODN_PDP_VID1HCOEFF10_VID1HCOEFF10_LENGTH (32)
#define ODN_PDP_VID1HCOEFF10_VID1HCOEFF10_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF11_OFFSET (0x04BC)

/* PDP, VID1HCOEFF11, VID1HCOEFF11
*/
#define ODN_PDP_VID1HCOEFF11_VID1HCOEFF11_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF11_VID1HCOEFF11_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF11_VID1HCOEFF11_SHIFT (0)
#define ODN_PDP_VID1HCOEFF11_VID1HCOEFF11_LENGTH (32)
#define ODN_PDP_VID1HCOEFF11_VID1HCOEFF11_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF12_OFFSET (0x04C0)

/* PDP, VID1HCOEFF12, VID1HCOEFF12
*/
#define ODN_PDP_VID1HCOEFF12_VID1HCOEFF12_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF12_VID1HCOEFF12_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF12_VID1HCOEFF12_SHIFT (0)
#define ODN_PDP_VID1HCOEFF12_VID1HCOEFF12_LENGTH (32)
#define ODN_PDP_VID1HCOEFF12_VID1HCOEFF12_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF13_OFFSET (0x04C4)

/* PDP, VID1HCOEFF13, VID1HCOEFF13
*/
#define ODN_PDP_VID1HCOEFF13_VID1HCOEFF13_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF13_VID1HCOEFF13_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF13_VID1HCOEFF13_SHIFT (0)
#define ODN_PDP_VID1HCOEFF13_VID1HCOEFF13_LENGTH (32)
#define ODN_PDP_VID1HCOEFF13_VID1HCOEFF13_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF14_OFFSET (0x04C8)

/* PDP, VID1HCOEFF14, VID1HCOEFF14
*/
#define ODN_PDP_VID1HCOEFF14_VID1HCOEFF14_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF14_VID1HCOEFF14_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF14_VID1HCOEFF14_SHIFT (0)
#define ODN_PDP_VID1HCOEFF14_VID1HCOEFF14_LENGTH (32)
#define ODN_PDP_VID1HCOEFF14_VID1HCOEFF14_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF15_OFFSET (0x04CC)

/* PDP, VID1HCOEFF15, VID1HCOEFF15
*/
#define ODN_PDP_VID1HCOEFF15_VID1HCOEFF15_MASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF15_VID1HCOEFF15_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID1HCOEFF15_VID1HCOEFF15_SHIFT (0)
#define ODN_PDP_VID1HCOEFF15_VID1HCOEFF15_LENGTH (32)
#define ODN_PDP_VID1HCOEFF15_VID1HCOEFF15_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1HCOEFF16_OFFSET (0x04D0)

/* PDP, VID1HCOEFF16, VID1HCOEFF16
*/
#define ODN_PDP_VID1HCOEFF16_VID1HCOEFF16_MASK (0x000000FF)
#define ODN_PDP_VID1HCOEFF16_VID1HCOEFF16_LSBMASK (0x000000FF)
#define ODN_PDP_VID1HCOEFF16_VID1HCOEFF16_SHIFT (0)
#define ODN_PDP_VID1HCOEFF16_VID1HCOEFF16_LENGTH (8)
#define ODN_PDP_VID1HCOEFF16_VID1HCOEFF16_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1SCALESIZE_OFFSET (0x04D4)

/* PDP, VID1SCALESIZE, VID1SCALEWIDTH
*/
#define ODN_PDP_VID1SCALESIZE_VID1SCALEWIDTH_MASK (0x0FFF0000)
#define ODN_PDP_VID1SCALESIZE_VID1SCALEWIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1SCALESIZE_VID1SCALEWIDTH_SHIFT (16)
#define ODN_PDP_VID1SCALESIZE_VID1SCALEWIDTH_LENGTH (12)
#define ODN_PDP_VID1SCALESIZE_VID1SCALEWIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, VID1SCALESIZE, VID1SCALEHEIGHT
*/
#define ODN_PDP_VID1SCALESIZE_VID1SCALEHEIGHT_MASK (0x00000FFF)
#define ODN_PDP_VID1SCALESIZE_VID1SCALEHEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1SCALESIZE_VID1SCALEHEIGHT_SHIFT (0)
#define ODN_PDP_VID1SCALESIZE_VID1SCALEHEIGHT_LENGTH (12)
#define ODN_PDP_VID1SCALESIZE_VID1SCALEHEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CORE_ID_OFFSET (0x04E0)

/* PDP, PVR_ODN_PDP_CORE_ID, GROUP_ID
*/
#define ODN_PDP_CORE_ID_GROUP_ID_MASK (0xFF000000)
#define ODN_PDP_CORE_ID_GROUP_ID_LSBMASK (0x000000FF)
#define ODN_PDP_CORE_ID_GROUP_ID_SHIFT (24)
#define ODN_PDP_CORE_ID_GROUP_ID_LENGTH (8)
#define ODN_PDP_CORE_ID_GROUP_ID_SIGNED_FIELD IMG_FALSE

/* PDP, PVR_ODN_PDP_CORE_ID, CORE_ID
*/
#define ODN_PDP_CORE_ID_CORE_ID_MASK (0x00FF0000)
#define ODN_PDP_CORE_ID_CORE_ID_LSBMASK (0x000000FF)
#define ODN_PDP_CORE_ID_CORE_ID_SHIFT (16)
#define ODN_PDP_CORE_ID_CORE_ID_LENGTH (8)
#define ODN_PDP_CORE_ID_CORE_ID_SIGNED_FIELD IMG_FALSE

/* PDP, PVR_ODN_PDP_CORE_ID, CONFIG_ID
*/
#define ODN_PDP_CORE_ID_CONFIG_ID_MASK (0x0000FFFF)
#define ODN_PDP_CORE_ID_CONFIG_ID_LSBMASK (0x0000FFFF)
#define ODN_PDP_CORE_ID_CONFIG_ID_SHIFT (0)
#define ODN_PDP_CORE_ID_CONFIG_ID_LENGTH (16)
#define ODN_PDP_CORE_ID_CONFIG_ID_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CORE_REV_OFFSET (0x04F0)

/* PDP, PVR_ODN_PDP_CORE_REV, MAJOR_REV
*/
#define ODN_PDP_CORE_REV_MAJOR_REV_MASK (0x00FF0000)
#define ODN_PDP_CORE_REV_MAJOR_REV_LSBMASK (0x000000FF)
#define ODN_PDP_CORE_REV_MAJOR_REV_SHIFT (16)
#define ODN_PDP_CORE_REV_MAJOR_REV_LENGTH (8)
#define ODN_PDP_CORE_REV_MAJOR_REV_SIGNED_FIELD IMG_FALSE

/* PDP, PVR_ODN_PDP_CORE_REV, MINOR_REV
*/
#define ODN_PDP_CORE_REV_MINOR_REV_MASK (0x0000FF00)
#define ODN_PDP_CORE_REV_MINOR_REV_LSBMASK (0x000000FF)
#define ODN_PDP_CORE_REV_MINOR_REV_SHIFT (8)
#define ODN_PDP_CORE_REV_MINOR_REV_LENGTH (8)
#define ODN_PDP_CORE_REV_MINOR_REV_SIGNED_FIELD IMG_FALSE

/* PDP, PVR_ODN_PDP_CORE_REV, MAINT_REV
*/
#define ODN_PDP_CORE_REV_MAINT_REV_MASK (0x000000FF)
#define ODN_PDP_CORE_REV_MAINT_REV_LSBMASK (0x000000FF)
#define ODN_PDP_CORE_REV_MAINT_REV_SHIFT (0)
#define ODN_PDP_CORE_REV_MAINT_REV_LENGTH (8)
#define ODN_PDP_CORE_REV_MAINT_REV_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2SCALECTRL_OFFSET (0x0500)

/* PDP, VID2SCALECTRL, VID2HSCALEBP
*/
#define ODN_PDP_VID2SCALECTRL_VID2HSCALEBP_MASK (0x80000000)
#define ODN_PDP_VID2SCALECTRL_VID2HSCALEBP_LSBMASK (0x00000001)
#define ODN_PDP_VID2SCALECTRL_VID2HSCALEBP_SHIFT (31)
#define ODN_PDP_VID2SCALECTRL_VID2HSCALEBP_LENGTH (1)
#define ODN_PDP_VID2SCALECTRL_VID2HSCALEBP_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SCALECTRL, VID2VSCALEBP
*/
#define ODN_PDP_VID2SCALECTRL_VID2VSCALEBP_MASK (0x40000000)
#define ODN_PDP_VID2SCALECTRL_VID2VSCALEBP_LSBMASK (0x00000001)
#define ODN_PDP_VID2SCALECTRL_VID2VSCALEBP_SHIFT (30)
#define ODN_PDP_VID2SCALECTRL_VID2VSCALEBP_LENGTH (1)
#define ODN_PDP_VID2SCALECTRL_VID2VSCALEBP_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SCALECTRL, VID2HSBEFOREVS
*/
#define ODN_PDP_VID2SCALECTRL_VID2HSBEFOREVS_MASK (0x20000000)
#define ODN_PDP_VID2SCALECTRL_VID2HSBEFOREVS_LSBMASK (0x00000001)
#define ODN_PDP_VID2SCALECTRL_VID2HSBEFOREVS_SHIFT (29)
#define ODN_PDP_VID2SCALECTRL_VID2HSBEFOREVS_LENGTH (1)
#define ODN_PDP_VID2SCALECTRL_VID2HSBEFOREVS_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SCALECTRL, VID2VSURUNCTRL
*/
#define ODN_PDP_VID2SCALECTRL_VID2VSURUNCTRL_MASK (0x08000000)
#define ODN_PDP_VID2SCALECTRL_VID2VSURUNCTRL_LSBMASK (0x00000001)
#define ODN_PDP_VID2SCALECTRL_VID2VSURUNCTRL_SHIFT (27)
#define ODN_PDP_VID2SCALECTRL_VID2VSURUNCTRL_LENGTH (1)
#define ODN_PDP_VID2SCALECTRL_VID2VSURUNCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SCALECTRL, VID2PAN_EN
*/
#define ODN_PDP_VID2SCALECTRL_VID2PAN_EN_MASK (0x00040000)
#define ODN_PDP_VID2SCALECTRL_VID2PAN_EN_LSBMASK (0x00000001)
#define ODN_PDP_VID2SCALECTRL_VID2PAN_EN_SHIFT (18)
#define ODN_PDP_VID2SCALECTRL_VID2PAN_EN_LENGTH (1)
#define ODN_PDP_VID2SCALECTRL_VID2PAN_EN_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SCALECTRL, VID2VORDER
*/
#define ODN_PDP_VID2SCALECTRL_VID2VORDER_MASK (0x00030000)
#define ODN_PDP_VID2SCALECTRL_VID2VORDER_LSBMASK (0x00000003)
#define ODN_PDP_VID2SCALECTRL_VID2VORDER_SHIFT (16)
#define ODN_PDP_VID2SCALECTRL_VID2VORDER_LENGTH (2)
#define ODN_PDP_VID2SCALECTRL_VID2VORDER_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SCALECTRL, VID2VPITCH
*/
#define ODN_PDP_VID2SCALECTRL_VID2VPITCH_MASK (0x0000FFFF)
#define ODN_PDP_VID2SCALECTRL_VID2VPITCH_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID2SCALECTRL_VID2VPITCH_SHIFT (0)
#define ODN_PDP_VID2SCALECTRL_VID2VPITCH_LENGTH (16)
#define ODN_PDP_VID2SCALECTRL_VID2VPITCH_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VSINIT_OFFSET (0x0504)

/* PDP, VID2VSINIT, VID2VINITIAL1
*/
#define ODN_PDP_VID2VSINIT_VID2VINITIAL1_MASK (0xFFFF0000)
#define ODN_PDP_VID2VSINIT_VID2VINITIAL1_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID2VSINIT_VID2VINITIAL1_SHIFT (16)
#define ODN_PDP_VID2VSINIT_VID2VINITIAL1_LENGTH (16)
#define ODN_PDP_VID2VSINIT_VID2VINITIAL1_SIGNED_FIELD IMG_FALSE

/* PDP, VID2VSINIT, VID2VINITIAL0
*/
#define ODN_PDP_VID2VSINIT_VID2VINITIAL0_MASK (0x0000FFFF)
#define ODN_PDP_VID2VSINIT_VID2VINITIAL0_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID2VSINIT_VID2VINITIAL0_SHIFT (0)
#define ODN_PDP_VID2VSINIT_VID2VINITIAL0_LENGTH (16)
#define ODN_PDP_VID2VSINIT_VID2VINITIAL0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VCOEFF0_OFFSET (0x0508)

/* PDP, VID2VCOEFF0, VID2VCOEFF0
*/
#define ODN_PDP_VID2VCOEFF0_VID2VCOEFF0_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF0_VID2VCOEFF0_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF0_VID2VCOEFF0_SHIFT (0)
#define ODN_PDP_VID2VCOEFF0_VID2VCOEFF0_LENGTH (32)
#define ODN_PDP_VID2VCOEFF0_VID2VCOEFF0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VCOEFF1_OFFSET (0x050C)

/* PDP, VID2VCOEFF1, VID2VCOEFF1
*/
#define ODN_PDP_VID2VCOEFF1_VID2VCOEFF1_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF1_VID2VCOEFF1_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF1_VID2VCOEFF1_SHIFT (0)
#define ODN_PDP_VID2VCOEFF1_VID2VCOEFF1_LENGTH (32)
#define ODN_PDP_VID2VCOEFF1_VID2VCOEFF1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VCOEFF2_OFFSET (0x0510)

/* PDP, VID2VCOEFF2, VID2VCOEFF2
*/
#define ODN_PDP_VID2VCOEFF2_VID2VCOEFF2_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF2_VID2VCOEFF2_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF2_VID2VCOEFF2_SHIFT (0)
#define ODN_PDP_VID2VCOEFF2_VID2VCOEFF2_LENGTH (32)
#define ODN_PDP_VID2VCOEFF2_VID2VCOEFF2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VCOEFF3_OFFSET (0x0514)

/* PDP, VID2VCOEFF3, VID2VCOEFF3
*/
#define ODN_PDP_VID2VCOEFF3_VID2VCOEFF3_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF3_VID2VCOEFF3_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF3_VID2VCOEFF3_SHIFT (0)
#define ODN_PDP_VID2VCOEFF3_VID2VCOEFF3_LENGTH (32)
#define ODN_PDP_VID2VCOEFF3_VID2VCOEFF3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VCOEFF4_OFFSET (0x0518)

/* PDP, VID2VCOEFF4, VID2VCOEFF4
*/
#define ODN_PDP_VID2VCOEFF4_VID2VCOEFF4_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF4_VID2VCOEFF4_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF4_VID2VCOEFF4_SHIFT (0)
#define ODN_PDP_VID2VCOEFF4_VID2VCOEFF4_LENGTH (32)
#define ODN_PDP_VID2VCOEFF4_VID2VCOEFF4_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VCOEFF5_OFFSET (0x051C)

/* PDP, VID2VCOEFF5, VID2VCOEFF5
*/
#define ODN_PDP_VID2VCOEFF5_VID2VCOEFF5_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF5_VID2VCOEFF5_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF5_VID2VCOEFF5_SHIFT (0)
#define ODN_PDP_VID2VCOEFF5_VID2VCOEFF5_LENGTH (32)
#define ODN_PDP_VID2VCOEFF5_VID2VCOEFF5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VCOEFF6_OFFSET (0x0520)

/* PDP, VID2VCOEFF6, VID2VCOEFF6
*/
#define ODN_PDP_VID2VCOEFF6_VID2VCOEFF6_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF6_VID2VCOEFF6_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF6_VID2VCOEFF6_SHIFT (0)
#define ODN_PDP_VID2VCOEFF6_VID2VCOEFF6_LENGTH (32)
#define ODN_PDP_VID2VCOEFF6_VID2VCOEFF6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VCOEFF7_OFFSET (0x0524)

/* PDP, VID2VCOEFF7, VID2VCOEFF7
*/
#define ODN_PDP_VID2VCOEFF7_VID2VCOEFF7_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF7_VID2VCOEFF7_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2VCOEFF7_VID2VCOEFF7_SHIFT (0)
#define ODN_PDP_VID2VCOEFF7_VID2VCOEFF7_LENGTH (32)
#define ODN_PDP_VID2VCOEFF7_VID2VCOEFF7_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2VCOEFF8_OFFSET (0x0528)

/* PDP, VID2VCOEFF8, VID2VCOEFF8
*/
#define ODN_PDP_VID2VCOEFF8_VID2VCOEFF8_MASK (0x000000FF)
#define ODN_PDP_VID2VCOEFF8_VID2VCOEFF8_LSBMASK (0x000000FF)
#define ODN_PDP_VID2VCOEFF8_VID2VCOEFF8_SHIFT (0)
#define ODN_PDP_VID2VCOEFF8_VID2VCOEFF8_LENGTH (8)
#define ODN_PDP_VID2VCOEFF8_VID2VCOEFF8_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HSINIT_OFFSET (0x052C)

/* PDP, VID2HSINIT, VID2HINITIAL
*/
#define ODN_PDP_VID2HSINIT_VID2HINITIAL_MASK (0xFFFF0000)
#define ODN_PDP_VID2HSINIT_VID2HINITIAL_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID2HSINIT_VID2HINITIAL_SHIFT (16)
#define ODN_PDP_VID2HSINIT_VID2HINITIAL_LENGTH (16)
#define ODN_PDP_VID2HSINIT_VID2HINITIAL_SIGNED_FIELD IMG_FALSE

/* PDP, VID2HSINIT, VID2HPITCH
*/
#define ODN_PDP_VID2HSINIT_VID2HPITCH_MASK (0x0000FFFF)
#define ODN_PDP_VID2HSINIT_VID2HPITCH_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID2HSINIT_VID2HPITCH_SHIFT (0)
#define ODN_PDP_VID2HSINIT_VID2HPITCH_LENGTH (16)
#define ODN_PDP_VID2HSINIT_VID2HPITCH_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF0_OFFSET (0x0530)

/* PDP, VID2HCOEFF0, VID2HCOEFF0
*/
#define ODN_PDP_VID2HCOEFF0_VID2HCOEFF0_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF0_VID2HCOEFF0_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF0_VID2HCOEFF0_SHIFT (0)
#define ODN_PDP_VID2HCOEFF0_VID2HCOEFF0_LENGTH (32)
#define ODN_PDP_VID2HCOEFF0_VID2HCOEFF0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF1_OFFSET (0x0534)

/* PDP, VID2HCOEFF1, VID2HCOEFF1
*/
#define ODN_PDP_VID2HCOEFF1_VID2HCOEFF1_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF1_VID2HCOEFF1_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF1_VID2HCOEFF1_SHIFT (0)
#define ODN_PDP_VID2HCOEFF1_VID2HCOEFF1_LENGTH (32)
#define ODN_PDP_VID2HCOEFF1_VID2HCOEFF1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF2_OFFSET (0x0538)

/* PDP, VID2HCOEFF2, VID2HCOEFF2
*/
#define ODN_PDP_VID2HCOEFF2_VID2HCOEFF2_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF2_VID2HCOEFF2_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF2_VID2HCOEFF2_SHIFT (0)
#define ODN_PDP_VID2HCOEFF2_VID2HCOEFF2_LENGTH (32)
#define ODN_PDP_VID2HCOEFF2_VID2HCOEFF2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF3_OFFSET (0x053C)

/* PDP, VID2HCOEFF3, VID2HCOEFF3
*/
#define ODN_PDP_VID2HCOEFF3_VID2HCOEFF3_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF3_VID2HCOEFF3_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF3_VID2HCOEFF3_SHIFT (0)
#define ODN_PDP_VID2HCOEFF3_VID2HCOEFF3_LENGTH (32)
#define ODN_PDP_VID2HCOEFF3_VID2HCOEFF3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF4_OFFSET (0x0540)

/* PDP, VID2HCOEFF4, VID2HCOEFF4
*/
#define ODN_PDP_VID2HCOEFF4_VID2HCOEFF4_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF4_VID2HCOEFF4_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF4_VID2HCOEFF4_SHIFT (0)
#define ODN_PDP_VID2HCOEFF4_VID2HCOEFF4_LENGTH (32)
#define ODN_PDP_VID2HCOEFF4_VID2HCOEFF4_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF5_OFFSET (0x0544)

/* PDP, VID2HCOEFF5, VID2HCOEFF5
*/
#define ODN_PDP_VID2HCOEFF5_VID2HCOEFF5_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF5_VID2HCOEFF5_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF5_VID2HCOEFF5_SHIFT (0)
#define ODN_PDP_VID2HCOEFF5_VID2HCOEFF5_LENGTH (32)
#define ODN_PDP_VID2HCOEFF5_VID2HCOEFF5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF6_OFFSET (0x0548)

/* PDP, VID2HCOEFF6, VID2HCOEFF6
*/
#define ODN_PDP_VID2HCOEFF6_VID2HCOEFF6_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF6_VID2HCOEFF6_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF6_VID2HCOEFF6_SHIFT (0)
#define ODN_PDP_VID2HCOEFF6_VID2HCOEFF6_LENGTH (32)
#define ODN_PDP_VID2HCOEFF6_VID2HCOEFF6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF7_OFFSET (0x054C)

/* PDP, VID2HCOEFF7, VID2HCOEFF7
*/
#define ODN_PDP_VID2HCOEFF7_VID2HCOEFF7_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF7_VID2HCOEFF7_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF7_VID2HCOEFF7_SHIFT (0)
#define ODN_PDP_VID2HCOEFF7_VID2HCOEFF7_LENGTH (32)
#define ODN_PDP_VID2HCOEFF7_VID2HCOEFF7_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF8_OFFSET (0x0550)

/* PDP, VID2HCOEFF8, VID2HCOEFF8
*/
#define ODN_PDP_VID2HCOEFF8_VID2HCOEFF8_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF8_VID2HCOEFF8_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF8_VID2HCOEFF8_SHIFT (0)
#define ODN_PDP_VID2HCOEFF8_VID2HCOEFF8_LENGTH (32)
#define ODN_PDP_VID2HCOEFF8_VID2HCOEFF8_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF9_OFFSET (0x0554)

/* PDP, VID2HCOEFF9, VID2HCOEFF9
*/
#define ODN_PDP_VID2HCOEFF9_VID2HCOEFF9_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF9_VID2HCOEFF9_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF9_VID2HCOEFF9_SHIFT (0)
#define ODN_PDP_VID2HCOEFF9_VID2HCOEFF9_LENGTH (32)
#define ODN_PDP_VID2HCOEFF9_VID2HCOEFF9_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF10_OFFSET (0x0558)

/* PDP, VID2HCOEFF10, VID2HCOEFF10
*/
#define ODN_PDP_VID2HCOEFF10_VID2HCOEFF10_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF10_VID2HCOEFF10_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF10_VID2HCOEFF10_SHIFT (0)
#define ODN_PDP_VID2HCOEFF10_VID2HCOEFF10_LENGTH (32)
#define ODN_PDP_VID2HCOEFF10_VID2HCOEFF10_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF11_OFFSET (0x055C)

/* PDP, VID2HCOEFF11, VID2HCOEFF11
*/
#define ODN_PDP_VID2HCOEFF11_VID2HCOEFF11_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF11_VID2HCOEFF11_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF11_VID2HCOEFF11_SHIFT (0)
#define ODN_PDP_VID2HCOEFF11_VID2HCOEFF11_LENGTH (32)
#define ODN_PDP_VID2HCOEFF11_VID2HCOEFF11_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF12_OFFSET (0x0560)

/* PDP, VID2HCOEFF12, VID2HCOEFF12
*/
#define ODN_PDP_VID2HCOEFF12_VID2HCOEFF12_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF12_VID2HCOEFF12_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF12_VID2HCOEFF12_SHIFT (0)
#define ODN_PDP_VID2HCOEFF12_VID2HCOEFF12_LENGTH (32)
#define ODN_PDP_VID2HCOEFF12_VID2HCOEFF12_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF13_OFFSET (0x0564)

/* PDP, VID2HCOEFF13, VID2HCOEFF13
*/
#define ODN_PDP_VID2HCOEFF13_VID2HCOEFF13_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF13_VID2HCOEFF13_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF13_VID2HCOEFF13_SHIFT (0)
#define ODN_PDP_VID2HCOEFF13_VID2HCOEFF13_LENGTH (32)
#define ODN_PDP_VID2HCOEFF13_VID2HCOEFF13_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF14_OFFSET (0x0568)

/* PDP, VID2HCOEFF14, VID2HCOEFF14
*/
#define ODN_PDP_VID2HCOEFF14_VID2HCOEFF14_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF14_VID2HCOEFF14_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF14_VID2HCOEFF14_SHIFT (0)
#define ODN_PDP_VID2HCOEFF14_VID2HCOEFF14_LENGTH (32)
#define ODN_PDP_VID2HCOEFF14_VID2HCOEFF14_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF15_OFFSET (0x056C)

/* PDP, VID2HCOEFF15, VID2HCOEFF15
*/
#define ODN_PDP_VID2HCOEFF15_VID2HCOEFF15_MASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF15_VID2HCOEFF15_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID2HCOEFF15_VID2HCOEFF15_SHIFT (0)
#define ODN_PDP_VID2HCOEFF15_VID2HCOEFF15_LENGTH (32)
#define ODN_PDP_VID2HCOEFF15_VID2HCOEFF15_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2HCOEFF16_OFFSET (0x0570)

/* PDP, VID2HCOEFF16, VID2HCOEFF16
*/
#define ODN_PDP_VID2HCOEFF16_VID2HCOEFF16_MASK (0x000000FF)
#define ODN_PDP_VID2HCOEFF16_VID2HCOEFF16_LSBMASK (0x000000FF)
#define ODN_PDP_VID2HCOEFF16_VID2HCOEFF16_SHIFT (0)
#define ODN_PDP_VID2HCOEFF16_VID2HCOEFF16_LENGTH (8)
#define ODN_PDP_VID2HCOEFF16_VID2HCOEFF16_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2SCALESIZE_OFFSET (0x0574)

/* PDP, VID2SCALESIZE, VID2SCALEWIDTH
*/
#define ODN_PDP_VID2SCALESIZE_VID2SCALEWIDTH_MASK (0x0FFF0000)
#define ODN_PDP_VID2SCALESIZE_VID2SCALEWIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2SCALESIZE_VID2SCALEWIDTH_SHIFT (16)
#define ODN_PDP_VID2SCALESIZE_VID2SCALEWIDTH_LENGTH (12)
#define ODN_PDP_VID2SCALESIZE_VID2SCALEWIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, VID2SCALESIZE, VID2SCALEHEIGHT
*/
#define ODN_PDP_VID2SCALESIZE_VID2SCALEHEIGHT_MASK (0x00000FFF)
#define ODN_PDP_VID2SCALESIZE_VID2SCALEHEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2SCALESIZE_VID2SCALEHEIGHT_SHIFT (0)
#define ODN_PDP_VID2SCALESIZE_VID2SCALEHEIGHT_LENGTH (12)
#define ODN_PDP_VID2SCALESIZE_VID2SCALEHEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3SCALECTRL_OFFSET (0x0578)

/* PDP, VID3SCALECTRL, VID3HSCALEBP
*/
#define ODN_PDP_VID3SCALECTRL_VID3HSCALEBP_MASK (0x80000000)
#define ODN_PDP_VID3SCALECTRL_VID3HSCALEBP_LSBMASK (0x00000001)
#define ODN_PDP_VID3SCALECTRL_VID3HSCALEBP_SHIFT (31)
#define ODN_PDP_VID3SCALECTRL_VID3HSCALEBP_LENGTH (1)
#define ODN_PDP_VID3SCALECTRL_VID3HSCALEBP_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SCALECTRL, VID3VSCALEBP
*/
#define ODN_PDP_VID3SCALECTRL_VID3VSCALEBP_MASK (0x40000000)
#define ODN_PDP_VID3SCALECTRL_VID3VSCALEBP_LSBMASK (0x00000001)
#define ODN_PDP_VID3SCALECTRL_VID3VSCALEBP_SHIFT (30)
#define ODN_PDP_VID3SCALECTRL_VID3VSCALEBP_LENGTH (1)
#define ODN_PDP_VID3SCALECTRL_VID3VSCALEBP_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SCALECTRL, VID3HSBEFOREVS
*/
#define ODN_PDP_VID3SCALECTRL_VID3HSBEFOREVS_MASK (0x20000000)
#define ODN_PDP_VID3SCALECTRL_VID3HSBEFOREVS_LSBMASK (0x00000001)
#define ODN_PDP_VID3SCALECTRL_VID3HSBEFOREVS_SHIFT (29)
#define ODN_PDP_VID3SCALECTRL_VID3HSBEFOREVS_LENGTH (1)
#define ODN_PDP_VID3SCALECTRL_VID3HSBEFOREVS_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SCALECTRL, VID3VSURUNCTRL
*/
#define ODN_PDP_VID3SCALECTRL_VID3VSURUNCTRL_MASK (0x08000000)
#define ODN_PDP_VID3SCALECTRL_VID3VSURUNCTRL_LSBMASK (0x00000001)
#define ODN_PDP_VID3SCALECTRL_VID3VSURUNCTRL_SHIFT (27)
#define ODN_PDP_VID3SCALECTRL_VID3VSURUNCTRL_LENGTH (1)
#define ODN_PDP_VID3SCALECTRL_VID3VSURUNCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SCALECTRL, VID3PAN_EN
*/
#define ODN_PDP_VID3SCALECTRL_VID3PAN_EN_MASK (0x00040000)
#define ODN_PDP_VID3SCALECTRL_VID3PAN_EN_LSBMASK (0x00000001)
#define ODN_PDP_VID3SCALECTRL_VID3PAN_EN_SHIFT (18)
#define ODN_PDP_VID3SCALECTRL_VID3PAN_EN_LENGTH (1)
#define ODN_PDP_VID3SCALECTRL_VID3PAN_EN_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SCALECTRL, VID3VORDER
*/
#define ODN_PDP_VID3SCALECTRL_VID3VORDER_MASK (0x00030000)
#define ODN_PDP_VID3SCALECTRL_VID3VORDER_LSBMASK (0x00000003)
#define ODN_PDP_VID3SCALECTRL_VID3VORDER_SHIFT (16)
#define ODN_PDP_VID3SCALECTRL_VID3VORDER_LENGTH (2)
#define ODN_PDP_VID3SCALECTRL_VID3VORDER_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SCALECTRL, VID3VPITCH
*/
#define ODN_PDP_VID3SCALECTRL_VID3VPITCH_MASK (0x0000FFFF)
#define ODN_PDP_VID3SCALECTRL_VID3VPITCH_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID3SCALECTRL_VID3VPITCH_SHIFT (0)
#define ODN_PDP_VID3SCALECTRL_VID3VPITCH_LENGTH (16)
#define ODN_PDP_VID3SCALECTRL_VID3VPITCH_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VSINIT_OFFSET (0x057C)

/* PDP, VID3VSINIT, VID3VINITIAL1
*/
#define ODN_PDP_VID3VSINIT_VID3VINITIAL1_MASK (0xFFFF0000)
#define ODN_PDP_VID3VSINIT_VID3VINITIAL1_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID3VSINIT_VID3VINITIAL1_SHIFT (16)
#define ODN_PDP_VID3VSINIT_VID3VINITIAL1_LENGTH (16)
#define ODN_PDP_VID3VSINIT_VID3VINITIAL1_SIGNED_FIELD IMG_FALSE

/* PDP, VID3VSINIT, VID3VINITIAL0
*/
#define ODN_PDP_VID3VSINIT_VID3VINITIAL0_MASK (0x0000FFFF)
#define ODN_PDP_VID3VSINIT_VID3VINITIAL0_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID3VSINIT_VID3VINITIAL0_SHIFT (0)
#define ODN_PDP_VID3VSINIT_VID3VINITIAL0_LENGTH (16)
#define ODN_PDP_VID3VSINIT_VID3VINITIAL0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VCOEFF0_OFFSET (0x0580)

/* PDP, VID3VCOEFF0, VID3VCOEFF0
*/
#define ODN_PDP_VID3VCOEFF0_VID3VCOEFF0_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF0_VID3VCOEFF0_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF0_VID3VCOEFF0_SHIFT (0)
#define ODN_PDP_VID3VCOEFF0_VID3VCOEFF0_LENGTH (32)
#define ODN_PDP_VID3VCOEFF0_VID3VCOEFF0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VCOEFF1_OFFSET (0x0584)

/* PDP, VID3VCOEFF1, VID3VCOEFF1
*/
#define ODN_PDP_VID3VCOEFF1_VID3VCOEFF1_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF1_VID3VCOEFF1_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF1_VID3VCOEFF1_SHIFT (0)
#define ODN_PDP_VID3VCOEFF1_VID3VCOEFF1_LENGTH (32)
#define ODN_PDP_VID3VCOEFF1_VID3VCOEFF1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VCOEFF2_OFFSET (0x0588)

/* PDP, VID3VCOEFF2, VID3VCOEFF2
*/
#define ODN_PDP_VID3VCOEFF2_VID3VCOEFF2_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF2_VID3VCOEFF2_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF2_VID3VCOEFF2_SHIFT (0)
#define ODN_PDP_VID3VCOEFF2_VID3VCOEFF2_LENGTH (32)
#define ODN_PDP_VID3VCOEFF2_VID3VCOEFF2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VCOEFF3_OFFSET (0x058C)

/* PDP, VID3VCOEFF3, VID3VCOEFF3
*/
#define ODN_PDP_VID3VCOEFF3_VID3VCOEFF3_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF3_VID3VCOEFF3_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF3_VID3VCOEFF3_SHIFT (0)
#define ODN_PDP_VID3VCOEFF3_VID3VCOEFF3_LENGTH (32)
#define ODN_PDP_VID3VCOEFF3_VID3VCOEFF3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VCOEFF4_OFFSET (0x0590)

/* PDP, VID3VCOEFF4, VID3VCOEFF4
*/
#define ODN_PDP_VID3VCOEFF4_VID3VCOEFF4_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF4_VID3VCOEFF4_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF4_VID3VCOEFF4_SHIFT (0)
#define ODN_PDP_VID3VCOEFF4_VID3VCOEFF4_LENGTH (32)
#define ODN_PDP_VID3VCOEFF4_VID3VCOEFF4_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VCOEFF5_OFFSET (0x0594)

/* PDP, VID3VCOEFF5, VID3VCOEFF5
*/
#define ODN_PDP_VID3VCOEFF5_VID3VCOEFF5_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF5_VID3VCOEFF5_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF5_VID3VCOEFF5_SHIFT (0)
#define ODN_PDP_VID3VCOEFF5_VID3VCOEFF5_LENGTH (32)
#define ODN_PDP_VID3VCOEFF5_VID3VCOEFF5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VCOEFF6_OFFSET (0x0598)

/* PDP, VID3VCOEFF6, VID3VCOEFF6
*/
#define ODN_PDP_VID3VCOEFF6_VID3VCOEFF6_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF6_VID3VCOEFF6_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF6_VID3VCOEFF6_SHIFT (0)
#define ODN_PDP_VID3VCOEFF6_VID3VCOEFF6_LENGTH (32)
#define ODN_PDP_VID3VCOEFF6_VID3VCOEFF6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VCOEFF7_OFFSET (0x059C)

/* PDP, VID3VCOEFF7, VID3VCOEFF7
*/
#define ODN_PDP_VID3VCOEFF7_VID3VCOEFF7_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF7_VID3VCOEFF7_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3VCOEFF7_VID3VCOEFF7_SHIFT (0)
#define ODN_PDP_VID3VCOEFF7_VID3VCOEFF7_LENGTH (32)
#define ODN_PDP_VID3VCOEFF7_VID3VCOEFF7_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3VCOEFF8_OFFSET (0x05A0)

/* PDP, VID3VCOEFF8, VID3VCOEFF8
*/
#define ODN_PDP_VID3VCOEFF8_VID3VCOEFF8_MASK (0x000000FF)
#define ODN_PDP_VID3VCOEFF8_VID3VCOEFF8_LSBMASK (0x000000FF)
#define ODN_PDP_VID3VCOEFF8_VID3VCOEFF8_SHIFT (0)
#define ODN_PDP_VID3VCOEFF8_VID3VCOEFF8_LENGTH (8)
#define ODN_PDP_VID3VCOEFF8_VID3VCOEFF8_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HSINIT_OFFSET (0x05A4)

/* PDP, VID3HSINIT, VID3HINITIAL
*/
#define ODN_PDP_VID3HSINIT_VID3HINITIAL_MASK (0xFFFF0000)
#define ODN_PDP_VID3HSINIT_VID3HINITIAL_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID3HSINIT_VID3HINITIAL_SHIFT (16)
#define ODN_PDP_VID3HSINIT_VID3HINITIAL_LENGTH (16)
#define ODN_PDP_VID3HSINIT_VID3HINITIAL_SIGNED_FIELD IMG_FALSE

/* PDP, VID3HSINIT, VID3HPITCH
*/
#define ODN_PDP_VID3HSINIT_VID3HPITCH_MASK (0x0000FFFF)
#define ODN_PDP_VID3HSINIT_VID3HPITCH_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID3HSINIT_VID3HPITCH_SHIFT (0)
#define ODN_PDP_VID3HSINIT_VID3HPITCH_LENGTH (16)
#define ODN_PDP_VID3HSINIT_VID3HPITCH_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF0_OFFSET (0x05A8)

/* PDP, VID3HCOEFF0, VID3HCOEFF0
*/
#define ODN_PDP_VID3HCOEFF0_VID3HCOEFF0_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF0_VID3HCOEFF0_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF0_VID3HCOEFF0_SHIFT (0)
#define ODN_PDP_VID3HCOEFF0_VID3HCOEFF0_LENGTH (32)
#define ODN_PDP_VID3HCOEFF0_VID3HCOEFF0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF1_OFFSET (0x05AC)

/* PDP, VID3HCOEFF1, VID3HCOEFF1
*/
#define ODN_PDP_VID3HCOEFF1_VID3HCOEFF1_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF1_VID3HCOEFF1_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF1_VID3HCOEFF1_SHIFT (0)
#define ODN_PDP_VID3HCOEFF1_VID3HCOEFF1_LENGTH (32)
#define ODN_PDP_VID3HCOEFF1_VID3HCOEFF1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF2_OFFSET (0x05B0)

/* PDP, VID3HCOEFF2, VID3HCOEFF2
*/
#define ODN_PDP_VID3HCOEFF2_VID3HCOEFF2_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF2_VID3HCOEFF2_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF2_VID3HCOEFF2_SHIFT (0)
#define ODN_PDP_VID3HCOEFF2_VID3HCOEFF2_LENGTH (32)
#define ODN_PDP_VID3HCOEFF2_VID3HCOEFF2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF3_OFFSET (0x05B4)

/* PDP, VID3HCOEFF3, VID3HCOEFF3
*/
#define ODN_PDP_VID3HCOEFF3_VID3HCOEFF3_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF3_VID3HCOEFF3_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF3_VID3HCOEFF3_SHIFT (0)
#define ODN_PDP_VID3HCOEFF3_VID3HCOEFF3_LENGTH (32)
#define ODN_PDP_VID3HCOEFF3_VID3HCOEFF3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF4_OFFSET (0x05B8)

/* PDP, VID3HCOEFF4, VID3HCOEFF4
*/
#define ODN_PDP_VID3HCOEFF4_VID3HCOEFF4_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF4_VID3HCOEFF4_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF4_VID3HCOEFF4_SHIFT (0)
#define ODN_PDP_VID3HCOEFF4_VID3HCOEFF4_LENGTH (32)
#define ODN_PDP_VID3HCOEFF4_VID3HCOEFF4_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF5_OFFSET (0x05BC)

/* PDP, VID3HCOEFF5, VID3HCOEFF5
*/
#define ODN_PDP_VID3HCOEFF5_VID3HCOEFF5_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF5_VID3HCOEFF5_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF5_VID3HCOEFF5_SHIFT (0)
#define ODN_PDP_VID3HCOEFF5_VID3HCOEFF5_LENGTH (32)
#define ODN_PDP_VID3HCOEFF5_VID3HCOEFF5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF6_OFFSET (0x05C0)

/* PDP, VID3HCOEFF6, VID3HCOEFF6
*/
#define ODN_PDP_VID3HCOEFF6_VID3HCOEFF6_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF6_VID3HCOEFF6_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF6_VID3HCOEFF6_SHIFT (0)
#define ODN_PDP_VID3HCOEFF6_VID3HCOEFF6_LENGTH (32)
#define ODN_PDP_VID3HCOEFF6_VID3HCOEFF6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF7_OFFSET (0x05C4)

/* PDP, VID3HCOEFF7, VID3HCOEFF7
*/
#define ODN_PDP_VID3HCOEFF7_VID3HCOEFF7_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF7_VID3HCOEFF7_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF7_VID3HCOEFF7_SHIFT (0)
#define ODN_PDP_VID3HCOEFF7_VID3HCOEFF7_LENGTH (32)
#define ODN_PDP_VID3HCOEFF7_VID3HCOEFF7_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF8_OFFSET (0x05C8)

/* PDP, VID3HCOEFF8, VID3HCOEFF8
*/
#define ODN_PDP_VID3HCOEFF8_VID3HCOEFF8_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF8_VID3HCOEFF8_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF8_VID3HCOEFF8_SHIFT (0)
#define ODN_PDP_VID3HCOEFF8_VID3HCOEFF8_LENGTH (32)
#define ODN_PDP_VID3HCOEFF8_VID3HCOEFF8_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF9_OFFSET (0x05CC)

/* PDP, VID3HCOEFF9, VID3HCOEFF9
*/
#define ODN_PDP_VID3HCOEFF9_VID3HCOEFF9_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF9_VID3HCOEFF9_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF9_VID3HCOEFF9_SHIFT (0)
#define ODN_PDP_VID3HCOEFF9_VID3HCOEFF9_LENGTH (32)
#define ODN_PDP_VID3HCOEFF9_VID3HCOEFF9_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF10_OFFSET (0x05D0)

/* PDP, VID3HCOEFF10, VID3HCOEFF10
*/
#define ODN_PDP_VID3HCOEFF10_VID3HCOEFF10_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF10_VID3HCOEFF10_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF10_VID3HCOEFF10_SHIFT (0)
#define ODN_PDP_VID3HCOEFF10_VID3HCOEFF10_LENGTH (32)
#define ODN_PDP_VID3HCOEFF10_VID3HCOEFF10_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF11_OFFSET (0x05D4)

/* PDP, VID3HCOEFF11, VID3HCOEFF11
*/
#define ODN_PDP_VID3HCOEFF11_VID3HCOEFF11_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF11_VID3HCOEFF11_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF11_VID3HCOEFF11_SHIFT (0)
#define ODN_PDP_VID3HCOEFF11_VID3HCOEFF11_LENGTH (32)
#define ODN_PDP_VID3HCOEFF11_VID3HCOEFF11_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF12_OFFSET (0x05D8)

/* PDP, VID3HCOEFF12, VID3HCOEFF12
*/
#define ODN_PDP_VID3HCOEFF12_VID3HCOEFF12_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF12_VID3HCOEFF12_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF12_VID3HCOEFF12_SHIFT (0)
#define ODN_PDP_VID3HCOEFF12_VID3HCOEFF12_LENGTH (32)
#define ODN_PDP_VID3HCOEFF12_VID3HCOEFF12_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF13_OFFSET (0x05DC)

/* PDP, VID3HCOEFF13, VID3HCOEFF13
*/
#define ODN_PDP_VID3HCOEFF13_VID3HCOEFF13_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF13_VID3HCOEFF13_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF13_VID3HCOEFF13_SHIFT (0)
#define ODN_PDP_VID3HCOEFF13_VID3HCOEFF13_LENGTH (32)
#define ODN_PDP_VID3HCOEFF13_VID3HCOEFF13_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF14_OFFSET (0x05E0)

/* PDP, VID3HCOEFF14, VID3HCOEFF14
*/
#define ODN_PDP_VID3HCOEFF14_VID3HCOEFF14_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF14_VID3HCOEFF14_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF14_VID3HCOEFF14_SHIFT (0)
#define ODN_PDP_VID3HCOEFF14_VID3HCOEFF14_LENGTH (32)
#define ODN_PDP_VID3HCOEFF14_VID3HCOEFF14_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF15_OFFSET (0x05E4)

/* PDP, VID3HCOEFF15, VID3HCOEFF15
*/
#define ODN_PDP_VID3HCOEFF15_VID3HCOEFF15_MASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF15_VID3HCOEFF15_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID3HCOEFF15_VID3HCOEFF15_SHIFT (0)
#define ODN_PDP_VID3HCOEFF15_VID3HCOEFF15_LENGTH (32)
#define ODN_PDP_VID3HCOEFF15_VID3HCOEFF15_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3HCOEFF16_OFFSET (0x05E8)

/* PDP, VID3HCOEFF16, VID3HCOEFF16
*/
#define ODN_PDP_VID3HCOEFF16_VID3HCOEFF16_MASK (0x000000FF)
#define ODN_PDP_VID3HCOEFF16_VID3HCOEFF16_LSBMASK (0x000000FF)
#define ODN_PDP_VID3HCOEFF16_VID3HCOEFF16_SHIFT (0)
#define ODN_PDP_VID3HCOEFF16_VID3HCOEFF16_LENGTH (8)
#define ODN_PDP_VID3HCOEFF16_VID3HCOEFF16_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3SCALESIZE_OFFSET (0x05EC)

/* PDP, VID3SCALESIZE, VID3SCALEWIDTH
*/
#define ODN_PDP_VID3SCALESIZE_VID3SCALEWIDTH_MASK (0x0FFF0000)
#define ODN_PDP_VID3SCALESIZE_VID3SCALEWIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3SCALESIZE_VID3SCALEWIDTH_SHIFT (16)
#define ODN_PDP_VID3SCALESIZE_VID3SCALEWIDTH_LENGTH (12)
#define ODN_PDP_VID3SCALESIZE_VID3SCALEWIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, VID3SCALESIZE, VID3SCALEHEIGHT
*/
#define ODN_PDP_VID3SCALESIZE_VID3SCALEHEIGHT_MASK (0x00000FFF)
#define ODN_PDP_VID3SCALESIZE_VID3SCALEHEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3SCALESIZE_VID3SCALEHEIGHT_SHIFT (0)
#define ODN_PDP_VID3SCALESIZE_VID3SCALEHEIGHT_LENGTH (12)
#define ODN_PDP_VID3SCALESIZE_VID3SCALEHEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4SCALECTRL_OFFSET (0x05F0)

/* PDP, VID4SCALECTRL, VID4HSCALEBP
*/
#define ODN_PDP_VID4SCALECTRL_VID4HSCALEBP_MASK (0x80000000)
#define ODN_PDP_VID4SCALECTRL_VID4HSCALEBP_LSBMASK (0x00000001)
#define ODN_PDP_VID4SCALECTRL_VID4HSCALEBP_SHIFT (31)
#define ODN_PDP_VID4SCALECTRL_VID4HSCALEBP_LENGTH (1)
#define ODN_PDP_VID4SCALECTRL_VID4HSCALEBP_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SCALECTRL, VID4VSCALEBP
*/
#define ODN_PDP_VID4SCALECTRL_VID4VSCALEBP_MASK (0x40000000)
#define ODN_PDP_VID4SCALECTRL_VID4VSCALEBP_LSBMASK (0x00000001)
#define ODN_PDP_VID4SCALECTRL_VID4VSCALEBP_SHIFT (30)
#define ODN_PDP_VID4SCALECTRL_VID4VSCALEBP_LENGTH (1)
#define ODN_PDP_VID4SCALECTRL_VID4VSCALEBP_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SCALECTRL, VID4HSBEFOREVS
*/
#define ODN_PDP_VID4SCALECTRL_VID4HSBEFOREVS_MASK (0x20000000)
#define ODN_PDP_VID4SCALECTRL_VID4HSBEFOREVS_LSBMASK (0x00000001)
#define ODN_PDP_VID4SCALECTRL_VID4HSBEFOREVS_SHIFT (29)
#define ODN_PDP_VID4SCALECTRL_VID4HSBEFOREVS_LENGTH (1)
#define ODN_PDP_VID4SCALECTRL_VID4HSBEFOREVS_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SCALECTRL, VID4VSURUNCTRL
*/
#define ODN_PDP_VID4SCALECTRL_VID4VSURUNCTRL_MASK (0x08000000)
#define ODN_PDP_VID4SCALECTRL_VID4VSURUNCTRL_LSBMASK (0x00000001)
#define ODN_PDP_VID4SCALECTRL_VID4VSURUNCTRL_SHIFT (27)
#define ODN_PDP_VID4SCALECTRL_VID4VSURUNCTRL_LENGTH (1)
#define ODN_PDP_VID4SCALECTRL_VID4VSURUNCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SCALECTRL, VID4PAN_EN
*/
#define ODN_PDP_VID4SCALECTRL_VID4PAN_EN_MASK (0x00040000)
#define ODN_PDP_VID4SCALECTRL_VID4PAN_EN_LSBMASK (0x00000001)
#define ODN_PDP_VID4SCALECTRL_VID4PAN_EN_SHIFT (18)
#define ODN_PDP_VID4SCALECTRL_VID4PAN_EN_LENGTH (1)
#define ODN_PDP_VID4SCALECTRL_VID4PAN_EN_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SCALECTRL, VID4VORDER
*/
#define ODN_PDP_VID4SCALECTRL_VID4VORDER_MASK (0x00030000)
#define ODN_PDP_VID4SCALECTRL_VID4VORDER_LSBMASK (0x00000003)
#define ODN_PDP_VID4SCALECTRL_VID4VORDER_SHIFT (16)
#define ODN_PDP_VID4SCALECTRL_VID4VORDER_LENGTH (2)
#define ODN_PDP_VID4SCALECTRL_VID4VORDER_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SCALECTRL, VID4VPITCH
*/
#define ODN_PDP_VID4SCALECTRL_VID4VPITCH_MASK (0x0000FFFF)
#define ODN_PDP_VID4SCALECTRL_VID4VPITCH_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID4SCALECTRL_VID4VPITCH_SHIFT (0)
#define ODN_PDP_VID4SCALECTRL_VID4VPITCH_LENGTH (16)
#define ODN_PDP_VID4SCALECTRL_VID4VPITCH_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VSINIT_OFFSET (0x05F4)

/* PDP, VID4VSINIT, VID4VINITIAL1
*/
#define ODN_PDP_VID4VSINIT_VID4VINITIAL1_MASK (0xFFFF0000)
#define ODN_PDP_VID4VSINIT_VID4VINITIAL1_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID4VSINIT_VID4VINITIAL1_SHIFT (16)
#define ODN_PDP_VID4VSINIT_VID4VINITIAL1_LENGTH (16)
#define ODN_PDP_VID4VSINIT_VID4VINITIAL1_SIGNED_FIELD IMG_FALSE

/* PDP, VID4VSINIT, VID4VINITIAL0
*/
#define ODN_PDP_VID4VSINIT_VID4VINITIAL0_MASK (0x0000FFFF)
#define ODN_PDP_VID4VSINIT_VID4VINITIAL0_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID4VSINIT_VID4VINITIAL0_SHIFT (0)
#define ODN_PDP_VID4VSINIT_VID4VINITIAL0_LENGTH (16)
#define ODN_PDP_VID4VSINIT_VID4VINITIAL0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VCOEFF0_OFFSET (0x05F8)

/* PDP, VID4VCOEFF0, VID4VCOEFF0
*/
#define ODN_PDP_VID4VCOEFF0_VID4VCOEFF0_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF0_VID4VCOEFF0_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF0_VID4VCOEFF0_SHIFT (0)
#define ODN_PDP_VID4VCOEFF0_VID4VCOEFF0_LENGTH (32)
#define ODN_PDP_VID4VCOEFF0_VID4VCOEFF0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VCOEFF1_OFFSET (0x05FC)

/* PDP, VID4VCOEFF1, VID4VCOEFF1
*/
#define ODN_PDP_VID4VCOEFF1_VID4VCOEFF1_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF1_VID4VCOEFF1_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF1_VID4VCOEFF1_SHIFT (0)
#define ODN_PDP_VID4VCOEFF1_VID4VCOEFF1_LENGTH (32)
#define ODN_PDP_VID4VCOEFF1_VID4VCOEFF1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VCOEFF2_OFFSET (0x0600)

/* PDP, VID4VCOEFF2, VID4VCOEFF2
*/
#define ODN_PDP_VID4VCOEFF2_VID4VCOEFF2_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF2_VID4VCOEFF2_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF2_VID4VCOEFF2_SHIFT (0)
#define ODN_PDP_VID4VCOEFF2_VID4VCOEFF2_LENGTH (32)
#define ODN_PDP_VID4VCOEFF2_VID4VCOEFF2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VCOEFF3_OFFSET (0x0604)

/* PDP, VID4VCOEFF3, VID4VCOEFF3
*/
#define ODN_PDP_VID4VCOEFF3_VID4VCOEFF3_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF3_VID4VCOEFF3_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF3_VID4VCOEFF3_SHIFT (0)
#define ODN_PDP_VID4VCOEFF3_VID4VCOEFF3_LENGTH (32)
#define ODN_PDP_VID4VCOEFF3_VID4VCOEFF3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VCOEFF4_OFFSET (0x0608)

/* PDP, VID4VCOEFF4, VID4VCOEFF4
*/
#define ODN_PDP_VID4VCOEFF4_VID4VCOEFF4_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF4_VID4VCOEFF4_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF4_VID4VCOEFF4_SHIFT (0)
#define ODN_PDP_VID4VCOEFF4_VID4VCOEFF4_LENGTH (32)
#define ODN_PDP_VID4VCOEFF4_VID4VCOEFF4_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VCOEFF5_OFFSET (0x060C)

/* PDP, VID4VCOEFF5, VID4VCOEFF5
*/
#define ODN_PDP_VID4VCOEFF5_VID4VCOEFF5_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF5_VID4VCOEFF5_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF5_VID4VCOEFF5_SHIFT (0)
#define ODN_PDP_VID4VCOEFF5_VID4VCOEFF5_LENGTH (32)
#define ODN_PDP_VID4VCOEFF5_VID4VCOEFF5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VCOEFF6_OFFSET (0x0610)

/* PDP, VID4VCOEFF6, VID4VCOEFF6
*/
#define ODN_PDP_VID4VCOEFF6_VID4VCOEFF6_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF6_VID4VCOEFF6_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF6_VID4VCOEFF6_SHIFT (0)
#define ODN_PDP_VID4VCOEFF6_VID4VCOEFF6_LENGTH (32)
#define ODN_PDP_VID4VCOEFF6_VID4VCOEFF6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VCOEFF7_OFFSET (0x0614)

/* PDP, VID4VCOEFF7, VID4VCOEFF7
*/
#define ODN_PDP_VID4VCOEFF7_VID4VCOEFF7_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF7_VID4VCOEFF7_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4VCOEFF7_VID4VCOEFF7_SHIFT (0)
#define ODN_PDP_VID4VCOEFF7_VID4VCOEFF7_LENGTH (32)
#define ODN_PDP_VID4VCOEFF7_VID4VCOEFF7_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4VCOEFF8_OFFSET (0x0618)

/* PDP, VID4VCOEFF8, VID4VCOEFF8
*/
#define ODN_PDP_VID4VCOEFF8_VID4VCOEFF8_MASK (0x000000FF)
#define ODN_PDP_VID4VCOEFF8_VID4VCOEFF8_LSBMASK (0x000000FF)
#define ODN_PDP_VID4VCOEFF8_VID4VCOEFF8_SHIFT (0)
#define ODN_PDP_VID4VCOEFF8_VID4VCOEFF8_LENGTH (8)
#define ODN_PDP_VID4VCOEFF8_VID4VCOEFF8_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HSINIT_OFFSET (0x061C)

/* PDP, VID4HSINIT, VID4HINITIAL
*/
#define ODN_PDP_VID4HSINIT_VID4HINITIAL_MASK (0xFFFF0000)
#define ODN_PDP_VID4HSINIT_VID4HINITIAL_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID4HSINIT_VID4HINITIAL_SHIFT (16)
#define ODN_PDP_VID4HSINIT_VID4HINITIAL_LENGTH (16)
#define ODN_PDP_VID4HSINIT_VID4HINITIAL_SIGNED_FIELD IMG_FALSE

/* PDP, VID4HSINIT, VID4HPITCH
*/
#define ODN_PDP_VID4HSINIT_VID4HPITCH_MASK (0x0000FFFF)
#define ODN_PDP_VID4HSINIT_VID4HPITCH_LSBMASK (0x0000FFFF)
#define ODN_PDP_VID4HSINIT_VID4HPITCH_SHIFT (0)
#define ODN_PDP_VID4HSINIT_VID4HPITCH_LENGTH (16)
#define ODN_PDP_VID4HSINIT_VID4HPITCH_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF0_OFFSET (0x0620)

/* PDP, VID4HCOEFF0, VID4HCOEFF0
*/
#define ODN_PDP_VID4HCOEFF0_VID4HCOEFF0_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF0_VID4HCOEFF0_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF0_VID4HCOEFF0_SHIFT (0)
#define ODN_PDP_VID4HCOEFF0_VID4HCOEFF0_LENGTH (32)
#define ODN_PDP_VID4HCOEFF0_VID4HCOEFF0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF1_OFFSET (0x0624)

/* PDP, VID4HCOEFF1, VID4HCOEFF1
*/
#define ODN_PDP_VID4HCOEFF1_VID4HCOEFF1_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF1_VID4HCOEFF1_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF1_VID4HCOEFF1_SHIFT (0)
#define ODN_PDP_VID4HCOEFF1_VID4HCOEFF1_LENGTH (32)
#define ODN_PDP_VID4HCOEFF1_VID4HCOEFF1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF2_OFFSET (0x0628)

/* PDP, VID4HCOEFF2, VID4HCOEFF2
*/
#define ODN_PDP_VID4HCOEFF2_VID4HCOEFF2_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF2_VID4HCOEFF2_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF2_VID4HCOEFF2_SHIFT (0)
#define ODN_PDP_VID4HCOEFF2_VID4HCOEFF2_LENGTH (32)
#define ODN_PDP_VID4HCOEFF2_VID4HCOEFF2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF3_OFFSET (0x062C)

/* PDP, VID4HCOEFF3, VID4HCOEFF3
*/
#define ODN_PDP_VID4HCOEFF3_VID4HCOEFF3_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF3_VID4HCOEFF3_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF3_VID4HCOEFF3_SHIFT (0)
#define ODN_PDP_VID4HCOEFF3_VID4HCOEFF3_LENGTH (32)
#define ODN_PDP_VID4HCOEFF3_VID4HCOEFF3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF4_OFFSET (0x0630)

/* PDP, VID4HCOEFF4, VID4HCOEFF4
*/
#define ODN_PDP_VID4HCOEFF4_VID4HCOEFF4_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF4_VID4HCOEFF4_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF4_VID4HCOEFF4_SHIFT (0)
#define ODN_PDP_VID4HCOEFF4_VID4HCOEFF4_LENGTH (32)
#define ODN_PDP_VID4HCOEFF4_VID4HCOEFF4_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF5_OFFSET (0x0634)

/* PDP, VID4HCOEFF5, VID4HCOEFF5
*/
#define ODN_PDP_VID4HCOEFF5_VID4HCOEFF5_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF5_VID4HCOEFF5_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF5_VID4HCOEFF5_SHIFT (0)
#define ODN_PDP_VID4HCOEFF5_VID4HCOEFF5_LENGTH (32)
#define ODN_PDP_VID4HCOEFF5_VID4HCOEFF5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF6_OFFSET (0x0638)

/* PDP, VID4HCOEFF6, VID4HCOEFF6
*/
#define ODN_PDP_VID4HCOEFF6_VID4HCOEFF6_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF6_VID4HCOEFF6_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF6_VID4HCOEFF6_SHIFT (0)
#define ODN_PDP_VID4HCOEFF6_VID4HCOEFF6_LENGTH (32)
#define ODN_PDP_VID4HCOEFF6_VID4HCOEFF6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF7_OFFSET (0x063C)

/* PDP, VID4HCOEFF7, VID4HCOEFF7
*/
#define ODN_PDP_VID4HCOEFF7_VID4HCOEFF7_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF7_VID4HCOEFF7_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF7_VID4HCOEFF7_SHIFT (0)
#define ODN_PDP_VID4HCOEFF7_VID4HCOEFF7_LENGTH (32)
#define ODN_PDP_VID4HCOEFF7_VID4HCOEFF7_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF8_OFFSET (0x0640)

/* PDP, VID4HCOEFF8, VID4HCOEFF8
*/
#define ODN_PDP_VID4HCOEFF8_VID4HCOEFF8_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF8_VID4HCOEFF8_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF8_VID4HCOEFF8_SHIFT (0)
#define ODN_PDP_VID4HCOEFF8_VID4HCOEFF8_LENGTH (32)
#define ODN_PDP_VID4HCOEFF8_VID4HCOEFF8_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF9_OFFSET (0x0644)

/* PDP, VID4HCOEFF9, VID4HCOEFF9
*/
#define ODN_PDP_VID4HCOEFF9_VID4HCOEFF9_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF9_VID4HCOEFF9_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF9_VID4HCOEFF9_SHIFT (0)
#define ODN_PDP_VID4HCOEFF9_VID4HCOEFF9_LENGTH (32)
#define ODN_PDP_VID4HCOEFF9_VID4HCOEFF9_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF10_OFFSET (0x0648)

/* PDP, VID4HCOEFF10, VID4HCOEFF10
*/
#define ODN_PDP_VID4HCOEFF10_VID4HCOEFF10_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF10_VID4HCOEFF10_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF10_VID4HCOEFF10_SHIFT (0)
#define ODN_PDP_VID4HCOEFF10_VID4HCOEFF10_LENGTH (32)
#define ODN_PDP_VID4HCOEFF10_VID4HCOEFF10_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF11_OFFSET (0x064C)

/* PDP, VID4HCOEFF11, VID4HCOEFF11
*/
#define ODN_PDP_VID4HCOEFF11_VID4HCOEFF11_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF11_VID4HCOEFF11_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF11_VID4HCOEFF11_SHIFT (0)
#define ODN_PDP_VID4HCOEFF11_VID4HCOEFF11_LENGTH (32)
#define ODN_PDP_VID4HCOEFF11_VID4HCOEFF11_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF12_OFFSET (0x0650)

/* PDP, VID4HCOEFF12, VID4HCOEFF12
*/
#define ODN_PDP_VID4HCOEFF12_VID4HCOEFF12_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF12_VID4HCOEFF12_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF12_VID4HCOEFF12_SHIFT (0)
#define ODN_PDP_VID4HCOEFF12_VID4HCOEFF12_LENGTH (32)
#define ODN_PDP_VID4HCOEFF12_VID4HCOEFF12_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF13_OFFSET (0x0654)

/* PDP, VID4HCOEFF13, VID4HCOEFF13
*/
#define ODN_PDP_VID4HCOEFF13_VID4HCOEFF13_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF13_VID4HCOEFF13_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF13_VID4HCOEFF13_SHIFT (0)
#define ODN_PDP_VID4HCOEFF13_VID4HCOEFF13_LENGTH (32)
#define ODN_PDP_VID4HCOEFF13_VID4HCOEFF13_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF14_OFFSET (0x0658)

/* PDP, VID4HCOEFF14, VID4HCOEFF14
*/
#define ODN_PDP_VID4HCOEFF14_VID4HCOEFF14_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF14_VID4HCOEFF14_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF14_VID4HCOEFF14_SHIFT (0)
#define ODN_PDP_VID4HCOEFF14_VID4HCOEFF14_LENGTH (32)
#define ODN_PDP_VID4HCOEFF14_VID4HCOEFF14_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF15_OFFSET (0x065C)

/* PDP, VID4HCOEFF15, VID4HCOEFF15
*/
#define ODN_PDP_VID4HCOEFF15_VID4HCOEFF15_MASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF15_VID4HCOEFF15_LSBMASK (0xFFFFFFFF)
#define ODN_PDP_VID4HCOEFF15_VID4HCOEFF15_SHIFT (0)
#define ODN_PDP_VID4HCOEFF15_VID4HCOEFF15_LENGTH (32)
#define ODN_PDP_VID4HCOEFF15_VID4HCOEFF15_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4HCOEFF16_OFFSET (0x0660)

/* PDP, VID4HCOEFF16, VID4HCOEFF16
*/
#define ODN_PDP_VID4HCOEFF16_VID4HCOEFF16_MASK (0x000000FF)
#define ODN_PDP_VID4HCOEFF16_VID4HCOEFF16_LSBMASK (0x000000FF)
#define ODN_PDP_VID4HCOEFF16_VID4HCOEFF16_SHIFT (0)
#define ODN_PDP_VID4HCOEFF16_VID4HCOEFF16_LENGTH (8)
#define ODN_PDP_VID4HCOEFF16_VID4HCOEFF16_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4SCALESIZE_OFFSET (0x0664)

/* PDP, VID4SCALESIZE, VID4SCALEWIDTH
*/
#define ODN_PDP_VID4SCALESIZE_VID4SCALEWIDTH_MASK (0x0FFF0000)
#define ODN_PDP_VID4SCALESIZE_VID4SCALEWIDTH_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4SCALESIZE_VID4SCALEWIDTH_SHIFT (16)
#define ODN_PDP_VID4SCALESIZE_VID4SCALEWIDTH_LENGTH (12)
#define ODN_PDP_VID4SCALESIZE_VID4SCALEWIDTH_SIGNED_FIELD IMG_FALSE

/* PDP, VID4SCALESIZE, VID4SCALEHEIGHT
*/
#define ODN_PDP_VID4SCALESIZE_VID4SCALEHEIGHT_MASK (0x00000FFF)
#define ODN_PDP_VID4SCALESIZE_VID4SCALEHEIGHT_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4SCALESIZE_VID4SCALEHEIGHT_SHIFT (0)
#define ODN_PDP_VID4SCALESIZE_VID4SCALEHEIGHT_LENGTH (12)
#define ODN_PDP_VID4SCALESIZE_VID4SCALEHEIGHT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PORTER_BLND0_OFFSET (0x0668)

/* PDP, PORTER_BLND0, BLND0BLENDTYPE
*/
#define ODN_PDP_PORTER_BLND0_BLND0BLENDTYPE_MASK (0x00000010)
#define ODN_PDP_PORTER_BLND0_BLND0BLENDTYPE_LSBMASK (0x00000001)
#define ODN_PDP_PORTER_BLND0_BLND0BLENDTYPE_SHIFT (4)
#define ODN_PDP_PORTER_BLND0_BLND0BLENDTYPE_LENGTH (1)
#define ODN_PDP_PORTER_BLND0_BLND0BLENDTYPE_SIGNED_FIELD IMG_FALSE

/* PDP, PORTER_BLND0, BLND0PORTERMODE
*/
#define ODN_PDP_PORTER_BLND0_BLND0PORTERMODE_MASK (0x0000000F)
#define ODN_PDP_PORTER_BLND0_BLND0PORTERMODE_LSBMASK (0x0000000F)
#define ODN_PDP_PORTER_BLND0_BLND0PORTERMODE_SHIFT (0)
#define ODN_PDP_PORTER_BLND0_BLND0PORTERMODE_LENGTH (4)
#define ODN_PDP_PORTER_BLND0_BLND0PORTERMODE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PORTER_BLND1_OFFSET (0x066C)

/* PDP, PORTER_BLND1, BLND1BLENDTYPE
*/
#define ODN_PDP_PORTER_BLND1_BLND1BLENDTYPE_MASK (0x00000010)
#define ODN_PDP_PORTER_BLND1_BLND1BLENDTYPE_LSBMASK (0x00000001)
#define ODN_PDP_PORTER_BLND1_BLND1BLENDTYPE_SHIFT (4)
#define ODN_PDP_PORTER_BLND1_BLND1BLENDTYPE_LENGTH (1)
#define ODN_PDP_PORTER_BLND1_BLND1BLENDTYPE_SIGNED_FIELD IMG_FALSE

/* PDP, PORTER_BLND1, BLND1PORTERMODE
*/
#define ODN_PDP_PORTER_BLND1_BLND1PORTERMODE_MASK (0x0000000F)
#define ODN_PDP_PORTER_BLND1_BLND1PORTERMODE_LSBMASK (0x0000000F)
#define ODN_PDP_PORTER_BLND1_BLND1PORTERMODE_SHIFT (0)
#define ODN_PDP_PORTER_BLND1_BLND1PORTERMODE_LENGTH (4)
#define ODN_PDP_PORTER_BLND1_BLND1PORTERMODE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PORTER_BLND2_OFFSET (0x0670)

/* PDP, PORTER_BLND2, BLND2BLENDTYPE
*/
#define ODN_PDP_PORTER_BLND2_BLND2BLENDTYPE_MASK (0x00000010)
#define ODN_PDP_PORTER_BLND2_BLND2BLENDTYPE_LSBMASK (0x00000001)
#define ODN_PDP_PORTER_BLND2_BLND2BLENDTYPE_SHIFT (4)
#define ODN_PDP_PORTER_BLND2_BLND2BLENDTYPE_LENGTH (1)
#define ODN_PDP_PORTER_BLND2_BLND2BLENDTYPE_SIGNED_FIELD IMG_FALSE

/* PDP, PORTER_BLND2, BLND2PORTERMODE
*/
#define ODN_PDP_PORTER_BLND2_BLND2PORTERMODE_MASK (0x0000000F)
#define ODN_PDP_PORTER_BLND2_BLND2PORTERMODE_LSBMASK (0x0000000F)
#define ODN_PDP_PORTER_BLND2_BLND2PORTERMODE_SHIFT (0)
#define ODN_PDP_PORTER_BLND2_BLND2PORTERMODE_LENGTH (4)
#define ODN_PDP_PORTER_BLND2_BLND2PORTERMODE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PORTER_BLND3_OFFSET (0x0674)

/* PDP, PORTER_BLND3, BLND3BLENDTYPE
*/
#define ODN_PDP_PORTER_BLND3_BLND3BLENDTYPE_MASK (0x00000010)
#define ODN_PDP_PORTER_BLND3_BLND3BLENDTYPE_LSBMASK (0x00000001)
#define ODN_PDP_PORTER_BLND3_BLND3BLENDTYPE_SHIFT (4)
#define ODN_PDP_PORTER_BLND3_BLND3BLENDTYPE_LENGTH (1)
#define ODN_PDP_PORTER_BLND3_BLND3BLENDTYPE_SIGNED_FIELD IMG_FALSE

/* PDP, PORTER_BLND3, BLND3PORTERMODE
*/
#define ODN_PDP_PORTER_BLND3_BLND3PORTERMODE_MASK (0x0000000F)
#define ODN_PDP_PORTER_BLND3_BLND3PORTERMODE_LSBMASK (0x0000000F)
#define ODN_PDP_PORTER_BLND3_BLND3PORTERMODE_SHIFT (0)
#define ODN_PDP_PORTER_BLND3_BLND3PORTERMODE_LENGTH (4)
#define ODN_PDP_PORTER_BLND3_BLND3PORTERMODE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PORTER_BLND4_OFFSET (0x0678)

/* PDP, PORTER_BLND4, BLND4BLENDTYPE
*/
#define ODN_PDP_PORTER_BLND4_BLND4BLENDTYPE_MASK (0x00000010)
#define ODN_PDP_PORTER_BLND4_BLND4BLENDTYPE_LSBMASK (0x00000001)
#define ODN_PDP_PORTER_BLND4_BLND4BLENDTYPE_SHIFT (4)
#define ODN_PDP_PORTER_BLND4_BLND4BLENDTYPE_LENGTH (1)
#define ODN_PDP_PORTER_BLND4_BLND4BLENDTYPE_SIGNED_FIELD IMG_FALSE

/* PDP, PORTER_BLND4, BLND4PORTERMODE
*/
#define ODN_PDP_PORTER_BLND4_BLND4PORTERMODE_MASK (0x0000000F)
#define ODN_PDP_PORTER_BLND4_BLND4PORTERMODE_LSBMASK (0x0000000F)
#define ODN_PDP_PORTER_BLND4_BLND4PORTERMODE_SHIFT (0)
#define ODN_PDP_PORTER_BLND4_BLND4PORTERMODE_LENGTH (4)
#define ODN_PDP_PORTER_BLND4_BLND4PORTERMODE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PORTER_BLND5_OFFSET (0x067C)

/* PDP, PORTER_BLND5, BLND5BLENDTYPE
*/
#define ODN_PDP_PORTER_BLND5_BLND5BLENDTYPE_MASK (0x00000010)
#define ODN_PDP_PORTER_BLND5_BLND5BLENDTYPE_LSBMASK (0x00000001)
#define ODN_PDP_PORTER_BLND5_BLND5BLENDTYPE_SHIFT (4)
#define ODN_PDP_PORTER_BLND5_BLND5BLENDTYPE_LENGTH (1)
#define ODN_PDP_PORTER_BLND5_BLND5BLENDTYPE_SIGNED_FIELD IMG_FALSE

/* PDP, PORTER_BLND5, BLND5PORTERMODE
*/
#define ODN_PDP_PORTER_BLND5_BLND5PORTERMODE_MASK (0x0000000F)
#define ODN_PDP_PORTER_BLND5_BLND5PORTERMODE_LSBMASK (0x0000000F)
#define ODN_PDP_PORTER_BLND5_BLND5PORTERMODE_SHIFT (0)
#define ODN_PDP_PORTER_BLND5_BLND5PORTERMODE_LENGTH (4)
#define ODN_PDP_PORTER_BLND5_BLND5PORTERMODE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PORTER_BLND6_OFFSET (0x0680)

/* PDP, PORTER_BLND6, BLND6BLENDTYPE
*/
#define ODN_PDP_PORTER_BLND6_BLND6BLENDTYPE_MASK (0x00000010)
#define ODN_PDP_PORTER_BLND6_BLND6BLENDTYPE_LSBMASK (0x00000001)
#define ODN_PDP_PORTER_BLND6_BLND6BLENDTYPE_SHIFT (4)
#define ODN_PDP_PORTER_BLND6_BLND6BLENDTYPE_LENGTH (1)
#define ODN_PDP_PORTER_BLND6_BLND6BLENDTYPE_SIGNED_FIELD IMG_FALSE

/* PDP, PORTER_BLND6, BLND6PORTERMODE
*/
#define ODN_PDP_PORTER_BLND6_BLND6PORTERMODE_MASK (0x0000000F)
#define ODN_PDP_PORTER_BLND6_BLND6PORTERMODE_LSBMASK (0x0000000F)
#define ODN_PDP_PORTER_BLND6_BLND6PORTERMODE_SHIFT (0)
#define ODN_PDP_PORTER_BLND6_BLND6PORTERMODE_LENGTH (4)
#define ODN_PDP_PORTER_BLND6_BLND6PORTERMODE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PORTER_BLND7_OFFSET (0x0684)

/* PDP, PORTER_BLND7, BLND7BLENDTYPE
*/
#define ODN_PDP_PORTER_BLND7_BLND7BLENDTYPE_MASK (0x00000010)
#define ODN_PDP_PORTER_BLND7_BLND7BLENDTYPE_LSBMASK (0x00000001)
#define ODN_PDP_PORTER_BLND7_BLND7BLENDTYPE_SHIFT (4)
#define ODN_PDP_PORTER_BLND7_BLND7BLENDTYPE_LENGTH (1)
#define ODN_PDP_PORTER_BLND7_BLND7BLENDTYPE_SIGNED_FIELD IMG_FALSE

/* PDP, PORTER_BLND7, BLND7PORTERMODE
*/
#define ODN_PDP_PORTER_BLND7_BLND7PORTERMODE_MASK (0x0000000F)
#define ODN_PDP_PORTER_BLND7_BLND7PORTERMODE_LSBMASK (0x0000000F)
#define ODN_PDP_PORTER_BLND7_BLND7PORTERMODE_SHIFT (0)
#define ODN_PDP_PORTER_BLND7_BLND7PORTERMODE_LENGTH (4)
#define ODN_PDP_PORTER_BLND7_BLND7PORTERMODE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_OFFSET (0x06C8)

/* PDP, VID1LUMAKEY_ALPHA_TRANS_OPAQUE, VID1LUMAKEYALPHA_TRANS
*/
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_TRANS_MASK \
	(0x03FF0000)
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_TRANS_LSBMASK \
	(0x000003FF)
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_TRANS_SHIFT (16)
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_TRANS_LENGTH \
	(10)
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_TRANS_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID1LUMAKEY_ALPHA_TRANS_OPAQUE, VID1LUMAKEYALPHA_OPAQUE
*/
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_OPAQUE_MASK \
	(0x000003FF)
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_OPAQUE_LSBMASK \
	(0x000003FF)
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_OPAQUE_SHIFT (0)
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_OPAQUE_LENGTH \
	(10)
#define ODN_PDP_VID1LUMAKEY_ALPHA_TRANS_OPAQUE_VID1LUMAKEYALPHA_OPAQUE_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_OFFSET (0x06CC)

/* PDP, VID1LUMAKEY_LUMA_MAX_MIN, VID1LUMAKEYYMAX
*/
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMAX_MASK (0x03FF0000)
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMAX_LSBMASK (0x000003FF)
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMAX_SHIFT (16)
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMAX_LENGTH (10)
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMAX_SIGNED_FIELD IMG_FALSE

/* PDP, VID1LUMAKEY_LUMA_MAX_MIN, VID1LUMAKEYYMIN
*/
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMIN_MASK (0x000003FF)
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMIN_LSBMASK (0x000003FF)
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMIN_SHIFT (0)
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMIN_LENGTH (10)
#define ODN_PDP_VID1LUMAKEY_LUMA_MAX_MIN_VID1LUMAKEYYMIN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1LUMAKEY_C_RG_OFFSET (0x06D0)

/* PDP, VID1LUMAKEY_C_RG, VID1LUMAKEYC_R
*/
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_R_MASK (0x0FFF0000)
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_R_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_R_SHIFT (16)
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_R_LENGTH (12)
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_R_SIGNED_FIELD IMG_FALSE

/* PDP, VID1LUMAKEY_C_RG, VID1LUMAKEYC_G
*/
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_G_MASK (0x00000FFF)
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_G_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_G_SHIFT (0)
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_G_LENGTH (12)
#define ODN_PDP_VID1LUMAKEY_C_RG_VID1LUMAKEYC_G_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1LUMAKEY_C_B_OFFSET (0x06D4)

/* PDP, VID1LUMAKEY_C_B, VID1LUMAKEYALPHAMULT
*/
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYALPHAMULT_MASK (0x20000000)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYALPHAMULT_LSBMASK (0x00000001)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYALPHAMULT_SHIFT (29)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYALPHAMULT_LENGTH (1)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYALPHAMULT_SIGNED_FIELD IMG_FALSE

/* PDP, VID1LUMAKEY_C_B, VID1LUMAKEYEN
*/
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYEN_MASK (0x10000000)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYEN_SHIFT (28)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYEN_LENGTH (1)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, VID1LUMAKEY_C_B, VID1LUMAKEYOUTOFF
*/
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYOUTOFF_MASK (0x03FF0000)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYOUTOFF_LSBMASK (0x000003FF)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYOUTOFF_SHIFT (16)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYOUTOFF_LENGTH (10)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYOUTOFF_SIGNED_FIELD IMG_FALSE

/* PDP, VID1LUMAKEY_C_B, VID1LUMAKEYC_B
*/
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYC_B_MASK (0x00000FFF)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYC_B_LSBMASK (0x00000FFF)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYC_B_SHIFT (0)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYC_B_LENGTH (12)
#define ODN_PDP_VID1LUMAKEY_C_B_VID1LUMAKEYC_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_OFFSET (0x06D8)

/* PDP, VID2LUMAKEY_ALPHA_TRANS_OPAQUE, VID2LUMAKEYALPHA_TRANS
*/
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_TRANS_MASK \
	(0x03FF0000)
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_TRANS_LSBMASK \
	(0x000003FF)
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_TRANS_SHIFT (16)
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_TRANS_LENGTH \
	(10)
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_TRANS_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID2LUMAKEY_ALPHA_TRANS_OPAQUE, VID2LUMAKEYALPHA_OPAQUE
*/
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_OPAQUE_MASK \
	(0x000003FF)
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_OPAQUE_LSBMASK \
	(0x000003FF)
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_OPAQUE_SHIFT (0)
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_OPAQUE_LENGTH \
	(10)
#define ODN_PDP_VID2LUMAKEY_ALPHA_TRANS_OPAQUE_VID2LUMAKEYALPHA_OPAQUE_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_OFFSET (0x06DC)

/* PDP, VID2LUMAKEY_LUMA_MAX_MIN, VID2LUMAKEYYMAX
*/
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMAX_MASK (0x03FF0000)
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMAX_LSBMASK (0x000003FF)
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMAX_SHIFT (16)
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMAX_LENGTH (10)
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMAX_SIGNED_FIELD IMG_FALSE

/* PDP, VID2LUMAKEY_LUMA_MAX_MIN, VID2LUMAKEYYMIN
*/
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMIN_MASK (0x000003FF)
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMIN_LSBMASK (0x000003FF)
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMIN_SHIFT (0)
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMIN_LENGTH (10)
#define ODN_PDP_VID2LUMAKEY_LUMA_MAX_MIN_VID2LUMAKEYYMIN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2LUMAKEY_C_RG_OFFSET (0x06E0)

/* PDP, VID2LUMAKEY_C_RG, VID2LUMAKEYC_R
*/
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_R_MASK (0x0FFF0000)
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_R_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_R_SHIFT (16)
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_R_LENGTH (12)
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_R_SIGNED_FIELD IMG_FALSE

/* PDP, VID2LUMAKEY_C_RG, VID2LUMAKEYC_G
*/
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_G_MASK (0x00000FFF)
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_G_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_G_SHIFT (0)
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_G_LENGTH (12)
#define ODN_PDP_VID2LUMAKEY_C_RG_VID2LUMAKEYC_G_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2LUMAKEY_C_B_OFFSET (0x06E4)

/* PDP, VID2LUMAKEY_C_B, VID2LUMAKEYALPHAMULT
*/
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYALPHAMULT_MASK (0x20000000)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYALPHAMULT_LSBMASK (0x00000001)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYALPHAMULT_SHIFT (29)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYALPHAMULT_LENGTH (1)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYALPHAMULT_SIGNED_FIELD IMG_FALSE

/* PDP, VID2LUMAKEY_C_B, VID2LUMAKEYEN
*/
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYEN_MASK (0x10000000)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYEN_SHIFT (28)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYEN_LENGTH (1)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, VID2LUMAKEY_C_B, VID2LUMAKEYOUTOFF
*/
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYOUTOFF_MASK (0x03FF0000)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYOUTOFF_LSBMASK (0x000003FF)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYOUTOFF_SHIFT (16)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYOUTOFF_LENGTH (10)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYOUTOFF_SIGNED_FIELD IMG_FALSE

/* PDP, VID2LUMAKEY_C_B, VID2LUMAKEYC_B
*/
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYC_B_MASK (0x00000FFF)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYC_B_LSBMASK (0x00000FFF)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYC_B_SHIFT (0)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYC_B_LENGTH (12)
#define ODN_PDP_VID2LUMAKEY_C_B_VID2LUMAKEYC_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_OFFSET (0x06E8)

/* PDP, VID3LUMAKEY_ALPHA_TRANS_OPAQUE, VID3LUMAKEYALPHA_TRANS
*/
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_TRANS_MASK \
	(0x03FF0000)
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_TRANS_LSBMASK \
	(0x000003FF)
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_TRANS_SHIFT (16)
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_TRANS_LENGTH \
	(10)
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_TRANS_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID3LUMAKEY_ALPHA_TRANS_OPAQUE, VID3LUMAKEYALPHA_OPAQUE
*/
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_OPAQUE_MASK \
	(0x000003FF)
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_OPAQUE_LSBMASK \
	(0x000003FF)
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_OPAQUE_SHIFT (0)
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_OPAQUE_LENGTH \
	(10)
#define ODN_PDP_VID3LUMAKEY_ALPHA_TRANS_OPAQUE_VID3LUMAKEYALPHA_OPAQUE_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_OFFSET (0x06EC)

/* PDP, VID3LUMAKEY_LUMA_MAX_MIN, VID3LUMAKEYYMAX
*/
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMAX_MASK (0x03FF0000)
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMAX_LSBMASK (0x000003FF)
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMAX_SHIFT (16)
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMAX_LENGTH (10)
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMAX_SIGNED_FIELD IMG_FALSE

/* PDP, VID3LUMAKEY_LUMA_MAX_MIN, VID3LUMAKEYYMIN
*/
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMIN_MASK (0x000003FF)
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMIN_LSBMASK (0x000003FF)
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMIN_SHIFT (0)
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMIN_LENGTH (10)
#define ODN_PDP_VID3LUMAKEY_LUMA_MAX_MIN_VID3LUMAKEYYMIN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3LUMAKEY_C_RG_OFFSET (0x06F0)

/* PDP, VID3LUMAKEY_C_RG, VID3LUMAKEYC_R
*/
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_R_MASK (0x0FFF0000)
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_R_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_R_SHIFT (16)
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_R_LENGTH (12)
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_R_SIGNED_FIELD IMG_FALSE

/* PDP, VID3LUMAKEY_C_RG, VID3LUMAKEYC_G
*/
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_G_MASK (0x00000FFF)
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_G_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_G_SHIFT (0)
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_G_LENGTH (12)
#define ODN_PDP_VID3LUMAKEY_C_RG_VID3LUMAKEYC_G_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3LUMAKEY_C_B_OFFSET (0x06F4)

/* PDP, VID3LUMAKEY_C_B, VID3LUMAKEYALPHAMULT
*/
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYALPHAMULT_MASK (0x20000000)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYALPHAMULT_LSBMASK (0x00000001)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYALPHAMULT_SHIFT (29)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYALPHAMULT_LENGTH (1)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYALPHAMULT_SIGNED_FIELD IMG_FALSE

/* PDP, VID3LUMAKEY_C_B, VID3LUMAKEYEN
*/
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYEN_MASK (0x10000000)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYEN_SHIFT (28)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYEN_LENGTH (1)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, VID3LUMAKEY_C_B, VID3LUMAKEYOUTOFF
*/
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYOUTOFF_MASK (0x03FF0000)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYOUTOFF_LSBMASK (0x000003FF)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYOUTOFF_SHIFT (16)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYOUTOFF_LENGTH (10)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYOUTOFF_SIGNED_FIELD IMG_FALSE

/* PDP, VID3LUMAKEY_C_B, VID3LUMAKEYC_B
*/
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYC_B_MASK (0x00000FFF)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYC_B_LSBMASK (0x00000FFF)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYC_B_SHIFT (0)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYC_B_LENGTH (12)
#define ODN_PDP_VID3LUMAKEY_C_B_VID3LUMAKEYC_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_OFFSET (0x06F8)

/* PDP, VID4LUMAKEY_ALPHA_TRANS_OPAQUE, VID4LUMAKEYALPHA_TRANS
*/
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_TRANS_MASK \
	(0x03FF0000)
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_TRANS_LSBMASK \
	(0x000003FF)
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_TRANS_SHIFT (16)
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_TRANS_LENGTH \
	(10)
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_TRANS_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID4LUMAKEY_ALPHA_TRANS_OPAQUE, VID4LUMAKEYALPHA_OPAQUE
*/
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_OPAQUE_MASK \
	(0x000003FF)
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_OPAQUE_LSBMASK \
	(0x000003FF)
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_OPAQUE_SHIFT (0)
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_OPAQUE_LENGTH \
	(10)
#define ODN_PDP_VID4LUMAKEY_ALPHA_TRANS_OPAQUE_VID4LUMAKEYALPHA_OPAQUE_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_OFFSET (0x06FC)

/* PDP, VID4LUMAKEY_LUMA_MAX_MIN, VID4LUMAKEYYMAX
*/
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMAX_MASK (0x03FF0000)
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMAX_LSBMASK (0x000003FF)
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMAX_SHIFT (16)
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMAX_LENGTH (10)
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMAX_SIGNED_FIELD IMG_FALSE

/* PDP, VID4LUMAKEY_LUMA_MAX_MIN, VID4LUMAKEYYMIN
*/
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMIN_MASK (0x000003FF)
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMIN_LSBMASK (0x000003FF)
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMIN_SHIFT (0)
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMIN_LENGTH (10)
#define ODN_PDP_VID4LUMAKEY_LUMA_MAX_MIN_VID4LUMAKEYYMIN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4LUMAKEY_C_RG_OFFSET (0x0700)

/* PDP, VID4LUMAKEY_C_RG, VID4LUMAKEYC_R
*/
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_R_MASK (0x0FFF0000)
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_R_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_R_SHIFT (16)
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_R_LENGTH (12)
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_R_SIGNED_FIELD IMG_FALSE

/* PDP, VID4LUMAKEY_C_RG, VID4LUMAKEYC_G
*/
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_G_MASK (0x00000FFF)
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_G_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_G_SHIFT (0)
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_G_LENGTH (12)
#define ODN_PDP_VID4LUMAKEY_C_RG_VID4LUMAKEYC_G_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4LUMAKEY_C_B_OFFSET (0x0704)

/* PDP, VID4LUMAKEY_C_B, VID4LUMAKEYALPHAMULT
*/
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYALPHAMULT_MASK (0x20000000)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYALPHAMULT_LSBMASK (0x00000001)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYALPHAMULT_SHIFT (29)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYALPHAMULT_LENGTH (1)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYALPHAMULT_SIGNED_FIELD IMG_FALSE

/* PDP, VID4LUMAKEY_C_B, VID4LUMAKEYEN
*/
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYEN_MASK (0x10000000)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYEN_LSBMASK (0x00000001)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYEN_SHIFT (28)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYEN_LENGTH (1)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYEN_SIGNED_FIELD IMG_FALSE

/* PDP, VID4LUMAKEY_C_B, VID4LUMAKEYOUTOFF
*/
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYOUTOFF_MASK (0x03FF0000)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYOUTOFF_LSBMASK (0x000003FF)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYOUTOFF_SHIFT (16)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYOUTOFF_LENGTH (10)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYOUTOFF_SIGNED_FIELD IMG_FALSE

/* PDP, VID4LUMAKEY_C_B, VID4LUMAKEYC_B
*/
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYC_B_MASK (0x00000FFF)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYC_B_LSBMASK (0x00000FFF)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYC_B_SHIFT (0)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYC_B_LENGTH (12)
#define ODN_PDP_VID4LUMAKEY_C_B_VID4LUMAKEYC_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CSCCOEFF0_OFFSET (0x0708)

/* PDP, CSCCOEFF0, CSCCOEFFRU
*/
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRU_MASK (0x003FF800)
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRU_LSBMASK (0x000007FF)
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRU_SHIFT (11)
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRU_LENGTH (11)
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRU_SIGNED_FIELD IMG_FALSE

/* PDP, CSCCOEFF0, CSCCOEFFRY
*/
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRY_MASK (0x000007FF)
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRY_LSBMASK (0x000007FF)
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRY_SHIFT (0)
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRY_LENGTH (11)
#define ODN_PDP_CSCCOEFF0_CSCCOEFFRY_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CSCCOEFF1_OFFSET (0x070C)

/* PDP, CSCCOEFF1, CSCCOEFFGY
*/
#define ODN_PDP_CSCCOEFF1_CSCCOEFFGY_MASK (0x003FF800)
#define ODN_PDP_CSCCOEFF1_CSCCOEFFGY_LSBMASK (0x000007FF)
#define ODN_PDP_CSCCOEFF1_CSCCOEFFGY_SHIFT (11)
#define ODN_PDP_CSCCOEFF1_CSCCOEFFGY_LENGTH (11)
#define ODN_PDP_CSCCOEFF1_CSCCOEFFGY_SIGNED_FIELD IMG_FALSE

/* PDP, CSCCOEFF1, CSCCOEFFRV
*/
#define ODN_PDP_CSCCOEFF1_CSCCOEFFRV_MASK (0x000007FF)
#define ODN_PDP_CSCCOEFF1_CSCCOEFFRV_LSBMASK (0x000007FF)
#define ODN_PDP_CSCCOEFF1_CSCCOEFFRV_SHIFT (0)
#define ODN_PDP_CSCCOEFF1_CSCCOEFFRV_LENGTH (11)
#define ODN_PDP_CSCCOEFF1_CSCCOEFFRV_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CSCCOEFF2_OFFSET (0x0710)

/* PDP, CSCCOEFF2, CSCCOEFFGV
*/
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGV_MASK (0x003FF800)
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGV_LSBMASK (0x000007FF)
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGV_SHIFT (11)
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGV_LENGTH (11)
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGV_SIGNED_FIELD IMG_FALSE

/* PDP, CSCCOEFF2, CSCCOEFFGU
*/
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGU_MASK (0x000007FF)
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGU_LSBMASK (0x000007FF)
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGU_SHIFT (0)
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGU_LENGTH (11)
#define ODN_PDP_CSCCOEFF2_CSCCOEFFGU_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CSCCOEFF3_OFFSET (0x0714)

/* PDP, CSCCOEFF3, CSCCOEFFBU
*/
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBU_MASK (0x003FF800)
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBU_LSBMASK (0x000007FF)
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBU_SHIFT (11)
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBU_LENGTH (11)
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBU_SIGNED_FIELD IMG_FALSE

/* PDP, CSCCOEFF3, CSCCOEFFBY
*/
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBY_MASK (0x000007FF)
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBY_LSBMASK (0x000007FF)
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBY_SHIFT (0)
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBY_LENGTH (11)
#define ODN_PDP_CSCCOEFF3_CSCCOEFFBY_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CSCCOEFF4_OFFSET (0x0718)

/* PDP, CSCCOEFF4, CSCCOEFFBV
*/
#define ODN_PDP_CSCCOEFF4_CSCCOEFFBV_MASK (0x000007FF)
#define ODN_PDP_CSCCOEFF4_CSCCOEFFBV_LSBMASK (0x000007FF)
#define ODN_PDP_CSCCOEFF4_CSCCOEFFBV_SHIFT (0)
#define ODN_PDP_CSCCOEFF4_CSCCOEFFBV_LENGTH (11)
#define ODN_PDP_CSCCOEFF4_CSCCOEFFBV_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_BGNDCOL_AR_OFFSET (0x071C)

/* PDP, BGNDCOL_AR, BGNDCOL_A
*/
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_A_MASK (0x03FF0000)
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_A_LSBMASK (0x000003FF)
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_A_SHIFT (16)
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_A_LENGTH (10)
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_A_SIGNED_FIELD IMG_FALSE

/* PDP, BGNDCOL_AR, BGNDCOL_R
*/
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_R_MASK (0x000003FF)
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_R_LSBMASK (0x000003FF)
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_R_SHIFT (0)
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_R_LENGTH (10)
#define ODN_PDP_BGNDCOL_AR_BGNDCOL_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_BGNDCOL_GB_OFFSET (0x0720)

/* PDP, BGNDCOL_GB, BGNDCOL_G
*/
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_G_MASK (0x03FF0000)
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_G_LSBMASK (0x000003FF)
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_G_SHIFT (16)
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_G_LENGTH (10)
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_G_SIGNED_FIELD IMG_FALSE

/* PDP, BGNDCOL_GB, BGNDCOL_B
*/
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_B_MASK (0x000003FF)
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_B_LSBMASK (0x000003FF)
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_B_SHIFT (0)
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_B_LENGTH (10)
#define ODN_PDP_BGNDCOL_GB_BGNDCOL_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_BORDCOL_R_OFFSET (0x0724)

/* PDP, BORDCOL_R, BORDCOL_R
*/
#define ODN_PDP_BORDCOL_R_BORDCOL_R_MASK (0x000003FF)
#define ODN_PDP_BORDCOL_R_BORDCOL_R_LSBMASK (0x000003FF)
#define ODN_PDP_BORDCOL_R_BORDCOL_R_SHIFT (0)
#define ODN_PDP_BORDCOL_R_BORDCOL_R_LENGTH (10)
#define ODN_PDP_BORDCOL_R_BORDCOL_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_BORDCOL_GB_OFFSET (0x0728)

/* PDP, BORDCOL_GB, BORDCOL_G
*/
#define ODN_PDP_BORDCOL_GB_BORDCOL_G_MASK (0x03FF0000)
#define ODN_PDP_BORDCOL_GB_BORDCOL_G_LSBMASK (0x000003FF)
#define ODN_PDP_BORDCOL_GB_BORDCOL_G_SHIFT (16)
#define ODN_PDP_BORDCOL_GB_BORDCOL_G_LENGTH (10)
#define ODN_PDP_BORDCOL_GB_BORDCOL_G_SIGNED_FIELD IMG_FALSE

/* PDP, BORDCOL_GB, BORDCOL_B
*/
#define ODN_PDP_BORDCOL_GB_BORDCOL_B_MASK (0x000003FF)
#define ODN_PDP_BORDCOL_GB_BORDCOL_B_LSBMASK (0x000003FF)
#define ODN_PDP_BORDCOL_GB_BORDCOL_B_SHIFT (0)
#define ODN_PDP_BORDCOL_GB_BORDCOL_B_LENGTH (10)
#define ODN_PDP_BORDCOL_GB_BORDCOL_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_LINESTAT_OFFSET (0x0734)

/* PDP, LINESTAT, LINENO
*/
#define ODN_PDP_LINESTAT_LINENO_MASK (0x00001FFF)
#define ODN_PDP_LINESTAT_LINENO_LSBMASK (0x00001FFF)
#define ODN_PDP_LINESTAT_LINENO_SHIFT (0)
#define ODN_PDP_LINESTAT_LINENO_LENGTH (13)
#define ODN_PDP_LINESTAT_LINENO_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_OFFSET (0x0738)

/* PDP, CR_ODN_PDP_PROCAMP_C11C12, CR_PROCAMP_C12
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C12_MASK (0x3FFF0000)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C12_LSBMASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C12_SHIFT (16)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C12_LENGTH (14)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C12_SIGNED_FIELD IMG_FALSE

/* PDP, CR_ODN_PDP_PROCAMP_C11C12, CR_PROCAMP_C11
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C11_MASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C11_LSBMASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C11_SHIFT (0)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C11_LENGTH (14)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C11C12_CR_PROCAMP_C11_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_OFFSET (0x073C)

/* PDP, CR_ODN_PDP_PROCAMP_C13C21, CR_PROCAMP_C21
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C21_MASK (0x3FFF0000)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C21_LSBMASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C21_SHIFT (16)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C21_LENGTH (14)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C21_SIGNED_FIELD IMG_FALSE

/* PDP, CR_ODN_PDP_PROCAMP_C13C21, CR_PROCAMP_C13
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C13_MASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C13_LSBMASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C13_SHIFT (0)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C13_LENGTH (14)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C13C21_CR_PROCAMP_C13_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_OFFSET (0x0740)

/* PDP, CR_ODN_PDP_PROCAMP_C22C23, CR_PROCAMP_C23
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C23_MASK (0x3FFF0000)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C23_LSBMASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C23_SHIFT (16)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C23_LENGTH (14)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C23_SIGNED_FIELD IMG_FALSE

/* PDP, CR_ODN_PDP_PROCAMP_C22C23, CR_PROCAMP_C22
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C22_MASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C22_LSBMASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C22_SHIFT (0)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C22_LENGTH (14)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C22C23_CR_PROCAMP_C22_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_OFFSET (0x0744)

/* PDP, CR_ODN_PDP_PROCAMP_C31C32, CR_PROCAMP_C32
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C32_MASK (0x3FFF0000)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C32_LSBMASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C32_SHIFT (16)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C32_LENGTH (14)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C32_SIGNED_FIELD IMG_FALSE

/* PDP, CR_ODN_PDP_PROCAMP_C31C32, CR_PROCAMP_C31
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C31_MASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C31_LSBMASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C31_SHIFT (0)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C31_LENGTH (14)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C31C32_CR_PROCAMP_C31_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_OFFSET (0x0748)

/* PDP, CR_ODN_PDP_PROCAMP_C33, CR_PROCAMP_C33
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_C33_MASK (0x3FFF0000)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_C33_LSBMASK (0x00003FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_C33_SHIFT (16)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_C33_LENGTH (14)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_C33_SIGNED_FIELD IMG_FALSE

/* PDP, CR_ODN_PDP_PROCAMP_C33, CR_PROCAMP_RANGE
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_RANGE_MASK (0x00000030)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_RANGE_LSBMASK (0x00000003)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_RANGE_SHIFT (4)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_RANGE_LENGTH (2)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_RANGE_SIGNED_FIELD IMG_FALSE

/* PDP, CR_ODN_PDP_PROCAMP_C33, CR_PROCAMP_EN
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_EN_MASK (0x00000001)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_EN_LSBMASK (0x00000001)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_EN_SHIFT (0)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_EN_LENGTH (1)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_C33_CR_PROCAMP_EN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_OFFSET (0x074C)

/* PDP, CR_ODN_PDP_PROCAMP_OUTOFFSET_BG, CR_PROCAMP_OUTOFF_G
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_G_MASK \
	(0x0FFF0000)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_G_LSBMASK \
	(0x00000FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_G_SHIFT (16)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_G_LENGTH (12)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_G_SIGNED_FIELD \
	IMG_FALSE

/* PDP, CR_ODN_PDP_PROCAMP_OUTOFFSET_BG, CR_PROCAMP_OUTOFF_B
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_B_MASK \
	(0x00000FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_B_LSBMASK \
	(0x00000FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_B_SHIFT (0)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_B_LENGTH (12)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_BG_CR_PROCAMP_OUTOFF_B_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_R_OFFSET (0x0750)

/* PDP, CR_ODN_PDP_PROCAMP_OUTOFFSET_R, CR_PROCAMP_OUTOFF_R
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_R_CR_PROCAMP_OUTOFF_R_MASK \
	(0x00000FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_R_CR_PROCAMP_OUTOFF_R_LSBMASK \
	(0x00000FFF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_R_CR_PROCAMP_OUTOFF_R_SHIFT (0)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_R_CR_PROCAMP_OUTOFF_R_LENGTH (12)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_OUTOFFSET_R_CR_PROCAMP_OUTOFF_R_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_OFFSET (0x0754)

/* PDP, CR_ODN_PDP_PROCAMP_INOFFSET_BG, CR_PROCAMP_INOFF_G
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_G_MASK \
	(0x03FF0000)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_G_LSBMASK \
	(0x000003FF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_G_SHIFT (16)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_G_LENGTH (10)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_G_SIGNED_FIELD \
	IMG_FALSE

/* PDP, CR_ODN_PDP_PROCAMP_INOFFSET_BG, CR_PROCAMP_INOFF_B
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_B_MASK \
	(0x000003FF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_B_LSBMASK \
	(0x000003FF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_B_SHIFT (0)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_B_LENGTH (10)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_BG_CR_PROCAMP_INOFF_B_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_R_OFFSET (0x0758)

/* PDP, CR_ODN_PDP_PROCAMP_INOFFSET_R, CR_PROCAMP_INOFF_R
*/
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_R_CR_PROCAMP_INOFF_R_MASK \
	(0x000003FF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_R_CR_PROCAMP_INOFF_R_LSBMASK \
	(0x000003FF)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_R_CR_PROCAMP_INOFF_R_SHIFT (0)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_R_CR_PROCAMP_INOFF_R_LENGTH (10)
#define ODN_PDP_CR_ODN_PDP_PROCAMP_INOFFSET_R_CR_PROCAMP_INOFF_R_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_SIGNAT_R_OFFSET (0x075C)

/* PDP, SIGNAT_R, SIGNATURE_R
*/
#define ODN_PDP_SIGNAT_R_SIGNATURE_R_MASK (0x000003FF)
#define ODN_PDP_SIGNAT_R_SIGNATURE_R_LSBMASK (0x000003FF)
#define ODN_PDP_SIGNAT_R_SIGNATURE_R_SHIFT (0)
#define ODN_PDP_SIGNAT_R_SIGNATURE_R_LENGTH (10)
#define ODN_PDP_SIGNAT_R_SIGNATURE_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_SIGNAT_GB_OFFSET (0x0760)

/* PDP, SIGNAT_GB, SIGNATURE_G
*/
#define ODN_PDP_SIGNAT_GB_SIGNATURE_G_MASK (0x03FF0000)
#define ODN_PDP_SIGNAT_GB_SIGNATURE_G_LSBMASK (0x000003FF)
#define ODN_PDP_SIGNAT_GB_SIGNATURE_G_SHIFT (16)
#define ODN_PDP_SIGNAT_GB_SIGNATURE_G_LENGTH (10)
#define ODN_PDP_SIGNAT_GB_SIGNATURE_G_SIGNED_FIELD IMG_FALSE

/* PDP, SIGNAT_GB, SIGNATURE_B
*/
#define ODN_PDP_SIGNAT_GB_SIGNATURE_B_MASK (0x000003FF)
#define ODN_PDP_SIGNAT_GB_SIGNATURE_B_LSBMASK (0x000003FF)
#define ODN_PDP_SIGNAT_GB_SIGNATURE_B_SHIFT (0)
#define ODN_PDP_SIGNAT_GB_SIGNATURE_B_LENGTH (10)
#define ODN_PDP_SIGNAT_GB_SIGNATURE_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_REGISTER_UPDATE_CTRL_OFFSET (0x0764)

/* PDP, REGISTER_UPDATE_CTRL, BYPASS_DOUBLE_BUFFERING
*/
#define ODN_PDP_REGISTER_UPDATE_CTRL_BYPASS_DOUBLE_BUFFERING_MASK (0x00000004)
#define ODN_PDP_REGISTER_UPDATE_CTRL_BYPASS_DOUBLE_BUFFERING_LSBMASK \
	(0x00000001)
#define ODN_PDP_REGISTER_UPDATE_CTRL_BYPASS_DOUBLE_BUFFERING_SHIFT (2)
#define ODN_PDP_REGISTER_UPDATE_CTRL_BYPASS_DOUBLE_BUFFERING_LENGTH (1)
#define ODN_PDP_REGISTER_UPDATE_CTRL_BYPASS_DOUBLE_BUFFERING_SIGNED_FIELD \
	IMG_FALSE

/* PDP, REGISTER_UPDATE_CTRL, REGISTERS_VALID
*/
#define ODN_PDP_REGISTER_UPDATE_CTRL_REGISTERS_VALID_MASK (0x00000002)
#define ODN_PDP_REGISTER_UPDATE_CTRL_REGISTERS_VALID_LSBMASK (0x00000001)
#define ODN_PDP_REGISTER_UPDATE_CTRL_REGISTERS_VALID_SHIFT (1)
#define ODN_PDP_REGISTER_UPDATE_CTRL_REGISTERS_VALID_LENGTH (1)
#define ODN_PDP_REGISTER_UPDATE_CTRL_REGISTERS_VALID_SIGNED_FIELD IMG_FALSE

/* PDP, REGISTER_UPDATE_CTRL, USE_VBLANK
*/
#define ODN_PDP_REGISTER_UPDATE_CTRL_USE_VBLANK_MASK (0x00000001)
#define ODN_PDP_REGISTER_UPDATE_CTRL_USE_VBLANK_LSBMASK (0x00000001)
#define ODN_PDP_REGISTER_UPDATE_CTRL_USE_VBLANK_SHIFT (0)
#define ODN_PDP_REGISTER_UPDATE_CTRL_USE_VBLANK_LENGTH (1)
#define ODN_PDP_REGISTER_UPDATE_CTRL_USE_VBLANK_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_REGISTER_UPDATE_STATUS_OFFSET (0x0768)

/* PDP, REGISTER_UPDATE_STATUS, REGISTERS_UPDATED
*/
#define ODN_PDP_REGISTER_UPDATE_STATUS_REGISTERS_UPDATED_MASK (0x00000002)
#define ODN_PDP_REGISTER_UPDATE_STATUS_REGISTERS_UPDATED_LSBMASK (0x00000001)
#define ODN_PDP_REGISTER_UPDATE_STATUS_REGISTERS_UPDATED_SHIFT (1)
#define ODN_PDP_REGISTER_UPDATE_STATUS_REGISTERS_UPDATED_LENGTH (1)
#define ODN_PDP_REGISTER_UPDATE_STATUS_REGISTERS_UPDATED_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DBGCTRL_OFFSET (0x076C)

/* PDP, DBGCTRL, DBG_READ
*/
#define ODN_PDP_DBGCTRL_DBG_READ_MASK (0x00000002)
#define ODN_PDP_DBGCTRL_DBG_READ_LSBMASK (0x00000001)
#define ODN_PDP_DBGCTRL_DBG_READ_SHIFT (1)
#define ODN_PDP_DBGCTRL_DBG_READ_LENGTH (1)
#define ODN_PDP_DBGCTRL_DBG_READ_SIGNED_FIELD IMG_FALSE

/* PDP, DBGCTRL, DBG_ENAB
*/
#define ODN_PDP_DBGCTRL_DBG_ENAB_MASK (0x00000001)
#define ODN_PDP_DBGCTRL_DBG_ENAB_LSBMASK (0x00000001)
#define ODN_PDP_DBGCTRL_DBG_ENAB_SHIFT (0)
#define ODN_PDP_DBGCTRL_DBG_ENAB_LENGTH (1)
#define ODN_PDP_DBGCTRL_DBG_ENAB_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DBGDATA_R_OFFSET (0x0770)

/* PDP, DBGDATA_R, DBG_DATA_R
*/
#define ODN_PDP_DBGDATA_R_DBG_DATA_R_MASK (0x000003FF)
#define ODN_PDP_DBGDATA_R_DBG_DATA_R_LSBMASK (0x000003FF)
#define ODN_PDP_DBGDATA_R_DBG_DATA_R_SHIFT (0)
#define ODN_PDP_DBGDATA_R_DBG_DATA_R_LENGTH (10)
#define ODN_PDP_DBGDATA_R_DBG_DATA_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DBGDATA_GB_OFFSET (0x0774)

/* PDP, DBGDATA_GB, DBG_DATA_G
*/
#define ODN_PDP_DBGDATA_GB_DBG_DATA_G_MASK (0x03FF0000)
#define ODN_PDP_DBGDATA_GB_DBG_DATA_G_LSBMASK (0x000003FF)
#define ODN_PDP_DBGDATA_GB_DBG_DATA_G_SHIFT (16)
#define ODN_PDP_DBGDATA_GB_DBG_DATA_G_LENGTH (10)
#define ODN_PDP_DBGDATA_GB_DBG_DATA_G_SIGNED_FIELD IMG_FALSE

/* PDP, DBGDATA_GB, DBG_DATA_B
*/
#define ODN_PDP_DBGDATA_GB_DBG_DATA_B_MASK (0x000003FF)
#define ODN_PDP_DBGDATA_GB_DBG_DATA_B_LSBMASK (0x000003FF)
#define ODN_PDP_DBGDATA_GB_DBG_DATA_B_SHIFT (0)
#define ODN_PDP_DBGDATA_GB_DBG_DATA_B_LENGTH (10)
#define ODN_PDP_DBGDATA_GB_DBG_DATA_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DBGSIDE_OFFSET (0x0778)

/* PDP, DBGSIDE, DBG_VAL
*/
#define ODN_PDP_DBGSIDE_DBG_VAL_MASK (0x00000008)
#define ODN_PDP_DBGSIDE_DBG_VAL_LSBMASK (0x00000001)
#define ODN_PDP_DBGSIDE_DBG_VAL_SHIFT (3)
#define ODN_PDP_DBGSIDE_DBG_VAL_LENGTH (1)
#define ODN_PDP_DBGSIDE_DBG_VAL_SIGNED_FIELD IMG_FALSE

/* PDP, DBGSIDE, DBG_SIDE
*/
#define ODN_PDP_DBGSIDE_DBG_SIDE_MASK (0x00000007)
#define ODN_PDP_DBGSIDE_DBG_SIDE_LSBMASK (0x00000007)
#define ODN_PDP_DBGSIDE_DBG_SIDE_SHIFT (0)
#define ODN_PDP_DBGSIDE_DBG_SIDE_LENGTH (3)
#define ODN_PDP_DBGSIDE_DBG_SIDE_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_OUTPUT_OFFSET (0x077C)

/* PDP, OUTPUT, EIGHT_BIT_OUTPUT
*/
#define ODN_PDP_OUTPUT_EIGHT_BIT_OUTPUT_MASK (0x00000002)
#define ODN_PDP_OUTPUT_EIGHT_BIT_OUTPUT_LSBMASK (0x00000001)
#define ODN_PDP_OUTPUT_EIGHT_BIT_OUTPUT_SHIFT (1)
#define ODN_PDP_OUTPUT_EIGHT_BIT_OUTPUT_LENGTH (1)
#define ODN_PDP_OUTPUT_EIGHT_BIT_OUTPUT_SIGNED_FIELD IMG_FALSE

/* PDP, OUTPUT, OUTPUT_CONFIG
*/
#define ODN_PDP_OUTPUT_OUTPUT_CONFIG_MASK (0x00000001)
#define ODN_PDP_OUTPUT_OUTPUT_CONFIG_LSBMASK (0x00000001)
#define ODN_PDP_OUTPUT_OUTPUT_CONFIG_SHIFT (0)
#define ODN_PDP_OUTPUT_OUTPUT_CONFIG_LENGTH (1)
#define ODN_PDP_OUTPUT_OUTPUT_CONFIG_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_SYNCCTRL_OFFSET (0x0780)

/* PDP, SYNCCTRL, SYNCACTIVE
*/
#define ODN_PDP_SYNCCTRL_SYNCACTIVE_MASK (0x80000000)
#define ODN_PDP_SYNCCTRL_SYNCACTIVE_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_SYNCACTIVE_SHIFT (31)
#define ODN_PDP_SYNCCTRL_SYNCACTIVE_LENGTH (1)
#define ODN_PDP_SYNCCTRL_SYNCACTIVE_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, ODN_PDP_RST
*/
#define ODN_PDP_SYNCCTRL_ODN_PDP_RST_MASK (0x20000000)
#define ODN_PDP_SYNCCTRL_ODN_PDP_RST_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_ODN_PDP_RST_SHIFT (29)
#define ODN_PDP_SYNCCTRL_ODN_PDP_RST_LENGTH (1)
#define ODN_PDP_SYNCCTRL_ODN_PDP_RST_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, POWERDN
*/
#define ODN_PDP_SYNCCTRL_POWERDN_MASK (0x10000000)
#define ODN_PDP_SYNCCTRL_POWERDN_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_POWERDN_SHIFT (28)
#define ODN_PDP_SYNCCTRL_POWERDN_LENGTH (1)
#define ODN_PDP_SYNCCTRL_POWERDN_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, LOWPWRMODE
*/
#define ODN_PDP_SYNCCTRL_LOWPWRMODE_MASK (0x08000000)
#define ODN_PDP_SYNCCTRL_LOWPWRMODE_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_LOWPWRMODE_SHIFT (27)
#define ODN_PDP_SYNCCTRL_LOWPWRMODE_LENGTH (1)
#define ODN_PDP_SYNCCTRL_LOWPWRMODE_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, UPDSYNCTRL
*/
#define ODN_PDP_SYNCCTRL_UPDSYNCTRL_MASK (0x04000000)
#define ODN_PDP_SYNCCTRL_UPDSYNCTRL_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_UPDSYNCTRL_SHIFT (26)
#define ODN_PDP_SYNCCTRL_UPDSYNCTRL_LENGTH (1)
#define ODN_PDP_SYNCCTRL_UPDSYNCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, UPDINTCTRL
*/
#define ODN_PDP_SYNCCTRL_UPDINTCTRL_MASK (0x02000000)
#define ODN_PDP_SYNCCTRL_UPDINTCTRL_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_UPDINTCTRL_SHIFT (25)
#define ODN_PDP_SYNCCTRL_UPDINTCTRL_LENGTH (1)
#define ODN_PDP_SYNCCTRL_UPDINTCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, UPDCTRL
*/
#define ODN_PDP_SYNCCTRL_UPDCTRL_MASK (0x01000000)
#define ODN_PDP_SYNCCTRL_UPDCTRL_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_UPDCTRL_SHIFT (24)
#define ODN_PDP_SYNCCTRL_UPDCTRL_LENGTH (1)
#define ODN_PDP_SYNCCTRL_UPDCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, UPDWAIT
*/
#define ODN_PDP_SYNCCTRL_UPDWAIT_MASK (0x000F0000)
#define ODN_PDP_SYNCCTRL_UPDWAIT_LSBMASK (0x0000000F)
#define ODN_PDP_SYNCCTRL_UPDWAIT_SHIFT (16)
#define ODN_PDP_SYNCCTRL_UPDWAIT_LENGTH (4)
#define ODN_PDP_SYNCCTRL_UPDWAIT_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, FIELD_EN
*/
#define ODN_PDP_SYNCCTRL_FIELD_EN_MASK (0x00002000)
#define ODN_PDP_SYNCCTRL_FIELD_EN_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_FIELD_EN_SHIFT (13)
#define ODN_PDP_SYNCCTRL_FIELD_EN_LENGTH (1)
#define ODN_PDP_SYNCCTRL_FIELD_EN_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, CSYNC_EN
*/
#define ODN_PDP_SYNCCTRL_CSYNC_EN_MASK (0x00001000)
#define ODN_PDP_SYNCCTRL_CSYNC_EN_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_CSYNC_EN_SHIFT (12)
#define ODN_PDP_SYNCCTRL_CSYNC_EN_LENGTH (1)
#define ODN_PDP_SYNCCTRL_CSYNC_EN_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, CLKPOL
*/
#define ODN_PDP_SYNCCTRL_CLKPOL_MASK (0x00000800)
#define ODN_PDP_SYNCCTRL_CLKPOL_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_CLKPOL_SHIFT (11)
#define ODN_PDP_SYNCCTRL_CLKPOL_LENGTH (1)
#define ODN_PDP_SYNCCTRL_CLKPOL_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, VS_SLAVE
*/
#define ODN_PDP_SYNCCTRL_VS_SLAVE_MASK (0x00000080)
#define ODN_PDP_SYNCCTRL_VS_SLAVE_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_VS_SLAVE_SHIFT (7)
#define ODN_PDP_SYNCCTRL_VS_SLAVE_LENGTH (1)
#define ODN_PDP_SYNCCTRL_VS_SLAVE_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, HS_SLAVE
*/
#define ODN_PDP_SYNCCTRL_HS_SLAVE_MASK (0x00000040)
#define ODN_PDP_SYNCCTRL_HS_SLAVE_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_HS_SLAVE_SHIFT (6)
#define ODN_PDP_SYNCCTRL_HS_SLAVE_LENGTH (1)
#define ODN_PDP_SYNCCTRL_HS_SLAVE_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, BLNKPOL
*/
#define ODN_PDP_SYNCCTRL_BLNKPOL_MASK (0x00000020)
#define ODN_PDP_SYNCCTRL_BLNKPOL_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_BLNKPOL_SHIFT (5)
#define ODN_PDP_SYNCCTRL_BLNKPOL_LENGTH (1)
#define ODN_PDP_SYNCCTRL_BLNKPOL_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, BLNKDIS
*/
#define ODN_PDP_SYNCCTRL_BLNKDIS_MASK (0x00000010)
#define ODN_PDP_SYNCCTRL_BLNKDIS_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_BLNKDIS_SHIFT (4)
#define ODN_PDP_SYNCCTRL_BLNKDIS_LENGTH (1)
#define ODN_PDP_SYNCCTRL_BLNKDIS_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, VSPOL
*/
#define ODN_PDP_SYNCCTRL_VSPOL_MASK (0x00000008)
#define ODN_PDP_SYNCCTRL_VSPOL_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_VSPOL_SHIFT (3)
#define ODN_PDP_SYNCCTRL_VSPOL_LENGTH (1)
#define ODN_PDP_SYNCCTRL_VSPOL_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, VSDIS
*/
#define ODN_PDP_SYNCCTRL_VSDIS_MASK (0x00000004)
#define ODN_PDP_SYNCCTRL_VSDIS_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_VSDIS_SHIFT (2)
#define ODN_PDP_SYNCCTRL_VSDIS_LENGTH (1)
#define ODN_PDP_SYNCCTRL_VSDIS_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, HSPOL
*/
#define ODN_PDP_SYNCCTRL_HSPOL_MASK (0x00000002)
#define ODN_PDP_SYNCCTRL_HSPOL_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_HSPOL_SHIFT (1)
#define ODN_PDP_SYNCCTRL_HSPOL_LENGTH (1)
#define ODN_PDP_SYNCCTRL_HSPOL_SIGNED_FIELD IMG_FALSE

/* PDP, SYNCCTRL, HSDIS
*/
#define ODN_PDP_SYNCCTRL_HSDIS_MASK (0x00000001)
#define ODN_PDP_SYNCCTRL_HSDIS_LSBMASK (0x00000001)
#define ODN_PDP_SYNCCTRL_HSDIS_SHIFT (0)
#define ODN_PDP_SYNCCTRL_HSDIS_LENGTH (1)
#define ODN_PDP_SYNCCTRL_HSDIS_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_HSYNC1_OFFSET (0x0784)

/* PDP, HSYNC1, HBPS
*/
#define ODN_PDP_HSYNC1_HBPS_MASK (0x1FFF0000)
#define ODN_PDP_HSYNC1_HBPS_LSBMASK (0x00001FFF)
#define ODN_PDP_HSYNC1_HBPS_SHIFT (16)
#define ODN_PDP_HSYNC1_HBPS_LENGTH (13)
#define ODN_PDP_HSYNC1_HBPS_SIGNED_FIELD IMG_FALSE

/* PDP, HSYNC1, HT
*/
#define ODN_PDP_HSYNC1_HT_MASK (0x00001FFF)
#define ODN_PDP_HSYNC1_HT_LSBMASK (0x00001FFF)
#define ODN_PDP_HSYNC1_HT_SHIFT (0)
#define ODN_PDP_HSYNC1_HT_LENGTH (13)
#define ODN_PDP_HSYNC1_HT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_HSYNC2_OFFSET (0x0788)

/* PDP, HSYNC2, HAS
*/
#define ODN_PDP_HSYNC2_HAS_MASK (0x1FFF0000)
#define ODN_PDP_HSYNC2_HAS_LSBMASK (0x00001FFF)
#define ODN_PDP_HSYNC2_HAS_SHIFT (16)
#define ODN_PDP_HSYNC2_HAS_LENGTH (13)
#define ODN_PDP_HSYNC2_HAS_SIGNED_FIELD IMG_FALSE

/* PDP, HSYNC2, HLBS
*/
#define ODN_PDP_HSYNC2_HLBS_MASK (0x00001FFF)
#define ODN_PDP_HSYNC2_HLBS_LSBMASK (0x00001FFF)
#define ODN_PDP_HSYNC2_HLBS_SHIFT (0)
#define ODN_PDP_HSYNC2_HLBS_LENGTH (13)
#define ODN_PDP_HSYNC2_HLBS_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_HSYNC3_OFFSET (0x078C)

/* PDP, HSYNC3, HFPS
*/
#define ODN_PDP_HSYNC3_HFPS_MASK (0x1FFF0000)
#define ODN_PDP_HSYNC3_HFPS_LSBMASK (0x00001FFF)
#define ODN_PDP_HSYNC3_HFPS_SHIFT (16)
#define ODN_PDP_HSYNC3_HFPS_LENGTH (13)
#define ODN_PDP_HSYNC3_HFPS_SIGNED_FIELD IMG_FALSE

/* PDP, HSYNC3, HRBS
*/
#define ODN_PDP_HSYNC3_HRBS_MASK (0x00001FFF)
#define ODN_PDP_HSYNC3_HRBS_LSBMASK (0x00001FFF)
#define ODN_PDP_HSYNC3_HRBS_SHIFT (0)
#define ODN_PDP_HSYNC3_HRBS_LENGTH (13)
#define ODN_PDP_HSYNC3_HRBS_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VSYNC1_OFFSET (0x0790)

/* PDP, VSYNC1, VBPS
*/
#define ODN_PDP_VSYNC1_VBPS_MASK (0x1FFF0000)
#define ODN_PDP_VSYNC1_VBPS_LSBMASK (0x00001FFF)
#define ODN_PDP_VSYNC1_VBPS_SHIFT (16)
#define ODN_PDP_VSYNC1_VBPS_LENGTH (13)
#define ODN_PDP_VSYNC1_VBPS_SIGNED_FIELD IMG_FALSE

/* PDP, VSYNC1, VT
*/
#define ODN_PDP_VSYNC1_VT_MASK (0x00001FFF)
#define ODN_PDP_VSYNC1_VT_LSBMASK (0x00001FFF)
#define ODN_PDP_VSYNC1_VT_SHIFT (0)
#define ODN_PDP_VSYNC1_VT_LENGTH (13)
#define ODN_PDP_VSYNC1_VT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VSYNC2_OFFSET (0x0794)

/* PDP, VSYNC2, VAS
*/
#define ODN_PDP_VSYNC2_VAS_MASK (0x1FFF0000)
#define ODN_PDP_VSYNC2_VAS_LSBMASK (0x00001FFF)
#define ODN_PDP_VSYNC2_VAS_SHIFT (16)
#define ODN_PDP_VSYNC2_VAS_LENGTH (13)
#define ODN_PDP_VSYNC2_VAS_SIGNED_FIELD IMG_FALSE

/* PDP, VSYNC2, VTBS
*/
#define ODN_PDP_VSYNC2_VTBS_MASK (0x00001FFF)
#define ODN_PDP_VSYNC2_VTBS_LSBMASK (0x00001FFF)
#define ODN_PDP_VSYNC2_VTBS_SHIFT (0)
#define ODN_PDP_VSYNC2_VTBS_LENGTH (13)
#define ODN_PDP_VSYNC2_VTBS_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VSYNC3_OFFSET (0x0798)

/* PDP, VSYNC3, VFPS
*/
#define ODN_PDP_VSYNC3_VFPS_MASK (0x1FFF0000)
#define ODN_PDP_VSYNC3_VFPS_LSBMASK (0x00001FFF)
#define ODN_PDP_VSYNC3_VFPS_SHIFT (16)
#define ODN_PDP_VSYNC3_VFPS_LENGTH (13)
#define ODN_PDP_VSYNC3_VFPS_SIGNED_FIELD IMG_FALSE

/* PDP, VSYNC3, VBBS
*/
#define ODN_PDP_VSYNC3_VBBS_MASK (0x00001FFF)
#define ODN_PDP_VSYNC3_VBBS_LSBMASK (0x00001FFF)
#define ODN_PDP_VSYNC3_VBBS_SHIFT (0)
#define ODN_PDP_VSYNC3_VBBS_LENGTH (13)
#define ODN_PDP_VSYNC3_VBBS_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_INTSTAT_OFFSET (0x079C)

/* PDP, INTSTAT, INTS_VID4ORUN
*/
#define ODN_PDP_INTSTAT_INTS_VID4ORUN_MASK (0x00080000)
#define ODN_PDP_INTSTAT_INTS_VID4ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VID4ORUN_SHIFT (19)
#define ODN_PDP_INTSTAT_INTS_VID4ORUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VID4ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_VID3ORUN
*/
#define ODN_PDP_INTSTAT_INTS_VID3ORUN_MASK (0x00040000)
#define ODN_PDP_INTSTAT_INTS_VID3ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VID3ORUN_SHIFT (18)
#define ODN_PDP_INTSTAT_INTS_VID3ORUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VID3ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_VID2ORUN
*/
#define ODN_PDP_INTSTAT_INTS_VID2ORUN_MASK (0x00020000)
#define ODN_PDP_INTSTAT_INTS_VID2ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VID2ORUN_SHIFT (17)
#define ODN_PDP_INTSTAT_INTS_VID2ORUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VID2ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_VID1ORUN
*/
#define ODN_PDP_INTSTAT_INTS_VID1ORUN_MASK (0x00010000)
#define ODN_PDP_INTSTAT_INTS_VID1ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VID1ORUN_SHIFT (16)
#define ODN_PDP_INTSTAT_INTS_VID1ORUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VID1ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_GRPH4ORUN
*/
#define ODN_PDP_INTSTAT_INTS_GRPH4ORUN_MASK (0x00008000)
#define ODN_PDP_INTSTAT_INTS_GRPH4ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_GRPH4ORUN_SHIFT (15)
#define ODN_PDP_INTSTAT_INTS_GRPH4ORUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_GRPH4ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_GRPH3ORUN
*/
#define ODN_PDP_INTSTAT_INTS_GRPH3ORUN_MASK (0x00004000)
#define ODN_PDP_INTSTAT_INTS_GRPH3ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_GRPH3ORUN_SHIFT (14)
#define ODN_PDP_INTSTAT_INTS_GRPH3ORUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_GRPH3ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_GRPH2ORUN
*/
#define ODN_PDP_INTSTAT_INTS_GRPH2ORUN_MASK (0x00002000)
#define ODN_PDP_INTSTAT_INTS_GRPH2ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_GRPH2ORUN_SHIFT (13)
#define ODN_PDP_INTSTAT_INTS_GRPH2ORUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_GRPH2ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_GRPH1ORUN
*/
#define ODN_PDP_INTSTAT_INTS_GRPH1ORUN_MASK (0x00001000)
#define ODN_PDP_INTSTAT_INTS_GRPH1ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_GRPH1ORUN_SHIFT (12)
#define ODN_PDP_INTSTAT_INTS_GRPH1ORUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_GRPH1ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_VID4URUN
*/
#define ODN_PDP_INTSTAT_INTS_VID4URUN_MASK (0x00000800)
#define ODN_PDP_INTSTAT_INTS_VID4URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VID4URUN_SHIFT (11)
#define ODN_PDP_INTSTAT_INTS_VID4URUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VID4URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_VID3URUN
*/
#define ODN_PDP_INTSTAT_INTS_VID3URUN_MASK (0x00000400)
#define ODN_PDP_INTSTAT_INTS_VID3URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VID3URUN_SHIFT (10)
#define ODN_PDP_INTSTAT_INTS_VID3URUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VID3URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_VID2URUN
*/
#define ODN_PDP_INTSTAT_INTS_VID2URUN_MASK (0x00000200)
#define ODN_PDP_INTSTAT_INTS_VID2URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VID2URUN_SHIFT (9)
#define ODN_PDP_INTSTAT_INTS_VID2URUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VID2URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_VID1URUN
*/
#define ODN_PDP_INTSTAT_INTS_VID1URUN_MASK (0x00000100)
#define ODN_PDP_INTSTAT_INTS_VID1URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VID1URUN_SHIFT (8)
#define ODN_PDP_INTSTAT_INTS_VID1URUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VID1URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_GRPH4URUN
*/
#define ODN_PDP_INTSTAT_INTS_GRPH4URUN_MASK (0x00000080)
#define ODN_PDP_INTSTAT_INTS_GRPH4URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_GRPH4URUN_SHIFT (7)
#define ODN_PDP_INTSTAT_INTS_GRPH4URUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_GRPH4URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_GRPH3URUN
*/
#define ODN_PDP_INTSTAT_INTS_GRPH3URUN_MASK (0x00000040)
#define ODN_PDP_INTSTAT_INTS_GRPH3URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_GRPH3URUN_SHIFT (6)
#define ODN_PDP_INTSTAT_INTS_GRPH3URUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_GRPH3URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_GRPH2URUN
*/
#define ODN_PDP_INTSTAT_INTS_GRPH2URUN_MASK (0x00000020)
#define ODN_PDP_INTSTAT_INTS_GRPH2URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_GRPH2URUN_SHIFT (5)
#define ODN_PDP_INTSTAT_INTS_GRPH2URUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_GRPH2URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_GRPH1URUN
*/
#define ODN_PDP_INTSTAT_INTS_GRPH1URUN_MASK (0x00000010)
#define ODN_PDP_INTSTAT_INTS_GRPH1URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_GRPH1URUN_SHIFT (4)
#define ODN_PDP_INTSTAT_INTS_GRPH1URUN_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_GRPH1URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_VBLNK1
*/
#define ODN_PDP_INTSTAT_INTS_VBLNK1_MASK (0x00000008)
#define ODN_PDP_INTSTAT_INTS_VBLNK1_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VBLNK1_SHIFT (3)
#define ODN_PDP_INTSTAT_INTS_VBLNK1_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VBLNK1_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_VBLNK0
*/
#define ODN_PDP_INTSTAT_INTS_VBLNK0_MASK (0x00000004)
#define ODN_PDP_INTSTAT_INTS_VBLNK0_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_VBLNK0_SHIFT (2)
#define ODN_PDP_INTSTAT_INTS_VBLNK0_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_VBLNK0_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_HBLNK1
*/
#define ODN_PDP_INTSTAT_INTS_HBLNK1_MASK (0x00000002)
#define ODN_PDP_INTSTAT_INTS_HBLNK1_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_HBLNK1_SHIFT (1)
#define ODN_PDP_INTSTAT_INTS_HBLNK1_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_HBLNK1_SIGNED_FIELD IMG_FALSE

/* PDP, INTSTAT, INTS_HBLNK0
*/
#define ODN_PDP_INTSTAT_INTS_HBLNK0_MASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_HBLNK0_LSBMASK (0x00000001)
#define ODN_PDP_INTSTAT_INTS_HBLNK0_SHIFT (0)
#define ODN_PDP_INTSTAT_INTS_HBLNK0_LENGTH (1)
#define ODN_PDP_INTSTAT_INTS_HBLNK0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_INTENAB_OFFSET (0x07A0)

/* PDP, INTENAB, INTEN_VID4ORUN
*/
#define ODN_PDP_INTENAB_INTEN_VID4ORUN_MASK (0x00080000)
#define ODN_PDP_INTENAB_INTEN_VID4ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VID4ORUN_SHIFT (19)
#define ODN_PDP_INTENAB_INTEN_VID4ORUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VID4ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_VID3ORUN
*/
#define ODN_PDP_INTENAB_INTEN_VID3ORUN_MASK (0x00040000)
#define ODN_PDP_INTENAB_INTEN_VID3ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VID3ORUN_SHIFT (18)
#define ODN_PDP_INTENAB_INTEN_VID3ORUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VID3ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_VID2ORUN
*/
#define ODN_PDP_INTENAB_INTEN_VID2ORUN_MASK (0x00020000)
#define ODN_PDP_INTENAB_INTEN_VID2ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VID2ORUN_SHIFT (17)
#define ODN_PDP_INTENAB_INTEN_VID2ORUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VID2ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_VID1ORUN
*/
#define ODN_PDP_INTENAB_INTEN_VID1ORUN_MASK (0x00010000)
#define ODN_PDP_INTENAB_INTEN_VID1ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VID1ORUN_SHIFT (16)
#define ODN_PDP_INTENAB_INTEN_VID1ORUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VID1ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_GRPH4ORUN
*/
#define ODN_PDP_INTENAB_INTEN_GRPH4ORUN_MASK (0x00008000)
#define ODN_PDP_INTENAB_INTEN_GRPH4ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_GRPH4ORUN_SHIFT (15)
#define ODN_PDP_INTENAB_INTEN_GRPH4ORUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_GRPH4ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_GRPH3ORUN
*/
#define ODN_PDP_INTENAB_INTEN_GRPH3ORUN_MASK (0x00004000)
#define ODN_PDP_INTENAB_INTEN_GRPH3ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_GRPH3ORUN_SHIFT (14)
#define ODN_PDP_INTENAB_INTEN_GRPH3ORUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_GRPH3ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_GRPH2ORUN
*/
#define ODN_PDP_INTENAB_INTEN_GRPH2ORUN_MASK (0x00002000)
#define ODN_PDP_INTENAB_INTEN_GRPH2ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_GRPH2ORUN_SHIFT (13)
#define ODN_PDP_INTENAB_INTEN_GRPH2ORUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_GRPH2ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_GRPH1ORUN
*/
#define ODN_PDP_INTENAB_INTEN_GRPH1ORUN_MASK (0x00001000)
#define ODN_PDP_INTENAB_INTEN_GRPH1ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_GRPH1ORUN_SHIFT (12)
#define ODN_PDP_INTENAB_INTEN_GRPH1ORUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_GRPH1ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_VID4URUN
*/
#define ODN_PDP_INTENAB_INTEN_VID4URUN_MASK (0x00000800)
#define ODN_PDP_INTENAB_INTEN_VID4URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VID4URUN_SHIFT (11)
#define ODN_PDP_INTENAB_INTEN_VID4URUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VID4URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_VID3URUN
*/
#define ODN_PDP_INTENAB_INTEN_VID3URUN_MASK (0x00000400)
#define ODN_PDP_INTENAB_INTEN_VID3URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VID3URUN_SHIFT (10)
#define ODN_PDP_INTENAB_INTEN_VID3URUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VID3URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_VID2URUN
*/
#define ODN_PDP_INTENAB_INTEN_VID2URUN_MASK (0x00000200)
#define ODN_PDP_INTENAB_INTEN_VID2URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VID2URUN_SHIFT (9)
#define ODN_PDP_INTENAB_INTEN_VID2URUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VID2URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_VID1URUN
*/
#define ODN_PDP_INTENAB_INTEN_VID1URUN_MASK (0x00000100)
#define ODN_PDP_INTENAB_INTEN_VID1URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VID1URUN_SHIFT (8)
#define ODN_PDP_INTENAB_INTEN_VID1URUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VID1URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_GRPH4URUN
*/
#define ODN_PDP_INTENAB_INTEN_GRPH4URUN_MASK (0x00000080)
#define ODN_PDP_INTENAB_INTEN_GRPH4URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_GRPH4URUN_SHIFT (7)
#define ODN_PDP_INTENAB_INTEN_GRPH4URUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_GRPH4URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_GRPH3URUN
*/
#define ODN_PDP_INTENAB_INTEN_GRPH3URUN_MASK (0x00000040)
#define ODN_PDP_INTENAB_INTEN_GRPH3URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_GRPH3URUN_SHIFT (6)
#define ODN_PDP_INTENAB_INTEN_GRPH3URUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_GRPH3URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_GRPH2URUN
*/
#define ODN_PDP_INTENAB_INTEN_GRPH2URUN_MASK (0x00000020)
#define ODN_PDP_INTENAB_INTEN_GRPH2URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_GRPH2URUN_SHIFT (5)
#define ODN_PDP_INTENAB_INTEN_GRPH2URUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_GRPH2URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_GRPH1URUN
*/
#define ODN_PDP_INTENAB_INTEN_GRPH1URUN_MASK (0x00000010)
#define ODN_PDP_INTENAB_INTEN_GRPH1URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_GRPH1URUN_SHIFT (4)
#define ODN_PDP_INTENAB_INTEN_GRPH1URUN_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_GRPH1URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_VBLNK1
*/
#define ODN_PDP_INTENAB_INTEN_VBLNK1_MASK (0x00000008)
#define ODN_PDP_INTENAB_INTEN_VBLNK1_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VBLNK1_SHIFT (3)
#define ODN_PDP_INTENAB_INTEN_VBLNK1_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VBLNK1_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_VBLNK0
*/
#define ODN_PDP_INTENAB_INTEN_VBLNK0_MASK (0x00000004)
#define ODN_PDP_INTENAB_INTEN_VBLNK0_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_VBLNK0_SHIFT (2)
#define ODN_PDP_INTENAB_INTEN_VBLNK0_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_VBLNK0_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_HBLNK1
*/
#define ODN_PDP_INTENAB_INTEN_HBLNK1_MASK (0x00000002)
#define ODN_PDP_INTENAB_INTEN_HBLNK1_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_HBLNK1_SHIFT (1)
#define ODN_PDP_INTENAB_INTEN_HBLNK1_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_HBLNK1_SIGNED_FIELD IMG_FALSE

/* PDP, INTENAB, INTEN_HBLNK0
*/
#define ODN_PDP_INTENAB_INTEN_HBLNK0_MASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_HBLNK0_LSBMASK (0x00000001)
#define ODN_PDP_INTENAB_INTEN_HBLNK0_SHIFT (0)
#define ODN_PDP_INTENAB_INTEN_HBLNK0_LENGTH (1)
#define ODN_PDP_INTENAB_INTEN_HBLNK0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_INTCLR_OFFSET (0x07A4)

/* PDP, INTCLR, INTCLR_VID4ORUN
*/
#define ODN_PDP_INTCLR_INTCLR_VID4ORUN_MASK (0x00080000)
#define ODN_PDP_INTCLR_INTCLR_VID4ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VID4ORUN_SHIFT (19)
#define ODN_PDP_INTCLR_INTCLR_VID4ORUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VID4ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_VID3ORUN
*/
#define ODN_PDP_INTCLR_INTCLR_VID3ORUN_MASK (0x00040000)
#define ODN_PDP_INTCLR_INTCLR_VID3ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VID3ORUN_SHIFT (18)
#define ODN_PDP_INTCLR_INTCLR_VID3ORUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VID3ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_VID2ORUN
*/
#define ODN_PDP_INTCLR_INTCLR_VID2ORUN_MASK (0x00020000)
#define ODN_PDP_INTCLR_INTCLR_VID2ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VID2ORUN_SHIFT (17)
#define ODN_PDP_INTCLR_INTCLR_VID2ORUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VID2ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_VID1ORUN
*/
#define ODN_PDP_INTCLR_INTCLR_VID1ORUN_MASK (0x00010000)
#define ODN_PDP_INTCLR_INTCLR_VID1ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VID1ORUN_SHIFT (16)
#define ODN_PDP_INTCLR_INTCLR_VID1ORUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VID1ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_GRPH4ORUN
*/
#define ODN_PDP_INTCLR_INTCLR_GRPH4ORUN_MASK (0x00008000)
#define ODN_PDP_INTCLR_INTCLR_GRPH4ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_GRPH4ORUN_SHIFT (15)
#define ODN_PDP_INTCLR_INTCLR_GRPH4ORUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_GRPH4ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_GRPH3ORUN
*/
#define ODN_PDP_INTCLR_INTCLR_GRPH3ORUN_MASK (0x00004000)
#define ODN_PDP_INTCLR_INTCLR_GRPH3ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_GRPH3ORUN_SHIFT (14)
#define ODN_PDP_INTCLR_INTCLR_GRPH3ORUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_GRPH3ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_GRPH2ORUN
*/
#define ODN_PDP_INTCLR_INTCLR_GRPH2ORUN_MASK (0x00002000)
#define ODN_PDP_INTCLR_INTCLR_GRPH2ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_GRPH2ORUN_SHIFT (13)
#define ODN_PDP_INTCLR_INTCLR_GRPH2ORUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_GRPH2ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_GRPH1ORUN
*/
#define ODN_PDP_INTCLR_INTCLR_GRPH1ORUN_MASK (0x00001000)
#define ODN_PDP_INTCLR_INTCLR_GRPH1ORUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_GRPH1ORUN_SHIFT (12)
#define ODN_PDP_INTCLR_INTCLR_GRPH1ORUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_GRPH1ORUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_VID4URUN
*/
#define ODN_PDP_INTCLR_INTCLR_VID4URUN_MASK (0x00000800)
#define ODN_PDP_INTCLR_INTCLR_VID4URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VID4URUN_SHIFT (11)
#define ODN_PDP_INTCLR_INTCLR_VID4URUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VID4URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_VID3URUN
*/
#define ODN_PDP_INTCLR_INTCLR_VID3URUN_MASK (0x00000400)
#define ODN_PDP_INTCLR_INTCLR_VID3URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VID3URUN_SHIFT (10)
#define ODN_PDP_INTCLR_INTCLR_VID3URUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VID3URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_VID2URUN
*/
#define ODN_PDP_INTCLR_INTCLR_VID2URUN_MASK (0x00000200)
#define ODN_PDP_INTCLR_INTCLR_VID2URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VID2URUN_SHIFT (9)
#define ODN_PDP_INTCLR_INTCLR_VID2URUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VID2URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_VID1URUN
*/
#define ODN_PDP_INTCLR_INTCLR_VID1URUN_MASK (0x00000100)
#define ODN_PDP_INTCLR_INTCLR_VID1URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VID1URUN_SHIFT (8)
#define ODN_PDP_INTCLR_INTCLR_VID1URUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VID1URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_GRPH4URUN
*/
#define ODN_PDP_INTCLR_INTCLR_GRPH4URUN_MASK (0x00000080)
#define ODN_PDP_INTCLR_INTCLR_GRPH4URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_GRPH4URUN_SHIFT (7)
#define ODN_PDP_INTCLR_INTCLR_GRPH4URUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_GRPH4URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_GRPH3URUN
*/
#define ODN_PDP_INTCLR_INTCLR_GRPH3URUN_MASK (0x00000040)
#define ODN_PDP_INTCLR_INTCLR_GRPH3URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_GRPH3URUN_SHIFT (6)
#define ODN_PDP_INTCLR_INTCLR_GRPH3URUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_GRPH3URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_GRPH2URUN
*/
#define ODN_PDP_INTCLR_INTCLR_GRPH2URUN_MASK (0x00000020)
#define ODN_PDP_INTCLR_INTCLR_GRPH2URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_GRPH2URUN_SHIFT (5)
#define ODN_PDP_INTCLR_INTCLR_GRPH2URUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_GRPH2URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_GRPH1URUN
*/
#define ODN_PDP_INTCLR_INTCLR_GRPH1URUN_MASK (0x00000010)
#define ODN_PDP_INTCLR_INTCLR_GRPH1URUN_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_GRPH1URUN_SHIFT (4)
#define ODN_PDP_INTCLR_INTCLR_GRPH1URUN_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_GRPH1URUN_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_VBLNK1
*/
#define ODN_PDP_INTCLR_INTCLR_VBLNK1_MASK (0x00000008)
#define ODN_PDP_INTCLR_INTCLR_VBLNK1_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VBLNK1_SHIFT (3)
#define ODN_PDP_INTCLR_INTCLR_VBLNK1_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VBLNK1_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_VBLNK0
*/
#define ODN_PDP_INTCLR_INTCLR_VBLNK0_MASK (0x00000004)
#define ODN_PDP_INTCLR_INTCLR_VBLNK0_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_VBLNK0_SHIFT (2)
#define ODN_PDP_INTCLR_INTCLR_VBLNK0_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_VBLNK0_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_HBLNK1
*/
#define ODN_PDP_INTCLR_INTCLR_HBLNK1_MASK (0x00000002)
#define ODN_PDP_INTCLR_INTCLR_HBLNK1_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_HBLNK1_SHIFT (1)
#define ODN_PDP_INTCLR_INTCLR_HBLNK1_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_HBLNK1_SIGNED_FIELD IMG_FALSE

/* PDP, INTCLR, INTCLR_HBLNK0
*/
#define ODN_PDP_INTCLR_INTCLR_HBLNK0_MASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_HBLNK0_LSBMASK (0x00000001)
#define ODN_PDP_INTCLR_INTCLR_HBLNK0_SHIFT (0)
#define ODN_PDP_INTCLR_INTCLR_HBLNK0_LENGTH (1)
#define ODN_PDP_INTCLR_INTCLR_HBLNK0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_MEMCTRL_OFFSET (0x07A8)

/* PDP, MEMCTRL, MEMREFRESH
*/
#define ODN_PDP_MEMCTRL_MEMREFRESH_MASK (0xC0000000)
#define ODN_PDP_MEMCTRL_MEMREFRESH_LSBMASK (0x00000003)
#define ODN_PDP_MEMCTRL_MEMREFRESH_SHIFT (30)
#define ODN_PDP_MEMCTRL_MEMREFRESH_LENGTH (2)
#define ODN_PDP_MEMCTRL_MEMREFRESH_SIGNED_FIELD IMG_FALSE

/* PDP, MEMCTRL, BURSTLEN
*/
#define ODN_PDP_MEMCTRL_BURSTLEN_MASK (0x000000FF)
#define ODN_PDP_MEMCTRL_BURSTLEN_LSBMASK (0x000000FF)
#define ODN_PDP_MEMCTRL_BURSTLEN_SHIFT (0)
#define ODN_PDP_MEMCTRL_BURSTLEN_LENGTH (8)
#define ODN_PDP_MEMCTRL_BURSTLEN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_MEM_THRESH_OFFSET (0x07AC)

/* PDP, MEM_THRESH, UVTHRESHOLD
*/
#define ODN_PDP_MEM_THRESH_UVTHRESHOLD_MASK (0xFF000000)
#define ODN_PDP_MEM_THRESH_UVTHRESHOLD_LSBMASK (0x000000FF)
#define ODN_PDP_MEM_THRESH_UVTHRESHOLD_SHIFT (24)
#define ODN_PDP_MEM_THRESH_UVTHRESHOLD_LENGTH (8)
#define ODN_PDP_MEM_THRESH_UVTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, MEM_THRESH, YTHRESHOLD
*/
#define ODN_PDP_MEM_THRESH_YTHRESHOLD_MASK (0x001FF000)
#define ODN_PDP_MEM_THRESH_YTHRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_MEM_THRESH_YTHRESHOLD_SHIFT (12)
#define ODN_PDP_MEM_THRESH_YTHRESHOLD_LENGTH (9)
#define ODN_PDP_MEM_THRESH_YTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, MEM_THRESH, THRESHOLD
*/
#define ODN_PDP_MEM_THRESH_THRESHOLD_MASK (0x000001FF)
#define ODN_PDP_MEM_THRESH_THRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_MEM_THRESH_THRESHOLD_SHIFT (0)
#define ODN_PDP_MEM_THRESH_THRESHOLD_LENGTH (9)
#define ODN_PDP_MEM_THRESH_THRESHOLD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_ALTERNATE_3D_CTRL_OFFSET (0x07B0)

/* PDP, ALTERNATE_3D_CTRL, ALT3D_ON
*/
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_ON_MASK (0x00000010)
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_ON_LSBMASK (0x00000001)
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_ON_SHIFT (4)
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_ON_LENGTH (1)
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_ON_SIGNED_FIELD IMG_FALSE

/* PDP, ALTERNATE_3D_CTRL, ALT3D_BLENDSEL
*/
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_BLENDSEL_MASK (0x00000007)
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_BLENDSEL_LSBMASK (0x00000007)
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_BLENDSEL_SHIFT (0)
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_BLENDSEL_LENGTH (3)
#define ODN_PDP_ALTERNATE_3D_CTRL_ALT3D_BLENDSEL_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA0_R_OFFSET (0x07B4)

/* PDP, GAMMA0_R, GAMMA0_R
*/
#define ODN_PDP_GAMMA0_R_GAMMA0_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA0_R_GAMMA0_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA0_R_GAMMA0_R_SHIFT (0)
#define ODN_PDP_GAMMA0_R_GAMMA0_R_LENGTH (10)
#define ODN_PDP_GAMMA0_R_GAMMA0_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA0_GB_OFFSET (0x07B8)

/* PDP, GAMMA0_GB, GAMMA0_G
*/
#define ODN_PDP_GAMMA0_GB_GAMMA0_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA0_GB_GAMMA0_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA0_GB_GAMMA0_G_SHIFT (16)
#define ODN_PDP_GAMMA0_GB_GAMMA0_G_LENGTH (10)
#define ODN_PDP_GAMMA0_GB_GAMMA0_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA0_GB, GAMMA0_B
*/
#define ODN_PDP_GAMMA0_GB_GAMMA0_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA0_GB_GAMMA0_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA0_GB_GAMMA0_B_SHIFT (0)
#define ODN_PDP_GAMMA0_GB_GAMMA0_B_LENGTH (10)
#define ODN_PDP_GAMMA0_GB_GAMMA0_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA1_R_OFFSET (0x07BC)

/* PDP, GAMMA1_R, GAMMA1_R
*/
#define ODN_PDP_GAMMA1_R_GAMMA1_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA1_R_GAMMA1_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA1_R_GAMMA1_R_SHIFT (0)
#define ODN_PDP_GAMMA1_R_GAMMA1_R_LENGTH (10)
#define ODN_PDP_GAMMA1_R_GAMMA1_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA1_GB_OFFSET (0x07C0)

/* PDP, GAMMA1_GB, GAMMA1_G
*/
#define ODN_PDP_GAMMA1_GB_GAMMA1_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA1_GB_GAMMA1_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA1_GB_GAMMA1_G_SHIFT (16)
#define ODN_PDP_GAMMA1_GB_GAMMA1_G_LENGTH (10)
#define ODN_PDP_GAMMA1_GB_GAMMA1_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA1_GB, GAMMA1_B
*/
#define ODN_PDP_GAMMA1_GB_GAMMA1_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA1_GB_GAMMA1_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA1_GB_GAMMA1_B_SHIFT (0)
#define ODN_PDP_GAMMA1_GB_GAMMA1_B_LENGTH (10)
#define ODN_PDP_GAMMA1_GB_GAMMA1_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA2_R_OFFSET (0x07C4)

/* PDP, GAMMA2_R, GAMMA2_R
*/
#define ODN_PDP_GAMMA2_R_GAMMA2_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA2_R_GAMMA2_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA2_R_GAMMA2_R_SHIFT (0)
#define ODN_PDP_GAMMA2_R_GAMMA2_R_LENGTH (10)
#define ODN_PDP_GAMMA2_R_GAMMA2_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA2_GB_OFFSET (0x07C8)

/* PDP, GAMMA2_GB, GAMMA2_G
*/
#define ODN_PDP_GAMMA2_GB_GAMMA2_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA2_GB_GAMMA2_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA2_GB_GAMMA2_G_SHIFT (16)
#define ODN_PDP_GAMMA2_GB_GAMMA2_G_LENGTH (10)
#define ODN_PDP_GAMMA2_GB_GAMMA2_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA2_GB, GAMMA2_B
*/
#define ODN_PDP_GAMMA2_GB_GAMMA2_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA2_GB_GAMMA2_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA2_GB_GAMMA2_B_SHIFT (0)
#define ODN_PDP_GAMMA2_GB_GAMMA2_B_LENGTH (10)
#define ODN_PDP_GAMMA2_GB_GAMMA2_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA3_R_OFFSET (0x07CC)

/* PDP, GAMMA3_R, GAMMA3_R
*/
#define ODN_PDP_GAMMA3_R_GAMMA3_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA3_R_GAMMA3_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA3_R_GAMMA3_R_SHIFT (0)
#define ODN_PDP_GAMMA3_R_GAMMA3_R_LENGTH (10)
#define ODN_PDP_GAMMA3_R_GAMMA3_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA3_GB_OFFSET (0x07D0)

/* PDP, GAMMA3_GB, GAMMA3_G
*/
#define ODN_PDP_GAMMA3_GB_GAMMA3_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA3_GB_GAMMA3_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA3_GB_GAMMA3_G_SHIFT (16)
#define ODN_PDP_GAMMA3_GB_GAMMA3_G_LENGTH (10)
#define ODN_PDP_GAMMA3_GB_GAMMA3_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA3_GB, GAMMA3_B
*/
#define ODN_PDP_GAMMA3_GB_GAMMA3_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA3_GB_GAMMA3_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA3_GB_GAMMA3_B_SHIFT (0)
#define ODN_PDP_GAMMA3_GB_GAMMA3_B_LENGTH (10)
#define ODN_PDP_GAMMA3_GB_GAMMA3_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA4_R_OFFSET (0x07D4)

/* PDP, GAMMA4_R, GAMMA4_R
*/
#define ODN_PDP_GAMMA4_R_GAMMA4_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA4_R_GAMMA4_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA4_R_GAMMA4_R_SHIFT (0)
#define ODN_PDP_GAMMA4_R_GAMMA4_R_LENGTH (10)
#define ODN_PDP_GAMMA4_R_GAMMA4_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA4_GB_OFFSET (0x07D8)

/* PDP, GAMMA4_GB, GAMMA4_G
*/
#define ODN_PDP_GAMMA4_GB_GAMMA4_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA4_GB_GAMMA4_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA4_GB_GAMMA4_G_SHIFT (16)
#define ODN_PDP_GAMMA4_GB_GAMMA4_G_LENGTH (10)
#define ODN_PDP_GAMMA4_GB_GAMMA4_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA4_GB, GAMMA4_B
*/
#define ODN_PDP_GAMMA4_GB_GAMMA4_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA4_GB_GAMMA4_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA4_GB_GAMMA4_B_SHIFT (0)
#define ODN_PDP_GAMMA4_GB_GAMMA4_B_LENGTH (10)
#define ODN_PDP_GAMMA4_GB_GAMMA4_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA5_R_OFFSET (0x07DC)

/* PDP, GAMMA5_R, GAMMA5_R
*/
#define ODN_PDP_GAMMA5_R_GAMMA5_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA5_R_GAMMA5_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA5_R_GAMMA5_R_SHIFT (0)
#define ODN_PDP_GAMMA5_R_GAMMA5_R_LENGTH (10)
#define ODN_PDP_GAMMA5_R_GAMMA5_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA5_GB_OFFSET (0x07E0)

/* PDP, GAMMA5_GB, GAMMA5_G
*/
#define ODN_PDP_GAMMA5_GB_GAMMA5_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA5_GB_GAMMA5_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA5_GB_GAMMA5_G_SHIFT (16)
#define ODN_PDP_GAMMA5_GB_GAMMA5_G_LENGTH (10)
#define ODN_PDP_GAMMA5_GB_GAMMA5_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA5_GB, GAMMA5_B
*/
#define ODN_PDP_GAMMA5_GB_GAMMA5_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA5_GB_GAMMA5_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA5_GB_GAMMA5_B_SHIFT (0)
#define ODN_PDP_GAMMA5_GB_GAMMA5_B_LENGTH (10)
#define ODN_PDP_GAMMA5_GB_GAMMA5_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA6_R_OFFSET (0x07E4)

/* PDP, GAMMA6_R, GAMMA6_R
*/
#define ODN_PDP_GAMMA6_R_GAMMA6_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA6_R_GAMMA6_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA6_R_GAMMA6_R_SHIFT (0)
#define ODN_PDP_GAMMA6_R_GAMMA6_R_LENGTH (10)
#define ODN_PDP_GAMMA6_R_GAMMA6_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA6_GB_OFFSET (0x07E8)

/* PDP, GAMMA6_GB, GAMMA6_G
*/
#define ODN_PDP_GAMMA6_GB_GAMMA6_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA6_GB_GAMMA6_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA6_GB_GAMMA6_G_SHIFT (16)
#define ODN_PDP_GAMMA6_GB_GAMMA6_G_LENGTH (10)
#define ODN_PDP_GAMMA6_GB_GAMMA6_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA6_GB, GAMMA6_B
*/
#define ODN_PDP_GAMMA6_GB_GAMMA6_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA6_GB_GAMMA6_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA6_GB_GAMMA6_B_SHIFT (0)
#define ODN_PDP_GAMMA6_GB_GAMMA6_B_LENGTH (10)
#define ODN_PDP_GAMMA6_GB_GAMMA6_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA7_R_OFFSET (0x07EC)

/* PDP, GAMMA7_R, GAMMA7_R
*/
#define ODN_PDP_GAMMA7_R_GAMMA7_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA7_R_GAMMA7_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA7_R_GAMMA7_R_SHIFT (0)
#define ODN_PDP_GAMMA7_R_GAMMA7_R_LENGTH (10)
#define ODN_PDP_GAMMA7_R_GAMMA7_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA7_GB_OFFSET (0x07F0)

/* PDP, GAMMA7_GB, GAMMA7_G
*/
#define ODN_PDP_GAMMA7_GB_GAMMA7_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA7_GB_GAMMA7_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA7_GB_GAMMA7_G_SHIFT (16)
#define ODN_PDP_GAMMA7_GB_GAMMA7_G_LENGTH (10)
#define ODN_PDP_GAMMA7_GB_GAMMA7_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA7_GB, GAMMA7_B
*/
#define ODN_PDP_GAMMA7_GB_GAMMA7_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA7_GB_GAMMA7_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA7_GB_GAMMA7_B_SHIFT (0)
#define ODN_PDP_GAMMA7_GB_GAMMA7_B_LENGTH (10)
#define ODN_PDP_GAMMA7_GB_GAMMA7_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA8_R_OFFSET (0x07F4)

/* PDP, GAMMA8_R, GAMMA8_R
*/
#define ODN_PDP_GAMMA8_R_GAMMA8_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA8_R_GAMMA8_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA8_R_GAMMA8_R_SHIFT (0)
#define ODN_PDP_GAMMA8_R_GAMMA8_R_LENGTH (10)
#define ODN_PDP_GAMMA8_R_GAMMA8_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA8_GB_OFFSET (0x07F8)

/* PDP, GAMMA8_GB, GAMMA8_G
*/
#define ODN_PDP_GAMMA8_GB_GAMMA8_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA8_GB_GAMMA8_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA8_GB_GAMMA8_G_SHIFT (16)
#define ODN_PDP_GAMMA8_GB_GAMMA8_G_LENGTH (10)
#define ODN_PDP_GAMMA8_GB_GAMMA8_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA8_GB, GAMMA8_B
*/
#define ODN_PDP_GAMMA8_GB_GAMMA8_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA8_GB_GAMMA8_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA8_GB_GAMMA8_B_SHIFT (0)
#define ODN_PDP_GAMMA8_GB_GAMMA8_B_LENGTH (10)
#define ODN_PDP_GAMMA8_GB_GAMMA8_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA9_R_OFFSET (0x07FC)

/* PDP, GAMMA9_R, GAMMA9_R
*/
#define ODN_PDP_GAMMA9_R_GAMMA9_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA9_R_GAMMA9_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA9_R_GAMMA9_R_SHIFT (0)
#define ODN_PDP_GAMMA9_R_GAMMA9_R_LENGTH (10)
#define ODN_PDP_GAMMA9_R_GAMMA9_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA9_GB_OFFSET (0x0800)

/* PDP, GAMMA9_GB, GAMMA9_G
*/
#define ODN_PDP_GAMMA9_GB_GAMMA9_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA9_GB_GAMMA9_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA9_GB_GAMMA9_G_SHIFT (16)
#define ODN_PDP_GAMMA9_GB_GAMMA9_G_LENGTH (10)
#define ODN_PDP_GAMMA9_GB_GAMMA9_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA9_GB, GAMMA9_B
*/
#define ODN_PDP_GAMMA9_GB_GAMMA9_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA9_GB_GAMMA9_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA9_GB_GAMMA9_B_SHIFT (0)
#define ODN_PDP_GAMMA9_GB_GAMMA9_B_LENGTH (10)
#define ODN_PDP_GAMMA9_GB_GAMMA9_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA10_R_OFFSET (0x0804)

/* PDP, GAMMA10_R, GAMMA10_R
*/
#define ODN_PDP_GAMMA10_R_GAMMA10_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA10_R_GAMMA10_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA10_R_GAMMA10_R_SHIFT (0)
#define ODN_PDP_GAMMA10_R_GAMMA10_R_LENGTH (10)
#define ODN_PDP_GAMMA10_R_GAMMA10_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA10_GB_OFFSET (0x0808)

/* PDP, GAMMA10_GB, GAMMA10_G
*/
#define ODN_PDP_GAMMA10_GB_GAMMA10_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA10_GB_GAMMA10_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA10_GB_GAMMA10_G_SHIFT (16)
#define ODN_PDP_GAMMA10_GB_GAMMA10_G_LENGTH (10)
#define ODN_PDP_GAMMA10_GB_GAMMA10_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA10_GB, GAMMA10_B
*/
#define ODN_PDP_GAMMA10_GB_GAMMA10_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA10_GB_GAMMA10_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA10_GB_GAMMA10_B_SHIFT (0)
#define ODN_PDP_GAMMA10_GB_GAMMA10_B_LENGTH (10)
#define ODN_PDP_GAMMA10_GB_GAMMA10_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA11_R_OFFSET (0x080C)

/* PDP, GAMMA11_R, GAMMA11_R
*/
#define ODN_PDP_GAMMA11_R_GAMMA11_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA11_R_GAMMA11_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA11_R_GAMMA11_R_SHIFT (0)
#define ODN_PDP_GAMMA11_R_GAMMA11_R_LENGTH (10)
#define ODN_PDP_GAMMA11_R_GAMMA11_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA11_GB_OFFSET (0x0810)

/* PDP, GAMMA11_GB, GAMMA11_G
*/
#define ODN_PDP_GAMMA11_GB_GAMMA11_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA11_GB_GAMMA11_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA11_GB_GAMMA11_G_SHIFT (16)
#define ODN_PDP_GAMMA11_GB_GAMMA11_G_LENGTH (10)
#define ODN_PDP_GAMMA11_GB_GAMMA11_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA11_GB, GAMMA11_B
*/
#define ODN_PDP_GAMMA11_GB_GAMMA11_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA11_GB_GAMMA11_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA11_GB_GAMMA11_B_SHIFT (0)
#define ODN_PDP_GAMMA11_GB_GAMMA11_B_LENGTH (10)
#define ODN_PDP_GAMMA11_GB_GAMMA11_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA12_R_OFFSET (0x0814)

/* PDP, GAMMA12_R, GAMMA12_R
*/
#define ODN_PDP_GAMMA12_R_GAMMA12_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA12_R_GAMMA12_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA12_R_GAMMA12_R_SHIFT (0)
#define ODN_PDP_GAMMA12_R_GAMMA12_R_LENGTH (10)
#define ODN_PDP_GAMMA12_R_GAMMA12_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA12_GB_OFFSET (0x0818)

/* PDP, GAMMA12_GB, GAMMA12_G
*/
#define ODN_PDP_GAMMA12_GB_GAMMA12_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA12_GB_GAMMA12_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA12_GB_GAMMA12_G_SHIFT (16)
#define ODN_PDP_GAMMA12_GB_GAMMA12_G_LENGTH (10)
#define ODN_PDP_GAMMA12_GB_GAMMA12_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA12_GB, GAMMA12_B
*/
#define ODN_PDP_GAMMA12_GB_GAMMA12_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA12_GB_GAMMA12_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA12_GB_GAMMA12_B_SHIFT (0)
#define ODN_PDP_GAMMA12_GB_GAMMA12_B_LENGTH (10)
#define ODN_PDP_GAMMA12_GB_GAMMA12_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA13_R_OFFSET (0x081C)

/* PDP, GAMMA13_R, GAMMA13_R
*/
#define ODN_PDP_GAMMA13_R_GAMMA13_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA13_R_GAMMA13_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA13_R_GAMMA13_R_SHIFT (0)
#define ODN_PDP_GAMMA13_R_GAMMA13_R_LENGTH (10)
#define ODN_PDP_GAMMA13_R_GAMMA13_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA13_GB_OFFSET (0x0820)

/* PDP, GAMMA13_GB, GAMMA13_G
*/
#define ODN_PDP_GAMMA13_GB_GAMMA13_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA13_GB_GAMMA13_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA13_GB_GAMMA13_G_SHIFT (16)
#define ODN_PDP_GAMMA13_GB_GAMMA13_G_LENGTH (10)
#define ODN_PDP_GAMMA13_GB_GAMMA13_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA13_GB, GAMMA13_B
*/
#define ODN_PDP_GAMMA13_GB_GAMMA13_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA13_GB_GAMMA13_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA13_GB_GAMMA13_B_SHIFT (0)
#define ODN_PDP_GAMMA13_GB_GAMMA13_B_LENGTH (10)
#define ODN_PDP_GAMMA13_GB_GAMMA13_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA14_R_OFFSET (0x0824)

/* PDP, GAMMA14_R, GAMMA14_R
*/
#define ODN_PDP_GAMMA14_R_GAMMA14_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA14_R_GAMMA14_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA14_R_GAMMA14_R_SHIFT (0)
#define ODN_PDP_GAMMA14_R_GAMMA14_R_LENGTH (10)
#define ODN_PDP_GAMMA14_R_GAMMA14_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA14_GB_OFFSET (0x0828)

/* PDP, GAMMA14_GB, GAMMA14_G
*/
#define ODN_PDP_GAMMA14_GB_GAMMA14_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA14_GB_GAMMA14_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA14_GB_GAMMA14_G_SHIFT (16)
#define ODN_PDP_GAMMA14_GB_GAMMA14_G_LENGTH (10)
#define ODN_PDP_GAMMA14_GB_GAMMA14_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA14_GB, GAMMA14_B
*/
#define ODN_PDP_GAMMA14_GB_GAMMA14_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA14_GB_GAMMA14_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA14_GB_GAMMA14_B_SHIFT (0)
#define ODN_PDP_GAMMA14_GB_GAMMA14_B_LENGTH (10)
#define ODN_PDP_GAMMA14_GB_GAMMA14_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA15_R_OFFSET (0x082C)

/* PDP, GAMMA15_R, GAMMA15_R
*/
#define ODN_PDP_GAMMA15_R_GAMMA15_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA15_R_GAMMA15_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA15_R_GAMMA15_R_SHIFT (0)
#define ODN_PDP_GAMMA15_R_GAMMA15_R_LENGTH (10)
#define ODN_PDP_GAMMA15_R_GAMMA15_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA15_GB_OFFSET (0x0830)

/* PDP, GAMMA15_GB, GAMMA15_G
*/
#define ODN_PDP_GAMMA15_GB_GAMMA15_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA15_GB_GAMMA15_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA15_GB_GAMMA15_G_SHIFT (16)
#define ODN_PDP_GAMMA15_GB_GAMMA15_G_LENGTH (10)
#define ODN_PDP_GAMMA15_GB_GAMMA15_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA15_GB, GAMMA15_B
*/
#define ODN_PDP_GAMMA15_GB_GAMMA15_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA15_GB_GAMMA15_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA15_GB_GAMMA15_B_SHIFT (0)
#define ODN_PDP_GAMMA15_GB_GAMMA15_B_LENGTH (10)
#define ODN_PDP_GAMMA15_GB_GAMMA15_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA16_R_OFFSET (0x0834)

/* PDP, GAMMA16_R, GAMMA16_R
*/
#define ODN_PDP_GAMMA16_R_GAMMA16_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA16_R_GAMMA16_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA16_R_GAMMA16_R_SHIFT (0)
#define ODN_PDP_GAMMA16_R_GAMMA16_R_LENGTH (10)
#define ODN_PDP_GAMMA16_R_GAMMA16_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA16_GB_OFFSET (0x0838)

/* PDP, GAMMA16_GB, GAMMA16_G
*/
#define ODN_PDP_GAMMA16_GB_GAMMA16_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA16_GB_GAMMA16_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA16_GB_GAMMA16_G_SHIFT (16)
#define ODN_PDP_GAMMA16_GB_GAMMA16_G_LENGTH (10)
#define ODN_PDP_GAMMA16_GB_GAMMA16_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA16_GB, GAMMA16_B
*/
#define ODN_PDP_GAMMA16_GB_GAMMA16_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA16_GB_GAMMA16_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA16_GB_GAMMA16_B_SHIFT (0)
#define ODN_PDP_GAMMA16_GB_GAMMA16_B_LENGTH (10)
#define ODN_PDP_GAMMA16_GB_GAMMA16_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA17_R_OFFSET (0x083C)

/* PDP, GAMMA17_R, GAMMA17_R
*/
#define ODN_PDP_GAMMA17_R_GAMMA17_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA17_R_GAMMA17_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA17_R_GAMMA17_R_SHIFT (0)
#define ODN_PDP_GAMMA17_R_GAMMA17_R_LENGTH (10)
#define ODN_PDP_GAMMA17_R_GAMMA17_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA17_GB_OFFSET (0x0840)

/* PDP, GAMMA17_GB, GAMMA17_G
*/
#define ODN_PDP_GAMMA17_GB_GAMMA17_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA17_GB_GAMMA17_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA17_GB_GAMMA17_G_SHIFT (16)
#define ODN_PDP_GAMMA17_GB_GAMMA17_G_LENGTH (10)
#define ODN_PDP_GAMMA17_GB_GAMMA17_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA17_GB, GAMMA17_B
*/
#define ODN_PDP_GAMMA17_GB_GAMMA17_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA17_GB_GAMMA17_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA17_GB_GAMMA17_B_SHIFT (0)
#define ODN_PDP_GAMMA17_GB_GAMMA17_B_LENGTH (10)
#define ODN_PDP_GAMMA17_GB_GAMMA17_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA18_R_OFFSET (0x0844)

/* PDP, GAMMA18_R, GAMMA18_R
*/
#define ODN_PDP_GAMMA18_R_GAMMA18_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA18_R_GAMMA18_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA18_R_GAMMA18_R_SHIFT (0)
#define ODN_PDP_GAMMA18_R_GAMMA18_R_LENGTH (10)
#define ODN_PDP_GAMMA18_R_GAMMA18_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA18_GB_OFFSET (0x0848)

/* PDP, GAMMA18_GB, GAMMA18_G
*/
#define ODN_PDP_GAMMA18_GB_GAMMA18_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA18_GB_GAMMA18_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA18_GB_GAMMA18_G_SHIFT (16)
#define ODN_PDP_GAMMA18_GB_GAMMA18_G_LENGTH (10)
#define ODN_PDP_GAMMA18_GB_GAMMA18_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA18_GB, GAMMA18_B
*/
#define ODN_PDP_GAMMA18_GB_GAMMA18_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA18_GB_GAMMA18_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA18_GB_GAMMA18_B_SHIFT (0)
#define ODN_PDP_GAMMA18_GB_GAMMA18_B_LENGTH (10)
#define ODN_PDP_GAMMA18_GB_GAMMA18_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA19_R_OFFSET (0x084C)

/* PDP, GAMMA19_R, GAMMA19_R
*/
#define ODN_PDP_GAMMA19_R_GAMMA19_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA19_R_GAMMA19_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA19_R_GAMMA19_R_SHIFT (0)
#define ODN_PDP_GAMMA19_R_GAMMA19_R_LENGTH (10)
#define ODN_PDP_GAMMA19_R_GAMMA19_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA19_GB_OFFSET (0x0850)

/* PDP, GAMMA19_GB, GAMMA19_G
*/
#define ODN_PDP_GAMMA19_GB_GAMMA19_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA19_GB_GAMMA19_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA19_GB_GAMMA19_G_SHIFT (16)
#define ODN_PDP_GAMMA19_GB_GAMMA19_G_LENGTH (10)
#define ODN_PDP_GAMMA19_GB_GAMMA19_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA19_GB, GAMMA19_B
*/
#define ODN_PDP_GAMMA19_GB_GAMMA19_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA19_GB_GAMMA19_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA19_GB_GAMMA19_B_SHIFT (0)
#define ODN_PDP_GAMMA19_GB_GAMMA19_B_LENGTH (10)
#define ODN_PDP_GAMMA19_GB_GAMMA19_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA20_R_OFFSET (0x0854)

/* PDP, GAMMA20_R, GAMMA20_R
*/
#define ODN_PDP_GAMMA20_R_GAMMA20_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA20_R_GAMMA20_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA20_R_GAMMA20_R_SHIFT (0)
#define ODN_PDP_GAMMA20_R_GAMMA20_R_LENGTH (10)
#define ODN_PDP_GAMMA20_R_GAMMA20_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA20_GB_OFFSET (0x0858)

/* PDP, GAMMA20_GB, GAMMA20_G
*/
#define ODN_PDP_GAMMA20_GB_GAMMA20_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA20_GB_GAMMA20_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA20_GB_GAMMA20_G_SHIFT (16)
#define ODN_PDP_GAMMA20_GB_GAMMA20_G_LENGTH (10)
#define ODN_PDP_GAMMA20_GB_GAMMA20_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA20_GB, GAMMA20_B
*/
#define ODN_PDP_GAMMA20_GB_GAMMA20_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA20_GB_GAMMA20_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA20_GB_GAMMA20_B_SHIFT (0)
#define ODN_PDP_GAMMA20_GB_GAMMA20_B_LENGTH (10)
#define ODN_PDP_GAMMA20_GB_GAMMA20_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA21_R_OFFSET (0x085C)

/* PDP, GAMMA21_R, GAMMA21_R
*/
#define ODN_PDP_GAMMA21_R_GAMMA21_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA21_R_GAMMA21_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA21_R_GAMMA21_R_SHIFT (0)
#define ODN_PDP_GAMMA21_R_GAMMA21_R_LENGTH (10)
#define ODN_PDP_GAMMA21_R_GAMMA21_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA21_GB_OFFSET (0x0860)

/* PDP, GAMMA21_GB, GAMMA21_G
*/
#define ODN_PDP_GAMMA21_GB_GAMMA21_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA21_GB_GAMMA21_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA21_GB_GAMMA21_G_SHIFT (16)
#define ODN_PDP_GAMMA21_GB_GAMMA21_G_LENGTH (10)
#define ODN_PDP_GAMMA21_GB_GAMMA21_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA21_GB, GAMMA21_B
*/
#define ODN_PDP_GAMMA21_GB_GAMMA21_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA21_GB_GAMMA21_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA21_GB_GAMMA21_B_SHIFT (0)
#define ODN_PDP_GAMMA21_GB_GAMMA21_B_LENGTH (10)
#define ODN_PDP_GAMMA21_GB_GAMMA21_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA22_R_OFFSET (0x0864)

/* PDP, GAMMA22_R, GAMMA22_R
*/
#define ODN_PDP_GAMMA22_R_GAMMA22_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA22_R_GAMMA22_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA22_R_GAMMA22_R_SHIFT (0)
#define ODN_PDP_GAMMA22_R_GAMMA22_R_LENGTH (10)
#define ODN_PDP_GAMMA22_R_GAMMA22_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA22_GB_OFFSET (0x0868)

/* PDP, GAMMA22_GB, GAMMA22_G
*/
#define ODN_PDP_GAMMA22_GB_GAMMA22_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA22_GB_GAMMA22_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA22_GB_GAMMA22_G_SHIFT (16)
#define ODN_PDP_GAMMA22_GB_GAMMA22_G_LENGTH (10)
#define ODN_PDP_GAMMA22_GB_GAMMA22_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA22_GB, GAMMA22_B
*/
#define ODN_PDP_GAMMA22_GB_GAMMA22_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA22_GB_GAMMA22_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA22_GB_GAMMA22_B_SHIFT (0)
#define ODN_PDP_GAMMA22_GB_GAMMA22_B_LENGTH (10)
#define ODN_PDP_GAMMA22_GB_GAMMA22_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA23_R_OFFSET (0x086C)

/* PDP, GAMMA23_R, GAMMA23_R
*/
#define ODN_PDP_GAMMA23_R_GAMMA23_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA23_R_GAMMA23_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA23_R_GAMMA23_R_SHIFT (0)
#define ODN_PDP_GAMMA23_R_GAMMA23_R_LENGTH (10)
#define ODN_PDP_GAMMA23_R_GAMMA23_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA23_GB_OFFSET (0x0870)

/* PDP, GAMMA23_GB, GAMMA23_G
*/
#define ODN_PDP_GAMMA23_GB_GAMMA23_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA23_GB_GAMMA23_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA23_GB_GAMMA23_G_SHIFT (16)
#define ODN_PDP_GAMMA23_GB_GAMMA23_G_LENGTH (10)
#define ODN_PDP_GAMMA23_GB_GAMMA23_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA23_GB, GAMMA23_B
*/
#define ODN_PDP_GAMMA23_GB_GAMMA23_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA23_GB_GAMMA23_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA23_GB_GAMMA23_B_SHIFT (0)
#define ODN_PDP_GAMMA23_GB_GAMMA23_B_LENGTH (10)
#define ODN_PDP_GAMMA23_GB_GAMMA23_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA24_R_OFFSET (0x0874)

/* PDP, GAMMA24_R, GAMMA24_R
*/
#define ODN_PDP_GAMMA24_R_GAMMA24_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA24_R_GAMMA24_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA24_R_GAMMA24_R_SHIFT (0)
#define ODN_PDP_GAMMA24_R_GAMMA24_R_LENGTH (10)
#define ODN_PDP_GAMMA24_R_GAMMA24_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA24_GB_OFFSET (0x0878)

/* PDP, GAMMA24_GB, GAMMA24_G
*/
#define ODN_PDP_GAMMA24_GB_GAMMA24_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA24_GB_GAMMA24_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA24_GB_GAMMA24_G_SHIFT (16)
#define ODN_PDP_GAMMA24_GB_GAMMA24_G_LENGTH (10)
#define ODN_PDP_GAMMA24_GB_GAMMA24_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA24_GB, GAMMA24_B
*/
#define ODN_PDP_GAMMA24_GB_GAMMA24_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA24_GB_GAMMA24_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA24_GB_GAMMA24_B_SHIFT (0)
#define ODN_PDP_GAMMA24_GB_GAMMA24_B_LENGTH (10)
#define ODN_PDP_GAMMA24_GB_GAMMA24_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA25_R_OFFSET (0x087C)

/* PDP, GAMMA25_R, GAMMA25_R
*/
#define ODN_PDP_GAMMA25_R_GAMMA25_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA25_R_GAMMA25_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA25_R_GAMMA25_R_SHIFT (0)
#define ODN_PDP_GAMMA25_R_GAMMA25_R_LENGTH (10)
#define ODN_PDP_GAMMA25_R_GAMMA25_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA25_GB_OFFSET (0x0880)

/* PDP, GAMMA25_GB, GAMMA25_G
*/
#define ODN_PDP_GAMMA25_GB_GAMMA25_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA25_GB_GAMMA25_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA25_GB_GAMMA25_G_SHIFT (16)
#define ODN_PDP_GAMMA25_GB_GAMMA25_G_LENGTH (10)
#define ODN_PDP_GAMMA25_GB_GAMMA25_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA25_GB, GAMMA25_B
*/
#define ODN_PDP_GAMMA25_GB_GAMMA25_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA25_GB_GAMMA25_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA25_GB_GAMMA25_B_SHIFT (0)
#define ODN_PDP_GAMMA25_GB_GAMMA25_B_LENGTH (10)
#define ODN_PDP_GAMMA25_GB_GAMMA25_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA26_R_OFFSET (0x0884)

/* PDP, GAMMA26_R, GAMMA26_R
*/
#define ODN_PDP_GAMMA26_R_GAMMA26_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA26_R_GAMMA26_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA26_R_GAMMA26_R_SHIFT (0)
#define ODN_PDP_GAMMA26_R_GAMMA26_R_LENGTH (10)
#define ODN_PDP_GAMMA26_R_GAMMA26_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA26_GB_OFFSET (0x0888)

/* PDP, GAMMA26_GB, GAMMA26_G
*/
#define ODN_PDP_GAMMA26_GB_GAMMA26_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA26_GB_GAMMA26_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA26_GB_GAMMA26_G_SHIFT (16)
#define ODN_PDP_GAMMA26_GB_GAMMA26_G_LENGTH (10)
#define ODN_PDP_GAMMA26_GB_GAMMA26_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA26_GB, GAMMA26_B
*/
#define ODN_PDP_GAMMA26_GB_GAMMA26_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA26_GB_GAMMA26_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA26_GB_GAMMA26_B_SHIFT (0)
#define ODN_PDP_GAMMA26_GB_GAMMA26_B_LENGTH (10)
#define ODN_PDP_GAMMA26_GB_GAMMA26_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA27_R_OFFSET (0x088C)

/* PDP, GAMMA27_R, GAMMA27_R
*/
#define ODN_PDP_GAMMA27_R_GAMMA27_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA27_R_GAMMA27_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA27_R_GAMMA27_R_SHIFT (0)
#define ODN_PDP_GAMMA27_R_GAMMA27_R_LENGTH (10)
#define ODN_PDP_GAMMA27_R_GAMMA27_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA27_GB_OFFSET (0x0890)

/* PDP, GAMMA27_GB, GAMMA27_G
*/
#define ODN_PDP_GAMMA27_GB_GAMMA27_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA27_GB_GAMMA27_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA27_GB_GAMMA27_G_SHIFT (16)
#define ODN_PDP_GAMMA27_GB_GAMMA27_G_LENGTH (10)
#define ODN_PDP_GAMMA27_GB_GAMMA27_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA27_GB, GAMMA27_B
*/
#define ODN_PDP_GAMMA27_GB_GAMMA27_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA27_GB_GAMMA27_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA27_GB_GAMMA27_B_SHIFT (0)
#define ODN_PDP_GAMMA27_GB_GAMMA27_B_LENGTH (10)
#define ODN_PDP_GAMMA27_GB_GAMMA27_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA28_R_OFFSET (0x0894)

/* PDP, GAMMA28_R, GAMMA28_R
*/
#define ODN_PDP_GAMMA28_R_GAMMA28_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA28_R_GAMMA28_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA28_R_GAMMA28_R_SHIFT (0)
#define ODN_PDP_GAMMA28_R_GAMMA28_R_LENGTH (10)
#define ODN_PDP_GAMMA28_R_GAMMA28_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA28_GB_OFFSET (0x0898)

/* PDP, GAMMA28_GB, GAMMA28_G
*/
#define ODN_PDP_GAMMA28_GB_GAMMA28_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA28_GB_GAMMA28_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA28_GB_GAMMA28_G_SHIFT (16)
#define ODN_PDP_GAMMA28_GB_GAMMA28_G_LENGTH (10)
#define ODN_PDP_GAMMA28_GB_GAMMA28_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA28_GB, GAMMA28_B
*/
#define ODN_PDP_GAMMA28_GB_GAMMA28_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA28_GB_GAMMA28_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA28_GB_GAMMA28_B_SHIFT (0)
#define ODN_PDP_GAMMA28_GB_GAMMA28_B_LENGTH (10)
#define ODN_PDP_GAMMA28_GB_GAMMA28_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA29_R_OFFSET (0x089C)

/* PDP, GAMMA29_R, GAMMA29_R
*/
#define ODN_PDP_GAMMA29_R_GAMMA29_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA29_R_GAMMA29_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA29_R_GAMMA29_R_SHIFT (0)
#define ODN_PDP_GAMMA29_R_GAMMA29_R_LENGTH (10)
#define ODN_PDP_GAMMA29_R_GAMMA29_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA29_GB_OFFSET (0x08A0)

/* PDP, GAMMA29_GB, GAMMA29_G
*/
#define ODN_PDP_GAMMA29_GB_GAMMA29_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA29_GB_GAMMA29_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA29_GB_GAMMA29_G_SHIFT (16)
#define ODN_PDP_GAMMA29_GB_GAMMA29_G_LENGTH (10)
#define ODN_PDP_GAMMA29_GB_GAMMA29_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA29_GB, GAMMA29_B
*/
#define ODN_PDP_GAMMA29_GB_GAMMA29_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA29_GB_GAMMA29_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA29_GB_GAMMA29_B_SHIFT (0)
#define ODN_PDP_GAMMA29_GB_GAMMA29_B_LENGTH (10)
#define ODN_PDP_GAMMA29_GB_GAMMA29_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA30_R_OFFSET (0x08A4)

/* PDP, GAMMA30_R, GAMMA30_R
*/
#define ODN_PDP_GAMMA30_R_GAMMA30_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA30_R_GAMMA30_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA30_R_GAMMA30_R_SHIFT (0)
#define ODN_PDP_GAMMA30_R_GAMMA30_R_LENGTH (10)
#define ODN_PDP_GAMMA30_R_GAMMA30_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA30_GB_OFFSET (0x08A8)

/* PDP, GAMMA30_GB, GAMMA30_G
*/
#define ODN_PDP_GAMMA30_GB_GAMMA30_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA30_GB_GAMMA30_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA30_GB_GAMMA30_G_SHIFT (16)
#define ODN_PDP_GAMMA30_GB_GAMMA30_G_LENGTH (10)
#define ODN_PDP_GAMMA30_GB_GAMMA30_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA30_GB, GAMMA30_B
*/
#define ODN_PDP_GAMMA30_GB_GAMMA30_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA30_GB_GAMMA30_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA30_GB_GAMMA30_B_SHIFT (0)
#define ODN_PDP_GAMMA30_GB_GAMMA30_B_LENGTH (10)
#define ODN_PDP_GAMMA30_GB_GAMMA30_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA31_R_OFFSET (0x08AC)

/* PDP, GAMMA31_R, GAMMA31_R
*/
#define ODN_PDP_GAMMA31_R_GAMMA31_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA31_R_GAMMA31_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA31_R_GAMMA31_R_SHIFT (0)
#define ODN_PDP_GAMMA31_R_GAMMA31_R_LENGTH (10)
#define ODN_PDP_GAMMA31_R_GAMMA31_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA31_GB_OFFSET (0x08B0)

/* PDP, GAMMA31_GB, GAMMA31_G
*/
#define ODN_PDP_GAMMA31_GB_GAMMA31_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA31_GB_GAMMA31_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA31_GB_GAMMA31_G_SHIFT (16)
#define ODN_PDP_GAMMA31_GB_GAMMA31_G_LENGTH (10)
#define ODN_PDP_GAMMA31_GB_GAMMA31_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA31_GB, GAMMA31_B
*/
#define ODN_PDP_GAMMA31_GB_GAMMA31_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA31_GB_GAMMA31_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA31_GB_GAMMA31_B_SHIFT (0)
#define ODN_PDP_GAMMA31_GB_GAMMA31_B_LENGTH (10)
#define ODN_PDP_GAMMA31_GB_GAMMA31_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA32_R_OFFSET (0x08B4)

/* PDP, GAMMA32_R, GAMMA32_R
*/
#define ODN_PDP_GAMMA32_R_GAMMA32_R_MASK (0x000003FF)
#define ODN_PDP_GAMMA32_R_GAMMA32_R_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA32_R_GAMMA32_R_SHIFT (0)
#define ODN_PDP_GAMMA32_R_GAMMA32_R_LENGTH (10)
#define ODN_PDP_GAMMA32_R_GAMMA32_R_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GAMMA32_GB_OFFSET (0x08B8)

/* PDP, GAMMA32_GB, GAMMA32_G
*/
#define ODN_PDP_GAMMA32_GB_GAMMA32_G_MASK (0x03FF0000)
#define ODN_PDP_GAMMA32_GB_GAMMA32_G_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA32_GB_GAMMA32_G_SHIFT (16)
#define ODN_PDP_GAMMA32_GB_GAMMA32_G_LENGTH (10)
#define ODN_PDP_GAMMA32_GB_GAMMA32_G_SIGNED_FIELD IMG_FALSE

/* PDP, GAMMA32_GB, GAMMA32_B
*/
#define ODN_PDP_GAMMA32_GB_GAMMA32_B_MASK (0x000003FF)
#define ODN_PDP_GAMMA32_GB_GAMMA32_B_LSBMASK (0x000003FF)
#define ODN_PDP_GAMMA32_GB_GAMMA32_B_SHIFT (0)
#define ODN_PDP_GAMMA32_GB_GAMMA32_B_LENGTH (10)
#define ODN_PDP_GAMMA32_GB_GAMMA32_B_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VEVENT_OFFSET (0x08BC)

/* PDP, VEVENT, VEVENT
*/
#define ODN_PDP_VEVENT_VEVENT_MASK (0x1FFF0000)
#define ODN_PDP_VEVENT_VEVENT_LSBMASK (0x00001FFF)
#define ODN_PDP_VEVENT_VEVENT_SHIFT (16)
#define ODN_PDP_VEVENT_VEVENT_LENGTH (13)
#define ODN_PDP_VEVENT_VEVENT_SIGNED_FIELD IMG_FALSE

/* PDP, VEVENT, VFETCH
*/
#define ODN_PDP_VEVENT_VFETCH_MASK (0x00001FFF)
#define ODN_PDP_VEVENT_VFETCH_LSBMASK (0x00001FFF)
#define ODN_PDP_VEVENT_VFETCH_SHIFT (0)
#define ODN_PDP_VEVENT_VFETCH_LENGTH (13)
#define ODN_PDP_VEVENT_VFETCH_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_HDECTRL_OFFSET (0x08C0)

/* PDP, HDECTRL, HDES
*/
#define ODN_PDP_HDECTRL_HDES_MASK (0x1FFF0000)
#define ODN_PDP_HDECTRL_HDES_LSBMASK (0x00001FFF)
#define ODN_PDP_HDECTRL_HDES_SHIFT (16)
#define ODN_PDP_HDECTRL_HDES_LENGTH (13)
#define ODN_PDP_HDECTRL_HDES_SIGNED_FIELD IMG_FALSE

/* PDP, HDECTRL, HDEF
*/
#define ODN_PDP_HDECTRL_HDEF_MASK (0x00001FFF)
#define ODN_PDP_HDECTRL_HDEF_LSBMASK (0x00001FFF)
#define ODN_PDP_HDECTRL_HDEF_SHIFT (0)
#define ODN_PDP_HDECTRL_HDEF_LENGTH (13)
#define ODN_PDP_HDECTRL_HDEF_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VDECTRL_OFFSET (0x08C4)

/* PDP, VDECTRL, VDES
*/
#define ODN_PDP_VDECTRL_VDES_MASK (0x1FFF0000)
#define ODN_PDP_VDECTRL_VDES_LSBMASK (0x00001FFF)
#define ODN_PDP_VDECTRL_VDES_SHIFT (16)
#define ODN_PDP_VDECTRL_VDES_LENGTH (13)
#define ODN_PDP_VDECTRL_VDES_SIGNED_FIELD IMG_FALSE

/* PDP, VDECTRL, VDEF
*/
#define ODN_PDP_VDECTRL_VDEF_MASK (0x00001FFF)
#define ODN_PDP_VDECTRL_VDEF_LSBMASK (0x00001FFF)
#define ODN_PDP_VDECTRL_VDEF_SHIFT (0)
#define ODN_PDP_VDECTRL_VDEF_LENGTH (13)
#define ODN_PDP_VDECTRL_VDEF_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_OPMASK_R_OFFSET (0x08C8)

/* PDP, OPMASK_R, MASKLEVEL
*/
#define ODN_PDP_OPMASK_R_MASKLEVEL_MASK (0x80000000)
#define ODN_PDP_OPMASK_R_MASKLEVEL_LSBMASK (0x00000001)
#define ODN_PDP_OPMASK_R_MASKLEVEL_SHIFT (31)
#define ODN_PDP_OPMASK_R_MASKLEVEL_LENGTH (1)
#define ODN_PDP_OPMASK_R_MASKLEVEL_SIGNED_FIELD IMG_FALSE

/* PDP, OPMASK_R, BLANKLEVEL
*/
#define ODN_PDP_OPMASK_R_BLANKLEVEL_MASK (0x40000000)
#define ODN_PDP_OPMASK_R_BLANKLEVEL_LSBMASK (0x00000001)
#define ODN_PDP_OPMASK_R_BLANKLEVEL_SHIFT (30)
#define ODN_PDP_OPMASK_R_BLANKLEVEL_LENGTH (1)
#define ODN_PDP_OPMASK_R_BLANKLEVEL_SIGNED_FIELD IMG_FALSE

/* PDP, OPMASK_R, MASKR
*/
#define ODN_PDP_OPMASK_R_MASKR_MASK (0x000003FF)
#define ODN_PDP_OPMASK_R_MASKR_LSBMASK (0x000003FF)
#define ODN_PDP_OPMASK_R_MASKR_SHIFT (0)
#define ODN_PDP_OPMASK_R_MASKR_LENGTH (10)
#define ODN_PDP_OPMASK_R_MASKR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_OPMASK_GB_OFFSET (0x08CC)

/* PDP, OPMASK_GB, MASKG
*/
#define ODN_PDP_OPMASK_GB_MASKG_MASK (0x03FF0000)
#define ODN_PDP_OPMASK_GB_MASKG_LSBMASK (0x000003FF)
#define ODN_PDP_OPMASK_GB_MASKG_SHIFT (16)
#define ODN_PDP_OPMASK_GB_MASKG_LENGTH (10)
#define ODN_PDP_OPMASK_GB_MASKG_SIGNED_FIELD IMG_FALSE

/* PDP, OPMASK_GB, MASKB
*/
#define ODN_PDP_OPMASK_GB_MASKB_MASK (0x000003FF)
#define ODN_PDP_OPMASK_GB_MASKB_LSBMASK (0x000003FF)
#define ODN_PDP_OPMASK_GB_MASKB_SHIFT (0)
#define ODN_PDP_OPMASK_GB_MASKB_LENGTH (10)
#define ODN_PDP_OPMASK_GB_MASKB_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_REGLD_ADDR_CTRL_OFFSET (0x08D0)

/* PDP, REGLD_ADDR_CTRL, REGLD_ADDRIN
*/
#define ODN_PDP_REGLD_ADDR_CTRL_REGLD_ADDRIN_MASK (0xFFFFFFF0)
#define ODN_PDP_REGLD_ADDR_CTRL_REGLD_ADDRIN_LSBMASK (0x0FFFFFFF)
#define ODN_PDP_REGLD_ADDR_CTRL_REGLD_ADDRIN_SHIFT (4)
#define ODN_PDP_REGLD_ADDR_CTRL_REGLD_ADDRIN_LENGTH (28)
#define ODN_PDP_REGLD_ADDR_CTRL_REGLD_ADDRIN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_REGLD_ADDR_STAT_OFFSET (0x08D4)

/* PDP, REGLD_ADDR_STAT, REGLD_ADDROUT
*/
#define ODN_PDP_REGLD_ADDR_STAT_REGLD_ADDROUT_MASK (0xFFFFFFF0)
#define ODN_PDP_REGLD_ADDR_STAT_REGLD_ADDROUT_LSBMASK (0x0FFFFFFF)
#define ODN_PDP_REGLD_ADDR_STAT_REGLD_ADDROUT_SHIFT (4)
#define ODN_PDP_REGLD_ADDR_STAT_REGLD_ADDROUT_LENGTH (28)
#define ODN_PDP_REGLD_ADDR_STAT_REGLD_ADDROUT_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_REGLD_STAT_OFFSET (0x08D8)

/* PDP, REGLD_STAT, REGLD_ADDREN
*/
#define ODN_PDP_REGLD_STAT_REGLD_ADDREN_MASK (0x00800000)
#define ODN_PDP_REGLD_STAT_REGLD_ADDREN_LSBMASK (0x00000001)
#define ODN_PDP_REGLD_STAT_REGLD_ADDREN_SHIFT (23)
#define ODN_PDP_REGLD_STAT_REGLD_ADDREN_LENGTH (1)
#define ODN_PDP_REGLD_STAT_REGLD_ADDREN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_REGLD_CTRL_OFFSET (0x08DC)

/* PDP, REGLD_CTRL, REGLD_ADDRLEN
*/
#define ODN_PDP_REGLD_CTRL_REGLD_ADDRLEN_MASK (0xFF000000)
#define ODN_PDP_REGLD_CTRL_REGLD_ADDRLEN_LSBMASK (0x000000FF)
#define ODN_PDP_REGLD_CTRL_REGLD_ADDRLEN_SHIFT (24)
#define ODN_PDP_REGLD_CTRL_REGLD_ADDRLEN_LENGTH (8)
#define ODN_PDP_REGLD_CTRL_REGLD_ADDRLEN_SIGNED_FIELD IMG_FALSE

/* PDP, REGLD_CTRL, REGLD_VAL
*/
#define ODN_PDP_REGLD_CTRL_REGLD_VAL_MASK (0x00800000)
#define ODN_PDP_REGLD_CTRL_REGLD_VAL_LSBMASK (0x00000001)
#define ODN_PDP_REGLD_CTRL_REGLD_VAL_SHIFT (23)
#define ODN_PDP_REGLD_CTRL_REGLD_VAL_LENGTH (1)
#define ODN_PDP_REGLD_CTRL_REGLD_VAL_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_UPDCTRL_OFFSET (0x08E0)

/* PDP, UPDCTRL, UPDFIELD
*/
#define ODN_PDP_UPDCTRL_UPDFIELD_MASK (0x00000001)
#define ODN_PDP_UPDCTRL_UPDFIELD_LSBMASK (0x00000001)
#define ODN_PDP_UPDCTRL_UPDFIELD_SHIFT (0)
#define ODN_PDP_UPDCTRL_UPDFIELD_LENGTH (1)
#define ODN_PDP_UPDCTRL_UPDFIELD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_INTCTRL_OFFSET (0x08E4)

/* PDP, PVR_ODN_PDP_INTCTRL, HBLNK_LINE
*/
#define ODN_PDP_INTCTRL_HBLNK_LINE_MASK (0x00010000)
#define ODN_PDP_INTCTRL_HBLNK_LINE_LSBMASK (0x00000001)
#define ODN_PDP_INTCTRL_HBLNK_LINE_SHIFT (16)
#define ODN_PDP_INTCTRL_HBLNK_LINE_LENGTH (1)
#define ODN_PDP_INTCTRL_HBLNK_LINE_SIGNED_FIELD IMG_FALSE

/* PDP, PVR_ODN_PDP_INTCTRL, HBLNK_LINENO
*/
#define ODN_PDP_INTCTRL_HBLNK_LINENO_MASK (0x00001FFF)
#define ODN_PDP_INTCTRL_HBLNK_LINENO_LSBMASK (0x00001FFF)
#define ODN_PDP_INTCTRL_HBLNK_LINENO_SHIFT (0)
#define ODN_PDP_INTCTRL_HBLNK_LINENO_LENGTH (13)
#define ODN_PDP_INTCTRL_HBLNK_LINENO_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PDISETUP_OFFSET (0x0900)

/* PDP, PDISETUP, PDI_BLNKLVL
*/
#define ODN_PDP_PDISETUP_PDI_BLNKLVL_MASK (0x00000040)
#define ODN_PDP_PDISETUP_PDI_BLNKLVL_LSBMASK (0x00000001)
#define ODN_PDP_PDISETUP_PDI_BLNKLVL_SHIFT (6)
#define ODN_PDP_PDISETUP_PDI_BLNKLVL_LENGTH (1)
#define ODN_PDP_PDISETUP_PDI_BLNKLVL_SIGNED_FIELD IMG_FALSE

/* PDP, PDISETUP, PDI_BLNK
*/
#define ODN_PDP_PDISETUP_PDI_BLNK_MASK (0x00000020)
#define ODN_PDP_PDISETUP_PDI_BLNK_LSBMASK (0x00000001)
#define ODN_PDP_PDISETUP_PDI_BLNK_SHIFT (5)
#define ODN_PDP_PDISETUP_PDI_BLNK_LENGTH (1)
#define ODN_PDP_PDISETUP_PDI_BLNK_SIGNED_FIELD IMG_FALSE

/* PDP, PDISETUP, PDI_PWR
*/
#define ODN_PDP_PDISETUP_PDI_PWR_MASK (0x00000010)
#define ODN_PDP_PDISETUP_PDI_PWR_LSBMASK (0x00000001)
#define ODN_PDP_PDISETUP_PDI_PWR_SHIFT (4)
#define ODN_PDP_PDISETUP_PDI_PWR_LENGTH (1)
#define ODN_PDP_PDISETUP_PDI_PWR_SIGNED_FIELD IMG_FALSE

/* PDP, PDISETUP, PDI_EN
*/
#define ODN_PDP_PDISETUP_PDI_EN_MASK (0x00000008)
#define ODN_PDP_PDISETUP_PDI_EN_LSBMASK (0x00000001)
#define ODN_PDP_PDISETUP_PDI_EN_SHIFT (3)
#define ODN_PDP_PDISETUP_PDI_EN_LENGTH (1)
#define ODN_PDP_PDISETUP_PDI_EN_SIGNED_FIELD IMG_FALSE

/* PDP, PDISETUP, PDI_GDEN
*/
#define ODN_PDP_PDISETUP_PDI_GDEN_MASK (0x00000004)
#define ODN_PDP_PDISETUP_PDI_GDEN_LSBMASK (0x00000001)
#define ODN_PDP_PDISETUP_PDI_GDEN_SHIFT (2)
#define ODN_PDP_PDISETUP_PDI_GDEN_LENGTH (1)
#define ODN_PDP_PDISETUP_PDI_GDEN_SIGNED_FIELD IMG_FALSE

/* PDP, PDISETUP, PDI_NFEN
*/
#define ODN_PDP_PDISETUP_PDI_NFEN_MASK (0x00000002)
#define ODN_PDP_PDISETUP_PDI_NFEN_LSBMASK (0x00000001)
#define ODN_PDP_PDISETUP_PDI_NFEN_SHIFT (1)
#define ODN_PDP_PDISETUP_PDI_NFEN_LENGTH (1)
#define ODN_PDP_PDISETUP_PDI_NFEN_SIGNED_FIELD IMG_FALSE

/* PDP, PDISETUP, PDI_CR
*/
#define ODN_PDP_PDISETUP_PDI_CR_MASK (0x00000001)
#define ODN_PDP_PDISETUP_PDI_CR_LSBMASK (0x00000001)
#define ODN_PDP_PDISETUP_PDI_CR_SHIFT (0)
#define ODN_PDP_PDISETUP_PDI_CR_LENGTH (1)
#define ODN_PDP_PDISETUP_PDI_CR_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PDITIMING0_OFFSET (0x0904)

/* PDP, PDITIMING0, PDI_PWRSVGD
*/
#define ODN_PDP_PDITIMING0_PDI_PWRSVGD_MASK (0x0F000000)
#define ODN_PDP_PDITIMING0_PDI_PWRSVGD_LSBMASK (0x0000000F)
#define ODN_PDP_PDITIMING0_PDI_PWRSVGD_SHIFT (24)
#define ODN_PDP_PDITIMING0_PDI_PWRSVGD_LENGTH (4)
#define ODN_PDP_PDITIMING0_PDI_PWRSVGD_SIGNED_FIELD IMG_FALSE

/* PDP, PDITIMING0, PDI_LSDEL
*/
#define ODN_PDP_PDITIMING0_PDI_LSDEL_MASK (0x007F0000)
#define ODN_PDP_PDITIMING0_PDI_LSDEL_LSBMASK (0x0000007F)
#define ODN_PDP_PDITIMING0_PDI_LSDEL_SHIFT (16)
#define ODN_PDP_PDITIMING0_PDI_LSDEL_LENGTH (7)
#define ODN_PDP_PDITIMING0_PDI_LSDEL_SIGNED_FIELD IMG_FALSE

/* PDP, PDITIMING0, PDI_PWRSV2GD2
*/
#define ODN_PDP_PDITIMING0_PDI_PWRSV2GD2_MASK (0x000003FF)
#define ODN_PDP_PDITIMING0_PDI_PWRSV2GD2_LSBMASK (0x000003FF)
#define ODN_PDP_PDITIMING0_PDI_PWRSV2GD2_SHIFT (0)
#define ODN_PDP_PDITIMING0_PDI_PWRSV2GD2_LENGTH (10)
#define ODN_PDP_PDITIMING0_PDI_PWRSV2GD2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PDITIMING1_OFFSET (0x0908)

/* PDP, PDITIMING1, PDI_NLDEL
*/
#define ODN_PDP_PDITIMING1_PDI_NLDEL_MASK (0x000F0000)
#define ODN_PDP_PDITIMING1_PDI_NLDEL_LSBMASK (0x0000000F)
#define ODN_PDP_PDITIMING1_PDI_NLDEL_SHIFT (16)
#define ODN_PDP_PDITIMING1_PDI_NLDEL_LENGTH (4)
#define ODN_PDP_PDITIMING1_PDI_NLDEL_SIGNED_FIELD IMG_FALSE

/* PDP, PDITIMING1, PDI_ACBDEL
*/
#define ODN_PDP_PDITIMING1_PDI_ACBDEL_MASK (0x000003FF)
#define ODN_PDP_PDITIMING1_PDI_ACBDEL_LSBMASK (0x000003FF)
#define ODN_PDP_PDITIMING1_PDI_ACBDEL_SHIFT (0)
#define ODN_PDP_PDITIMING1_PDI_ACBDEL_LENGTH (10)
#define ODN_PDP_PDITIMING1_PDI_ACBDEL_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PDICOREID_OFFSET (0x090C)

/* PDP, PDICOREID, PDI_GROUP_ID
*/
#define ODN_PDP_PDICOREID_PDI_GROUP_ID_MASK (0xFF000000)
#define ODN_PDP_PDICOREID_PDI_GROUP_ID_LSBMASK (0x000000FF)
#define ODN_PDP_PDICOREID_PDI_GROUP_ID_SHIFT (24)
#define ODN_PDP_PDICOREID_PDI_GROUP_ID_LENGTH (8)
#define ODN_PDP_PDICOREID_PDI_GROUP_ID_SIGNED_FIELD IMG_FALSE

/* PDP, PDICOREID, PDI_CORE_ID
*/
#define ODN_PDP_PDICOREID_PDI_CORE_ID_MASK (0x00FF0000)
#define ODN_PDP_PDICOREID_PDI_CORE_ID_LSBMASK (0x000000FF)
#define ODN_PDP_PDICOREID_PDI_CORE_ID_SHIFT (16)
#define ODN_PDP_PDICOREID_PDI_CORE_ID_LENGTH (8)
#define ODN_PDP_PDICOREID_PDI_CORE_ID_SIGNED_FIELD IMG_FALSE

/* PDP, PDICOREID, PDI_CONFIG_ID
*/
#define ODN_PDP_PDICOREID_PDI_CONFIG_ID_MASK (0x0000FFFF)
#define ODN_PDP_PDICOREID_PDI_CONFIG_ID_LSBMASK (0x0000FFFF)
#define ODN_PDP_PDICOREID_PDI_CONFIG_ID_SHIFT (0)
#define ODN_PDP_PDICOREID_PDI_CONFIG_ID_LENGTH (16)
#define ODN_PDP_PDICOREID_PDI_CONFIG_ID_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_PDICOREREV_OFFSET (0x0910)

/* PDP, PDICOREREV, PDI_MAJOR_REV
*/
#define ODN_PDP_PDICOREREV_PDI_MAJOR_REV_MASK (0x00FF0000)
#define ODN_PDP_PDICOREREV_PDI_MAJOR_REV_LSBMASK (0x000000FF)
#define ODN_PDP_PDICOREREV_PDI_MAJOR_REV_SHIFT (16)
#define ODN_PDP_PDICOREREV_PDI_MAJOR_REV_LENGTH (8)
#define ODN_PDP_PDICOREREV_PDI_MAJOR_REV_SIGNED_FIELD IMG_FALSE

/* PDP, PDICOREREV, PDI_MINOR_REV
*/
#define ODN_PDP_PDICOREREV_PDI_MINOR_REV_MASK (0x0000FF00)
#define ODN_PDP_PDICOREREV_PDI_MINOR_REV_LSBMASK (0x000000FF)
#define ODN_PDP_PDICOREREV_PDI_MINOR_REV_SHIFT (8)
#define ODN_PDP_PDICOREREV_PDI_MINOR_REV_LENGTH (8)
#define ODN_PDP_PDICOREREV_PDI_MINOR_REV_SIGNED_FIELD IMG_FALSE

/* PDP, PDICOREREV, PDI_MAINT_REV
*/
#define ODN_PDP_PDICOREREV_PDI_MAINT_REV_MASK (0x000000FF)
#define ODN_PDP_PDICOREREV_PDI_MAINT_REV_LSBMASK (0x000000FF)
#define ODN_PDP_PDICOREREV_PDI_MAINT_REV_SHIFT (0)
#define ODN_PDP_PDICOREREV_PDI_MAINT_REV_LENGTH (8)
#define ODN_PDP_PDICOREREV_PDI_MAINT_REV_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX2_OFFSET (0x0920)

/* PDP, DITHERMATRIX2, DITHERMATRIX2X1Y1
*/
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y1_MASK (0x000000C0)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y1_LSBMASK (0x00000003)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y1_SHIFT (6)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y1_LENGTH (2)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX2, DITHERMATRIX2X0Y1
*/
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y1_MASK (0x00000030)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y1_LSBMASK (0x00000003)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y1_SHIFT (4)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y1_LENGTH (2)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX2, DITHERMATRIX2X1Y0
*/
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y0_MASK (0x0000000C)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y0_LSBMASK (0x00000003)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y0_SHIFT (2)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y0_LENGTH (2)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X1Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX2, DITHERMATRIX2X0Y0
*/
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y0_MASK (0x00000003)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y0_LSBMASK (0x00000003)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y0_SHIFT (0)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y0_LENGTH (2)
#define ODN_PDP_DITHERMATRIX2_DITHERMATRIX2X0Y0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX4_0_OFFSET (0x0924)

/* PDP, DITHERMATRIX4_0, DITHERMATRIX4X3Y1
*/
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y1_MASK (0xF0000000)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y1_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y1_SHIFT (28)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y1_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_0, DITHERMATRIX4X2Y1
*/
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y1_MASK (0x0F000000)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y1_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y1_SHIFT (24)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y1_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_0, DITHERMATRIX4X1Y1
*/
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y1_MASK (0x00F00000)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y1_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y1_SHIFT (20)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y1_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_0, DITHERMATRIX4X0Y1
*/
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y1_MASK (0x000F0000)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y1_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y1_SHIFT (16)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y1_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_0, DITHERMATRIX4X3Y0
*/
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y0_MASK (0x0000F000)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y0_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y0_SHIFT (12)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y0_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X3Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_0, DITHERMATRIX4X2Y0
*/
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y0_MASK (0x00000F00)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y0_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y0_SHIFT (8)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y0_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X2Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_0, DITHERMATRIX4X1Y0
*/
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y0_MASK (0x000000F0)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y0_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y0_SHIFT (4)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y0_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X1Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_0, DITHERMATRIX4X0Y0
*/
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y0_MASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y0_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y0_SHIFT (0)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y0_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_0_DITHERMATRIX4X0Y0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX4_1_OFFSET (0x0928)

/* PDP, DITHERMATRIX4_1, DITHERMATRIX4X3Y3
*/
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y3_MASK (0xF0000000)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y3_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y3_SHIFT (28)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y3_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_1, DITHERMATRIX4X2Y3
*/
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y3_MASK (0x0F000000)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y3_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y3_SHIFT (24)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y3_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_1, DITHERMATRIX4X1Y3
*/
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y3_MASK (0x00F00000)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y3_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y3_SHIFT (20)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y3_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_1, DITHERMATRIX4X0Y3
*/
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y3_MASK (0x000F0000)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y3_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y3_SHIFT (16)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y3_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_1, DITHERMATRIX4X3Y2
*/
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y2_MASK (0x0000F000)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y2_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y2_SHIFT (12)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y2_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X3Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_1, DITHERMATRIX4X2Y2
*/
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y2_MASK (0x00000F00)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y2_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y2_SHIFT (8)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y2_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X2Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_1, DITHERMATRIX4X1Y2
*/
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y2_MASK (0x000000F0)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y2_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y2_SHIFT (4)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y2_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X1Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX4_1, DITHERMATRIX4X0Y2
*/
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y2_MASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y2_LSBMASK (0x0000000F)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y2_SHIFT (0)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y2_LENGTH (4)
#define ODN_PDP_DITHERMATRIX4_1_DITHERMATRIX4X0Y2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_0_OFFSET (0x092C)

/* PDP, DITHERMATRIX8_0, DITHERMATRIX8X4Y0
*/
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X4Y0_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X4Y0_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X4Y0_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X4Y0_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X4Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_0, DITHERMATRIX8X3Y0
*/
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X3Y0_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X3Y0_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X3Y0_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X3Y0_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X3Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_0, DITHERMATRIX8X2Y0
*/
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X2Y0_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X2Y0_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X2Y0_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X2Y0_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X2Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_0, DITHERMATRIX8X1Y0
*/
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X1Y0_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X1Y0_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X1Y0_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X1Y0_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X1Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_0, DITHERMATRIX8X0Y0
*/
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X0Y0_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X0Y0_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X0Y0_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X0Y0_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_0_DITHERMATRIX8X0Y0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_1_OFFSET (0x0930)

/* PDP, DITHERMATRIX8_1, DITHERMATRIX8X1Y1
*/
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X1Y1_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X1Y1_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X1Y1_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X1Y1_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X1Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_1, DITHERMATRIX8X0Y1
*/
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X0Y1_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X0Y1_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X0Y1_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X0Y1_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X0Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_1, DITHERMATRIX8X7Y0
*/
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X7Y0_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X7Y0_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X7Y0_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X7Y0_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X7Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_1, DITHERMATRIX8X6Y0
*/
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X6Y0_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X6Y0_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X6Y0_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X6Y0_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X6Y0_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_1, DITHERMATRIX8X5Y0
*/
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X5Y0_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X5Y0_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X5Y0_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X5Y0_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_1_DITHERMATRIX8X5Y0_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_2_OFFSET (0x0934)

/* PDP, DITHERMATRIX8_2, DITHERMATRIX8X6Y1
*/
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X6Y1_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X6Y1_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X6Y1_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X6Y1_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X6Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_2, DITHERMATRIX8X5Y1
*/
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X5Y1_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X5Y1_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X5Y1_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X5Y1_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X5Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_2, DITHERMATRIX8X4Y1
*/
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X4Y1_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X4Y1_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X4Y1_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X4Y1_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X4Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_2, DITHERMATRIX8X3Y1
*/
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X3Y1_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X3Y1_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X3Y1_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X3Y1_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X3Y1_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_2, DITHERMATRIX8X2Y1
*/
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X2Y1_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X2Y1_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X2Y1_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X2Y1_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_2_DITHERMATRIX8X2Y1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_3_OFFSET (0x0938)

/* PDP, DITHERMATRIX8_3, DITHERMATRIX8X3Y2
*/
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X3Y2_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X3Y2_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X3Y2_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X3Y2_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X3Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_3, DITHERMATRIX8X2Y2
*/
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X2Y2_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X2Y2_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X2Y2_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X2Y2_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X2Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_3, DITHERMATRIX8X1Y2
*/
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X1Y2_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X1Y2_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X1Y2_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X1Y2_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X1Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_3, DITHERMATRIX8X0Y2
*/
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X0Y2_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X0Y2_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X0Y2_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X0Y2_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X0Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_3, DITHERMATRIX8X7Y1
*/
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X7Y1_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X7Y1_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X7Y1_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X7Y1_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_3_DITHERMATRIX8X7Y1_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_4_OFFSET (0x093C)

/* PDP, DITHERMATRIX8_4, DITHERMATRIX8X0Y3
*/
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X0Y3_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X0Y3_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X0Y3_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X0Y3_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X0Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_4, DITHERMATRIX8X7Y2
*/
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X7Y2_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X7Y2_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X7Y2_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X7Y2_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X7Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_4, DITHERMATRIX8X6Y2
*/
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X6Y2_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X6Y2_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X6Y2_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X6Y2_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X6Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_4, DITHERMATRIX8X5Y2
*/
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X5Y2_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X5Y2_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X5Y2_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X5Y2_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X5Y2_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_4, DITHERMATRIX8X4Y2
*/
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X4Y2_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X4Y2_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X4Y2_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X4Y2_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_4_DITHERMATRIX8X4Y2_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_5_OFFSET (0x0940)

/* PDP, DITHERMATRIX8_5, DITHERMATRIX8X5Y3
*/
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X5Y3_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X5Y3_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X5Y3_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X5Y3_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X5Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_5, DITHERMATRIX8X4Y3
*/
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X4Y3_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X4Y3_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X4Y3_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X4Y3_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X4Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_5, DITHERMATRIX8X3Y3
*/
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X3Y3_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X3Y3_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X3Y3_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X3Y3_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X3Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_5, DITHERMATRIX8X2Y3
*/
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X2Y3_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X2Y3_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X2Y3_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X2Y3_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X2Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_5, DITHERMATRIX8X1Y3
*/
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X1Y3_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X1Y3_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X1Y3_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X1Y3_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_5_DITHERMATRIX8X1Y3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_6_OFFSET (0x0944)

/* PDP, DITHERMATRIX8_6, DITHERMATRIX8X2Y4
*/
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X2Y4_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X2Y4_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X2Y4_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X2Y4_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X2Y4_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_6, DITHERMATRIX8X1Y4
*/
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X1Y4_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X1Y4_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X1Y4_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X1Y4_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X1Y4_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_6, DITHERMATRIX8X0Y4
*/
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X0Y4_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X0Y4_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X0Y4_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X0Y4_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X0Y4_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_6, DITHERMATRIX8X7Y3
*/
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X7Y3_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X7Y3_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X7Y3_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X7Y3_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X7Y3_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_6, DITHERMATRIX8X6Y3
*/
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X6Y3_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X6Y3_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X6Y3_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X6Y3_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_6_DITHERMATRIX8X6Y3_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_7_OFFSET (0x0948)

/* PDP, DITHERMATRIX8_7, DITHERMATRIX8X7Y4
*/
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X7Y4_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X7Y4_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X7Y4_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X7Y4_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X7Y4_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_7, DITHERMATRIX8X6Y4
*/
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X6Y4_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X6Y4_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X6Y4_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X6Y4_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X6Y4_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_7, DITHERMATRIX8X5Y4
*/
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X5Y4_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X5Y4_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X5Y4_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X5Y4_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X5Y4_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_7, DITHERMATRIX8X4Y4
*/
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X4Y4_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X4Y4_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X4Y4_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X4Y4_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X4Y4_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_7, DITHERMATRIX8X3Y4
*/
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X3Y4_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X3Y4_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X3Y4_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X3Y4_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_7_DITHERMATRIX8X3Y4_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_8_OFFSET (0x094C)

/* PDP, DITHERMATRIX8_8, DITHERMATRIX8X4Y5
*/
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X4Y5_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X4Y5_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X4Y5_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X4Y5_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X4Y5_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_8, DITHERMATRIX8X3Y5
*/
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X3Y5_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X3Y5_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X3Y5_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X3Y5_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X3Y5_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_8, DITHERMATRIX8X2Y5
*/
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X2Y5_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X2Y5_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X2Y5_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X2Y5_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X2Y5_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_8, DITHERMATRIX8X1Y5
*/
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X1Y5_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X1Y5_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X1Y5_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X1Y5_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X1Y5_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_8, DITHERMATRIX8X0Y5
*/
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X0Y5_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X0Y5_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X0Y5_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X0Y5_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_8_DITHERMATRIX8X0Y5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_9_OFFSET (0x0950)

/* PDP, DITHERMATRIX8_9, DITHERMATRIX8X1Y6
*/
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X1Y6_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X1Y6_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X1Y6_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X1Y6_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X1Y6_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_9, DITHERMATRIX8X0Y6
*/
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X0Y6_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X0Y6_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X0Y6_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X0Y6_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X0Y6_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_9, DITHERMATRIX8X7Y5
*/
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X7Y5_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X7Y5_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X7Y5_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X7Y5_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X7Y5_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_9, DITHERMATRIX8X6Y5
*/
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X6Y5_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X6Y5_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X6Y5_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X6Y5_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X6Y5_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_9, DITHERMATRIX8X5Y5
*/
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X5Y5_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X5Y5_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X5Y5_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X5Y5_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_9_DITHERMATRIX8X5Y5_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_10_OFFSET (0x0954)

/* PDP, DITHERMATRIX8_10, DITHERMATRIX8X6Y6
*/
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X6Y6_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X6Y6_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X6Y6_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X6Y6_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X6Y6_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_10, DITHERMATRIX8X5Y6
*/
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X5Y6_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X5Y6_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X5Y6_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X5Y6_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X5Y6_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_10, DITHERMATRIX8X4Y6
*/
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X4Y6_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X4Y6_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X4Y6_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X4Y6_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X4Y6_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_10, DITHERMATRIX8X3Y6
*/
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X3Y6_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X3Y6_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X3Y6_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X3Y6_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X3Y6_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_10, DITHERMATRIX8X2Y6
*/
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X2Y6_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X2Y6_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X2Y6_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X2Y6_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_10_DITHERMATRIX8X2Y6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_11_OFFSET (0x0958)

/* PDP, DITHERMATRIX8_11, DITHERMATRIX8X3Y7
*/
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X3Y7_MASK (0x3F000000)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X3Y7_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X3Y7_SHIFT (24)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X3Y7_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X3Y7_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_11, DITHERMATRIX8X2Y7
*/
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X2Y7_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X2Y7_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X2Y7_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X2Y7_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X2Y7_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_11, DITHERMATRIX8X1Y7
*/
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X1Y7_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X1Y7_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X1Y7_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X1Y7_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X1Y7_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_11, DITHERMATRIX8X0Y7
*/
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X0Y7_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X0Y7_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X0Y7_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X0Y7_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X0Y7_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_11, DITHERMATRIX8X7Y6
*/
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X7Y6_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X7Y6_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X7Y6_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X7Y6_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_11_DITHERMATRIX8X7Y6_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_DITHERMATRIX8_12_OFFSET (0x095C)

/* PDP, DITHERMATRIX8_12, DITHERMATRIX8X7Y7
*/
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X7Y7_MASK (0x00FC0000)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X7Y7_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X7Y7_SHIFT (18)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X7Y7_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X7Y7_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_12, DITHERMATRIX8X6Y7
*/
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X6Y7_MASK (0x0003F000)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X6Y7_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X6Y7_SHIFT (12)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X6Y7_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X6Y7_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_12, DITHERMATRIX8X5Y7
*/
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X5Y7_MASK (0x00000FC0)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X5Y7_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X5Y7_SHIFT (6)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X5Y7_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X5Y7_SIGNED_FIELD IMG_FALSE

/* PDP, DITHERMATRIX8_12, DITHERMATRIX8X4Y7
*/
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X4Y7_MASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X4Y7_LSBMASK (0x0000003F)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X4Y7_SHIFT (0)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X4Y7_LENGTH (6)
#define ODN_PDP_DITHERMATRIX8_12_DITHERMATRIX8X4Y7_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1_MEMCTRL_OFFSET (0x0960)

/* PDP, GRPH1_MEMCTRL, GRPH1_LOCAL_GLOBAL_MEMCTRL
*/
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_LOCAL_GLOBAL_MEMCTRL_MASK (0x80000000)
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_LOCAL_GLOBAL_MEMCTRL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_LOCAL_GLOBAL_MEMCTRL_SHIFT (31)
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_LOCAL_GLOBAL_MEMCTRL_LENGTH (1)
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_LOCAL_GLOBAL_MEMCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1_MEMCTRL, GRPH1_BURSTLEN
*/
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_BURSTLEN_MASK (0x000000FF)
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_BURSTLEN_LSBMASK (0x000000FF)
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_BURSTLEN_SHIFT (0)
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_BURSTLEN_LENGTH (8)
#define ODN_PDP_GRPH1_MEMCTRL_GRPH1_BURSTLEN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1_MEM_THRESH_OFFSET (0x0964)

/* PDP, GRPH1_MEM_THRESH, GRPH1_UVTHRESHOLD
*/
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_UVTHRESHOLD_MASK (0xFF000000)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_UVTHRESHOLD_LSBMASK (0x000000FF)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_UVTHRESHOLD_SHIFT (24)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_UVTHRESHOLD_LENGTH (8)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_UVTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1_MEM_THRESH, GRPH1_YTHRESHOLD
*/
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_YTHRESHOLD_MASK (0x001FF000)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_YTHRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_YTHRESHOLD_SHIFT (12)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_YTHRESHOLD_LENGTH (9)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_YTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1_MEM_THRESH, GRPH1_THRESHOLD
*/
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_THRESHOLD_MASK (0x000001FF)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_THRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_THRESHOLD_SHIFT (0)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_THRESHOLD_LENGTH (9)
#define ODN_PDP_GRPH1_MEM_THRESH_GRPH1_THRESHOLD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2_MEMCTRL_OFFSET (0x0968)

/* PDP, GRPH2_MEMCTRL, GRPH2_LOCAL_GLOBAL_MEMCTRL
*/
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_LOCAL_GLOBAL_MEMCTRL_MASK (0x80000000)
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_LOCAL_GLOBAL_MEMCTRL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_LOCAL_GLOBAL_MEMCTRL_SHIFT (31)
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_LOCAL_GLOBAL_MEMCTRL_LENGTH (1)
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_LOCAL_GLOBAL_MEMCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2_MEMCTRL, GRPH2_BURSTLEN
*/
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_BURSTLEN_MASK (0x000000FF)
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_BURSTLEN_LSBMASK (0x000000FF)
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_BURSTLEN_SHIFT (0)
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_BURSTLEN_LENGTH (8)
#define ODN_PDP_GRPH2_MEMCTRL_GRPH2_BURSTLEN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH2_MEM_THRESH_OFFSET (0x096C)

/* PDP, GRPH2_MEM_THRESH, GRPH2_UVTHRESHOLD
*/
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_UVTHRESHOLD_MASK (0xFF000000)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_UVTHRESHOLD_LSBMASK (0x000000FF)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_UVTHRESHOLD_SHIFT (24)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_UVTHRESHOLD_LENGTH (8)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_UVTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2_MEM_THRESH, GRPH2_YTHRESHOLD
*/
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_YTHRESHOLD_MASK (0x001FF000)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_YTHRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_YTHRESHOLD_SHIFT (12)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_YTHRESHOLD_LENGTH (9)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_YTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2_MEM_THRESH, GRPH2_THRESHOLD
*/
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_THRESHOLD_MASK (0x000001FF)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_THRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_THRESHOLD_SHIFT (0)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_THRESHOLD_LENGTH (9)
#define ODN_PDP_GRPH2_MEM_THRESH_GRPH2_THRESHOLD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3_MEMCTRL_OFFSET (0x0970)

/* PDP, GRPH3_MEMCTRL, GRPH3_LOCAL_GLOBAL_MEMCTRL
*/
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_LOCAL_GLOBAL_MEMCTRL_MASK (0x80000000)
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_LOCAL_GLOBAL_MEMCTRL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_LOCAL_GLOBAL_MEMCTRL_SHIFT (31)
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_LOCAL_GLOBAL_MEMCTRL_LENGTH (1)
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_LOCAL_GLOBAL_MEMCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3_MEMCTRL, GRPH3_BURSTLEN
*/
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_BURSTLEN_MASK (0x000000FF)
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_BURSTLEN_LSBMASK (0x000000FF)
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_BURSTLEN_SHIFT (0)
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_BURSTLEN_LENGTH (8)
#define ODN_PDP_GRPH3_MEMCTRL_GRPH3_BURSTLEN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH3_MEM_THRESH_OFFSET (0x0974)

/* PDP, GRPH3_MEM_THRESH, GRPH3_UVTHRESHOLD
*/
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_UVTHRESHOLD_MASK (0xFF000000)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_UVTHRESHOLD_LSBMASK (0x000000FF)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_UVTHRESHOLD_SHIFT (24)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_UVTHRESHOLD_LENGTH (8)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_UVTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3_MEM_THRESH, GRPH3_YTHRESHOLD
*/
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_YTHRESHOLD_MASK (0x001FF000)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_YTHRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_YTHRESHOLD_SHIFT (12)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_YTHRESHOLD_LENGTH (9)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_YTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3_MEM_THRESH, GRPH3_THRESHOLD
*/
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_THRESHOLD_MASK (0x000001FF)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_THRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_THRESHOLD_SHIFT (0)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_THRESHOLD_LENGTH (9)
#define ODN_PDP_GRPH3_MEM_THRESH_GRPH3_THRESHOLD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4_MEMCTRL_OFFSET (0x0978)

/* PDP, GRPH4_MEMCTRL, GRPH4_LOCAL_GLOBAL_MEMCTRL
*/
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_LOCAL_GLOBAL_MEMCTRL_MASK (0x80000000)
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_LOCAL_GLOBAL_MEMCTRL_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_LOCAL_GLOBAL_MEMCTRL_SHIFT (31)
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_LOCAL_GLOBAL_MEMCTRL_LENGTH (1)
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_LOCAL_GLOBAL_MEMCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4_MEMCTRL, GRPH4_BURSTLEN
*/
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_BURSTLEN_MASK (0x000000FF)
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_BURSTLEN_LSBMASK (0x000000FF)
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_BURSTLEN_SHIFT (0)
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_BURSTLEN_LENGTH (8)
#define ODN_PDP_GRPH4_MEMCTRL_GRPH4_BURSTLEN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH4_MEM_THRESH_OFFSET (0x097C)

/* PDP, GRPH4_MEM_THRESH, GRPH4_UVTHRESHOLD
*/
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_UVTHRESHOLD_MASK (0xFF000000)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_UVTHRESHOLD_LSBMASK (0x000000FF)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_UVTHRESHOLD_SHIFT (24)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_UVTHRESHOLD_LENGTH (8)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_UVTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4_MEM_THRESH, GRPH4_YTHRESHOLD
*/
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_YTHRESHOLD_MASK (0x001FF000)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_YTHRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_YTHRESHOLD_SHIFT (12)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_YTHRESHOLD_LENGTH (9)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_YTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4_MEM_THRESH, GRPH4_THRESHOLD
*/
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_THRESHOLD_MASK (0x000001FF)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_THRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_THRESHOLD_SHIFT (0)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_THRESHOLD_LENGTH (9)
#define ODN_PDP_GRPH4_MEM_THRESH_GRPH4_THRESHOLD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1_MEMCTRL_OFFSET (0x0980)

/* PDP, VID1_MEMCTRL, VID1_LOCAL_GLOBAL_MEMCTRL
*/
#define ODN_PDP_VID1_MEMCTRL_VID1_LOCAL_GLOBAL_MEMCTRL_MASK (0x80000000)
#define ODN_PDP_VID1_MEMCTRL_VID1_LOCAL_GLOBAL_MEMCTRL_LSBMASK (0x00000001)
#define ODN_PDP_VID1_MEMCTRL_VID1_LOCAL_GLOBAL_MEMCTRL_SHIFT (31)
#define ODN_PDP_VID1_MEMCTRL_VID1_LOCAL_GLOBAL_MEMCTRL_LENGTH (1)
#define ODN_PDP_VID1_MEMCTRL_VID1_LOCAL_GLOBAL_MEMCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, VID1_MEMCTRL, VID1_BURSTLEN
*/
#define ODN_PDP_VID1_MEMCTRL_VID1_BURSTLEN_MASK (0x000000FF)
#define ODN_PDP_VID1_MEMCTRL_VID1_BURSTLEN_LSBMASK (0x000000FF)
#define ODN_PDP_VID1_MEMCTRL_VID1_BURSTLEN_SHIFT (0)
#define ODN_PDP_VID1_MEMCTRL_VID1_BURSTLEN_LENGTH (8)
#define ODN_PDP_VID1_MEMCTRL_VID1_BURSTLEN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID1_MEM_THRESH_OFFSET (0x0984)

/* PDP, VID1_MEM_THRESH, VID1_UVTHRESHOLD
*/
#define ODN_PDP_VID1_MEM_THRESH_VID1_UVTHRESHOLD_MASK (0xFF000000)
#define ODN_PDP_VID1_MEM_THRESH_VID1_UVTHRESHOLD_LSBMASK (0x000000FF)
#define ODN_PDP_VID1_MEM_THRESH_VID1_UVTHRESHOLD_SHIFT (24)
#define ODN_PDP_VID1_MEM_THRESH_VID1_UVTHRESHOLD_LENGTH (8)
#define ODN_PDP_VID1_MEM_THRESH_VID1_UVTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, VID1_MEM_THRESH, VID1_YTHRESHOLD
*/
#define ODN_PDP_VID1_MEM_THRESH_VID1_YTHRESHOLD_MASK (0x001FF000)
#define ODN_PDP_VID1_MEM_THRESH_VID1_YTHRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_VID1_MEM_THRESH_VID1_YTHRESHOLD_SHIFT (12)
#define ODN_PDP_VID1_MEM_THRESH_VID1_YTHRESHOLD_LENGTH (9)
#define ODN_PDP_VID1_MEM_THRESH_VID1_YTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, VID1_MEM_THRESH, VID1_THRESHOLD
*/
#define ODN_PDP_VID1_MEM_THRESH_VID1_THRESHOLD_MASK (0x000001FF)
#define ODN_PDP_VID1_MEM_THRESH_VID1_THRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_VID1_MEM_THRESH_VID1_THRESHOLD_SHIFT (0)
#define ODN_PDP_VID1_MEM_THRESH_VID1_THRESHOLD_LENGTH (9)
#define ODN_PDP_VID1_MEM_THRESH_VID1_THRESHOLD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2_MEMCTRL_OFFSET (0x0988)

/* PDP, VID2_MEMCTRL, VID2_LOCAL_GLOBAL_MEMCTRL
*/
#define ODN_PDP_VID2_MEMCTRL_VID2_LOCAL_GLOBAL_MEMCTRL_MASK (0x80000000)
#define ODN_PDP_VID2_MEMCTRL_VID2_LOCAL_GLOBAL_MEMCTRL_LSBMASK (0x00000001)
#define ODN_PDP_VID2_MEMCTRL_VID2_LOCAL_GLOBAL_MEMCTRL_SHIFT (31)
#define ODN_PDP_VID2_MEMCTRL_VID2_LOCAL_GLOBAL_MEMCTRL_LENGTH (1)
#define ODN_PDP_VID2_MEMCTRL_VID2_LOCAL_GLOBAL_MEMCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, VID2_MEMCTRL, VID2_BURSTLEN
*/
#define ODN_PDP_VID2_MEMCTRL_VID2_BURSTLEN_MASK (0x000000FF)
#define ODN_PDP_VID2_MEMCTRL_VID2_BURSTLEN_LSBMASK (0x000000FF)
#define ODN_PDP_VID2_MEMCTRL_VID2_BURSTLEN_SHIFT (0)
#define ODN_PDP_VID2_MEMCTRL_VID2_BURSTLEN_LENGTH (8)
#define ODN_PDP_VID2_MEMCTRL_VID2_BURSTLEN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID2_MEM_THRESH_OFFSET (0x098C)

/* PDP, VID2_MEM_THRESH, VID2_UVTHRESHOLD
*/
#define ODN_PDP_VID2_MEM_THRESH_VID2_UVTHRESHOLD_MASK (0xFF000000)
#define ODN_PDP_VID2_MEM_THRESH_VID2_UVTHRESHOLD_LSBMASK (0x000000FF)
#define ODN_PDP_VID2_MEM_THRESH_VID2_UVTHRESHOLD_SHIFT (24)
#define ODN_PDP_VID2_MEM_THRESH_VID2_UVTHRESHOLD_LENGTH (8)
#define ODN_PDP_VID2_MEM_THRESH_VID2_UVTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, VID2_MEM_THRESH, VID2_YTHRESHOLD
*/
#define ODN_PDP_VID2_MEM_THRESH_VID2_YTHRESHOLD_MASK (0x001FF000)
#define ODN_PDP_VID2_MEM_THRESH_VID2_YTHRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_VID2_MEM_THRESH_VID2_YTHRESHOLD_SHIFT (12)
#define ODN_PDP_VID2_MEM_THRESH_VID2_YTHRESHOLD_LENGTH (9)
#define ODN_PDP_VID2_MEM_THRESH_VID2_YTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, VID2_MEM_THRESH, VID2_THRESHOLD
*/
#define ODN_PDP_VID2_MEM_THRESH_VID2_THRESHOLD_MASK (0x000001FF)
#define ODN_PDP_VID2_MEM_THRESH_VID2_THRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_VID2_MEM_THRESH_VID2_THRESHOLD_SHIFT (0)
#define ODN_PDP_VID2_MEM_THRESH_VID2_THRESHOLD_LENGTH (9)
#define ODN_PDP_VID2_MEM_THRESH_VID2_THRESHOLD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3_MEMCTRL_OFFSET (0x0990)

/* PDP, VID3_MEMCTRL, VID3_LOCAL_GLOBAL_MEMCTRL
*/
#define ODN_PDP_VID3_MEMCTRL_VID3_LOCAL_GLOBAL_MEMCTRL_MASK (0x80000000)
#define ODN_PDP_VID3_MEMCTRL_VID3_LOCAL_GLOBAL_MEMCTRL_LSBMASK (0x00000001)
#define ODN_PDP_VID3_MEMCTRL_VID3_LOCAL_GLOBAL_MEMCTRL_SHIFT (31)
#define ODN_PDP_VID3_MEMCTRL_VID3_LOCAL_GLOBAL_MEMCTRL_LENGTH (1)
#define ODN_PDP_VID3_MEMCTRL_VID3_LOCAL_GLOBAL_MEMCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, VID3_MEMCTRL, VID3_BURSTLEN
*/
#define ODN_PDP_VID3_MEMCTRL_VID3_BURSTLEN_MASK (0x000000FF)
#define ODN_PDP_VID3_MEMCTRL_VID3_BURSTLEN_LSBMASK (0x000000FF)
#define ODN_PDP_VID3_MEMCTRL_VID3_BURSTLEN_SHIFT (0)
#define ODN_PDP_VID3_MEMCTRL_VID3_BURSTLEN_LENGTH (8)
#define ODN_PDP_VID3_MEMCTRL_VID3_BURSTLEN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID3_MEM_THRESH_OFFSET (0x0994)

/* PDP, VID3_MEM_THRESH, VID3_UVTHRESHOLD
*/
#define ODN_PDP_VID3_MEM_THRESH_VID3_UVTHRESHOLD_MASK (0xFF000000)
#define ODN_PDP_VID3_MEM_THRESH_VID3_UVTHRESHOLD_LSBMASK (0x000000FF)
#define ODN_PDP_VID3_MEM_THRESH_VID3_UVTHRESHOLD_SHIFT (24)
#define ODN_PDP_VID3_MEM_THRESH_VID3_UVTHRESHOLD_LENGTH (8)
#define ODN_PDP_VID3_MEM_THRESH_VID3_UVTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, VID3_MEM_THRESH, VID3_YTHRESHOLD
*/
#define ODN_PDP_VID3_MEM_THRESH_VID3_YTHRESHOLD_MASK (0x001FF000)
#define ODN_PDP_VID3_MEM_THRESH_VID3_YTHRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_VID3_MEM_THRESH_VID3_YTHRESHOLD_SHIFT (12)
#define ODN_PDP_VID3_MEM_THRESH_VID3_YTHRESHOLD_LENGTH (9)
#define ODN_PDP_VID3_MEM_THRESH_VID3_YTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, VID3_MEM_THRESH, VID3_THRESHOLD
*/
#define ODN_PDP_VID3_MEM_THRESH_VID3_THRESHOLD_MASK (0x000001FF)
#define ODN_PDP_VID3_MEM_THRESH_VID3_THRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_VID3_MEM_THRESH_VID3_THRESHOLD_SHIFT (0)
#define ODN_PDP_VID3_MEM_THRESH_VID3_THRESHOLD_LENGTH (9)
#define ODN_PDP_VID3_MEM_THRESH_VID3_THRESHOLD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4_MEMCTRL_OFFSET (0x0998)

/* PDP, VID4_MEMCTRL, VID4_LOCAL_GLOBAL_MEMCTRL
*/
#define ODN_PDP_VID4_MEMCTRL_VID4_LOCAL_GLOBAL_MEMCTRL_MASK (0x80000000)
#define ODN_PDP_VID4_MEMCTRL_VID4_LOCAL_GLOBAL_MEMCTRL_LSBMASK (0x00000001)
#define ODN_PDP_VID4_MEMCTRL_VID4_LOCAL_GLOBAL_MEMCTRL_SHIFT (31)
#define ODN_PDP_VID4_MEMCTRL_VID4_LOCAL_GLOBAL_MEMCTRL_LENGTH (1)
#define ODN_PDP_VID4_MEMCTRL_VID4_LOCAL_GLOBAL_MEMCTRL_SIGNED_FIELD IMG_FALSE

/* PDP, VID4_MEMCTRL, VID4_BURSTLEN
*/
#define ODN_PDP_VID4_MEMCTRL_VID4_BURSTLEN_MASK (0x000000FF)
#define ODN_PDP_VID4_MEMCTRL_VID4_BURSTLEN_LSBMASK (0x000000FF)
#define ODN_PDP_VID4_MEMCTRL_VID4_BURSTLEN_SHIFT (0)
#define ODN_PDP_VID4_MEMCTRL_VID4_BURSTLEN_LENGTH (8)
#define ODN_PDP_VID4_MEMCTRL_VID4_BURSTLEN_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_VID4_MEM_THRESH_OFFSET (0x099C)

/* PDP, VID4_MEM_THRESH, VID4_UVTHRESHOLD
*/
#define ODN_PDP_VID4_MEM_THRESH_VID4_UVTHRESHOLD_MASK (0xFF000000)
#define ODN_PDP_VID4_MEM_THRESH_VID4_UVTHRESHOLD_LSBMASK (0x000000FF)
#define ODN_PDP_VID4_MEM_THRESH_VID4_UVTHRESHOLD_SHIFT (24)
#define ODN_PDP_VID4_MEM_THRESH_VID4_UVTHRESHOLD_LENGTH (8)
#define ODN_PDP_VID4_MEM_THRESH_VID4_UVTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, VID4_MEM_THRESH, VID4_YTHRESHOLD
*/
#define ODN_PDP_VID4_MEM_THRESH_VID4_YTHRESHOLD_MASK (0x001FF000)
#define ODN_PDP_VID4_MEM_THRESH_VID4_YTHRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_VID4_MEM_THRESH_VID4_YTHRESHOLD_SHIFT (12)
#define ODN_PDP_VID4_MEM_THRESH_VID4_YTHRESHOLD_LENGTH (9)
#define ODN_PDP_VID4_MEM_THRESH_VID4_YTHRESHOLD_SIGNED_FIELD IMG_FALSE

/* PDP, VID4_MEM_THRESH, VID4_THRESHOLD
*/
#define ODN_PDP_VID4_MEM_THRESH_VID4_THRESHOLD_MASK (0x000001FF)
#define ODN_PDP_VID4_MEM_THRESH_VID4_THRESHOLD_LSBMASK (0x000001FF)
#define ODN_PDP_VID4_MEM_THRESH_VID4_THRESHOLD_SHIFT (0)
#define ODN_PDP_VID4_MEM_THRESH_VID4_THRESHOLD_LENGTH (9)
#define ODN_PDP_VID4_MEM_THRESH_VID4_THRESHOLD_SIGNED_FIELD IMG_FALSE

#define ODN_PDP_GRPH1_PANIC_THRESH_OFFSET (0x09A0)

/* PDP, GRPH1_PANIC_THRESH, GRPH1_ALERT_UV_ENABLE
*/
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_ENABLE_MASK (0x80000000)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_ENABLE_SHIFT (31)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_ENABLE_LENGTH (1)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1_PANIC_THRESH, GRPH1_ALERT_Y_ENABLE
*/
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_ENABLE_MASK (0x40000000)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_ENABLE_SHIFT (30)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_ENABLE_LENGTH (1)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH1_PANIC_THRESH, GRPH1_ALERT_UV_THRESHOLD_MAX
*/
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MAX_MASK \
	(0x3F800000)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MAX_LSBMASK \
	(0x0000007F)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MAX_SHIFT (23)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MAX_LENGTH (7)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH1_PANIC_THRESH, GRPH1_ALERT_UV_THRESHOLD_MIN
*/
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MIN_MASK \
	(0x007F0000)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MIN_LSBMASK \
	(0x0000007F)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MIN_SHIFT (16)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MIN_LENGTH (7)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_UV_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH1_PANIC_THRESH, GRPH1_ALERT_Y_THRESHOLD_MAX
*/
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MAX_MASK (0x0000FF00)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MAX_LSBMASK \
	(0x000000FF)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MAX_SHIFT (8)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MAX_LENGTH (8)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH1_PANIC_THRESH, GRPH1_ALERT_Y_THRESHOLD_MIN
*/
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MIN_MASK (0x000000FF)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MIN_LSBMASK \
	(0x000000FF)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MIN_SHIFT (0)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MIN_LENGTH (8)
#define ODN_PDP_GRPH1_PANIC_THRESH_GRPH1_ALERT_Y_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_GRPH2_PANIC_THRESH_OFFSET (0x09A4)

/* PDP, GRPH2_PANIC_THRESH, GRPH2_ALERT_UV_ENABLE
*/
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_ENABLE_MASK (0x80000000)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_ENABLE_SHIFT (31)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_ENABLE_LENGTH (1)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2_PANIC_THRESH, GRPH2_ALERT_Y_ENABLE
*/
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_ENABLE_MASK (0x40000000)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_ENABLE_SHIFT (30)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_ENABLE_LENGTH (1)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH2_PANIC_THRESH, GRPH2_ALERT_UV_THRESHOLD_MAX
*/
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MAX_MASK \
	(0x3F800000)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MAX_LSBMASK \
	(0x0000007F)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MAX_SHIFT (23)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MAX_LENGTH (7)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH2_PANIC_THRESH, GRPH2_ALERT_UV_THRESHOLD_MIN
*/
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MIN_MASK \
	(0x007F0000)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MIN_LSBMASK \
	(0x0000007F)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MIN_SHIFT (16)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MIN_LENGTH (7)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_UV_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH2_PANIC_THRESH, GRPH2_ALERT_Y_THRESHOLD_MAX
*/
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MAX_MASK (0x0000FF00)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MAX_LSBMASK \
	(0x000000FF)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MAX_SHIFT (8)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MAX_LENGTH (8)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH2_PANIC_THRESH, GRPH2_ALERT_Y_THRESHOLD_MIN
*/
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MIN_MASK (0x000000FF)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MIN_LSBMASK \
	(0x000000FF)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MIN_SHIFT (0)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MIN_LENGTH (8)
#define ODN_PDP_GRPH2_PANIC_THRESH_GRPH2_ALERT_Y_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_GRPH3_PANIC_THRESH_OFFSET (0x09A8)

/* PDP, GRPH3_PANIC_THRESH, GRPH3_ALERT_UV_ENABLE
*/
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_ENABLE_MASK (0x80000000)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_ENABLE_SHIFT (31)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_ENABLE_LENGTH (1)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3_PANIC_THRESH, GRPH3_ALERT_Y_ENABLE
*/
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_ENABLE_MASK (0x40000000)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_ENABLE_SHIFT (30)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_ENABLE_LENGTH (1)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH3_PANIC_THRESH, GRPH3_ALERT_UV_THRESHOLD_MAX
*/
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MAX_MASK \
	(0x3F800000)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MAX_LSBMASK \
	(0x0000007F)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MAX_SHIFT (23)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MAX_LENGTH (7)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH3_PANIC_THRESH, GRPH3_ALERT_UV_THRESHOLD_MIN
*/
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MIN_MASK \
	(0x007F0000)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MIN_LSBMASK \
	(0x0000007F)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MIN_SHIFT (16)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MIN_LENGTH (7)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_UV_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH3_PANIC_THRESH, GRPH3_ALERT_Y_THRESHOLD_MAX
*/
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MAX_MASK (0x0000FF00)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MAX_LSBMASK \
	(0x000000FF)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MAX_SHIFT (8)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MAX_LENGTH (8)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH3_PANIC_THRESH, GRPH3_ALERT_Y_THRESHOLD_MIN
*/
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MIN_MASK (0x000000FF)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MIN_LSBMASK \
	(0x000000FF)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MIN_SHIFT (0)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MIN_LENGTH (8)
#define ODN_PDP_GRPH3_PANIC_THRESH_GRPH3_ALERT_Y_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_GRPH4_PANIC_THRESH_OFFSET (0x09AC)

/* PDP, GRPH4_PANIC_THRESH, GRPH4_ALERT_UV_ENABLE
*/
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_ENABLE_MASK (0x80000000)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_ENABLE_SHIFT (31)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_ENABLE_LENGTH (1)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4_PANIC_THRESH, GRPH4_ALERT_Y_ENABLE
*/
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_ENABLE_MASK (0x40000000)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_ENABLE_SHIFT (30)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_ENABLE_LENGTH (1)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, GRPH4_PANIC_THRESH, GRPH4_ALERT_UV_THRESHOLD_MAX
*/
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MAX_MASK \
	(0x3F800000)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MAX_LSBMASK \
	(0x0000007F)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MAX_SHIFT (23)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MAX_LENGTH (7)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH4_PANIC_THRESH, GRPH4_ALERT_UV_THRESHOLD_MIN
*/
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MIN_MASK \
	(0x007F0000)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MIN_LSBMASK \
	(0x0000007F)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MIN_SHIFT (16)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MIN_LENGTH (7)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_UV_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH4_PANIC_THRESH, GRPH4_ALERT_Y_THRESHOLD_MAX
*/
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MAX_MASK (0x0000FF00)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MAX_LSBMASK \
	(0x000000FF)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MAX_SHIFT (8)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MAX_LENGTH (8)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, GRPH4_PANIC_THRESH, GRPH4_ALERT_Y_THRESHOLD_MIN
*/
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MIN_MASK (0x000000FF)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MIN_LSBMASK \
	(0x000000FF)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MIN_SHIFT (0)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MIN_LENGTH (8)
#define ODN_PDP_GRPH4_PANIC_THRESH_GRPH4_ALERT_Y_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_VID1_PANIC_THRESH_OFFSET (0x09B0)

/* PDP, VID1_PANIC_THRESH, VID1_ALERT_UV_ENABLE
*/
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_ENABLE_MASK (0x80000000)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_ENABLE_SHIFT (31)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_ENABLE_LENGTH (1)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, VID1_PANIC_THRESH, VID1_ALERT_Y_ENABLE
*/
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_ENABLE_MASK (0x40000000)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_ENABLE_SHIFT (30)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_ENABLE_LENGTH (1)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, VID1_PANIC_THRESH, VID1_ALERT_UV_THRESHOLD_MAX
*/
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MAX_MASK (0x3F800000)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MAX_LSBMASK \
	(0x0000007F)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MAX_SHIFT (23)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MAX_LENGTH (7)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID1_PANIC_THRESH, VID1_ALERT_UV_THRESHOLD_MIN
*/
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MIN_MASK (0x007F0000)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MIN_LSBMASK \
	(0x0000007F)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MIN_SHIFT (16)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MIN_LENGTH (7)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_UV_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID1_PANIC_THRESH, VID1_ALERT_Y_THRESHOLD_MAX
*/
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MAX_MASK (0x0000FF00)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MAX_LSBMASK \
	(0x000000FF)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MAX_SHIFT (8)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MAX_LENGTH (8)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID1_PANIC_THRESH, VID1_ALERT_Y_THRESHOLD_MIN
*/
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MIN_MASK (0x000000FF)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MIN_LSBMASK \
	(0x000000FF)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MIN_SHIFT (0)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MIN_LENGTH (8)
#define ODN_PDP_VID1_PANIC_THRESH_VID1_ALERT_Y_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_VID2_PANIC_THRESH_OFFSET (0x09B4)

/* PDP, VID2_PANIC_THRESH, VID2_ALERT_UV_ENABLE
*/
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_ENABLE_MASK (0x80000000)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_ENABLE_SHIFT (31)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_ENABLE_LENGTH (1)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, VID2_PANIC_THRESH, VID2_ALERT_Y_ENABLE
*/
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_ENABLE_MASK (0x40000000)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_ENABLE_SHIFT (30)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_ENABLE_LENGTH (1)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, VID2_PANIC_THRESH, VID2_ALERT_UV_THRESHOLD_MAX
*/
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MAX_MASK (0x3F800000)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MAX_LSBMASK \
	(0x0000007F)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MAX_SHIFT (23)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MAX_LENGTH (7)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID2_PANIC_THRESH, VID2_ALERT_UV_THRESHOLD_MIN
*/
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MIN_MASK (0x007F0000)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MIN_LSBMASK \
	(0x0000007F)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MIN_SHIFT (16)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MIN_LENGTH (7)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_UV_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID2_PANIC_THRESH, VID2_ALERT_Y_THRESHOLD_MAX
*/
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MAX_MASK (0x0000FF00)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MAX_LSBMASK \
	(0x000000FF)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MAX_SHIFT (8)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MAX_LENGTH (8)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID2_PANIC_THRESH, VID2_ALERT_Y_THRESHOLD_MIN
*/
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MIN_MASK (0x000000FF)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MIN_LSBMASK \
	(0x000000FF)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MIN_SHIFT (0)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MIN_LENGTH (8)
#define ODN_PDP_VID2_PANIC_THRESH_VID2_ALERT_Y_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_VID3_PANIC_THRESH_OFFSET (0x09B8)

/* PDP, VID3_PANIC_THRESH, VID3_ALERT_UV_ENABLE
*/
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_ENABLE_MASK (0x80000000)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_ENABLE_SHIFT (31)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_ENABLE_LENGTH (1)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, VID3_PANIC_THRESH, VID3_ALERT_Y_ENABLE
*/
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_ENABLE_MASK (0x40000000)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_ENABLE_SHIFT (30)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_ENABLE_LENGTH (1)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, VID3_PANIC_THRESH, VID3_ALERT_UV_THRESHOLD_MAX
*/
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MAX_MASK (0x3F800000)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MAX_LSBMASK \
	(0x0000007F)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MAX_SHIFT (23)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MAX_LENGTH (7)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID3_PANIC_THRESH, VID3_ALERT_UV_THRESHOLD_MIN
*/
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MIN_MASK (0x007F0000)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MIN_LSBMASK \
	(0x0000007F)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MIN_SHIFT (16)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MIN_LENGTH (7)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_UV_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID3_PANIC_THRESH, VID3_ALERT_Y_THRESHOLD_MAX
*/
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MAX_MASK (0x0000FF00)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MAX_LSBMASK \
	(0x000000FF)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MAX_SHIFT (8)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MAX_LENGTH (8)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID3_PANIC_THRESH, VID3_ALERT_Y_THRESHOLD_MIN
*/
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MIN_MASK (0x000000FF)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MIN_LSBMASK \
	(0x000000FF)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MIN_SHIFT (0)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MIN_LENGTH (8)
#define ODN_PDP_VID3_PANIC_THRESH_VID3_ALERT_Y_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_VID4_PANIC_THRESH_OFFSET (0x09BC)

/* PDP, VID4_PANIC_THRESH, VID4_ALERT_UV_ENABLE
*/
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_ENABLE_MASK (0x80000000)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_ENABLE_SHIFT (31)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_ENABLE_LENGTH (1)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, VID4_PANIC_THRESH, VID4_ALERT_Y_ENABLE
*/
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_ENABLE_MASK (0x40000000)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_ENABLE_LSBMASK (0x00000001)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_ENABLE_SHIFT (30)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_ENABLE_LENGTH (1)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_ENABLE_SIGNED_FIELD IMG_FALSE

/* PDP, VID4_PANIC_THRESH, VID4_ALERT_UV_THRESHOLD_MAX
*/
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MAX_MASK (0x3F800000)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MAX_LSBMASK \
	(0x0000007F)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MAX_SHIFT (23)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MAX_LENGTH (7)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID4_PANIC_THRESH, VID4_ALERT_UV_THRESHOLD_MIN
*/
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MIN_MASK (0x007F0000)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MIN_LSBMASK \
	(0x0000007F)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MIN_SHIFT (16)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MIN_LENGTH (7)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_UV_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID4_PANIC_THRESH, VID4_ALERT_Y_THRESHOLD_MAX
*/
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MAX_MASK (0x0000FF00)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MAX_LSBMASK \
	(0x000000FF)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MAX_SHIFT (8)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MAX_LENGTH (8)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MAX_SIGNED_FIELD \
	IMG_FALSE

/* PDP, VID4_PANIC_THRESH, VID4_ALERT_Y_THRESHOLD_MIN
*/
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MIN_MASK (0x000000FF)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MIN_LSBMASK \
	(0x000000FF)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MIN_SHIFT (0)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MIN_LENGTH (8)
#define ODN_PDP_VID4_PANIC_THRESH_VID4_ALERT_Y_THRESHOLD_MIN_SIGNED_FIELD \
	IMG_FALSE

#define ODN_PDP_BURST_BOUNDARY_OFFSET (0x09C0)

/* PDP, BURST_BOUNDARY, BURST_BOUNDARY
*/
#define ODN_PDP_BURST_BOUNDARY_BURST_BOUNDARY_MASK (0x0000003F)
#define ODN_PDP_BURST_BOUNDARY_BURST_BOUNDARY_LSBMASK (0x0000003F)
#define ODN_PDP_BURST_BOUNDARY_BURST_BOUNDARY_SHIFT (0)
#define ODN_PDP_BURST_BOUNDARY_BURST_BOUNDARY_LENGTH (6)
#define ODN_PDP_BURST_BOUNDARY_BURST_BOUNDARY_SIGNED_FIELD IMG_FALSE

/* ---------------------- End of register definitions ---------------------- */

/* NUMREG defines the extent of register address space.
*/

#define ODN_PDP_NUMREG ((0x09C0 >> 2) + 1)

/* Info about video plane addresses */
#define ODN_PDP_YADDR_BITS (ODN_PDP_VID1BASEADDR_VID1BASEADDR_LENGTH)
#define ODN_PDP_YADDR_ALIGN 5
#define ODN_PDP_UADDR_BITS (ODN_PDP_VID1UBASEADDR_VID1UBASEADDR_LENGTH)
#define ODN_PDP_UADDR_ALIGN 5
#define ODN_PDP_VADDR_BITS (ODN_PDP_VID1VBASEADDR_VID1VBASEADDR_LENGTH)
#define ODN_PDP_VADDR_ALIGN 5

#define ODN_PDP_YSTRIDE_BITS (ODN_PDP_VID1STRIDE_VID1STRIDE_LENGTH)
#define ODN_PDP_YSTRIDE_ALIGN 5

#define ODN_PDP_MAX_INPUT_WIDTH (ODN_PDP_VID1SIZE_VID1WIDTH_LSBMASK + 1)
#define ODN_PDP_MAX_INPUT_HEIGHT (ODN_PDP_VID1SIZE_VID1HEIGHT_LSBMASK + 1)

/* Maximum 6 bytes per pixel for RGB161616 */
#define ODN_PDP_MAX_IMAGE_BYTES \
	(ODN_PDP_MAX_INPUT_WIDTH * ODN_PDP_MAX_INPUT_HEIGHT * 6)

/* Round up */
#define ODN_PDP_MAX_IMAGE_PAGES \
	((ODN_PDP_MAX_IMAGE_BYTES + PAGE_SIZE - 1) / PAGE_SIZE)

#define ODN_PDP_YADDR_MAX \
	(((1 << ODN_PDP_YADDR_BITS) - 1) << ODN_PDP_YADDR_ALIGN)
#define ODN_PDP_UADDR_MAX \
	(((1 << ODN_PDP_UADDR_BITS) - 1) << ODN_PDP_UADDR_ALIGN)
#define ODN_PDP_VADDR_MAX \
	(((1 << ODN_PDP_VADDR_BITS) - 1) << ODN_PDP_VADDR_ALIGN)
#define ODN_PDP_YSTRIDE_MAX \
	((1 << ODN_PDP_YSTRIDE_BITS) << ODN_PDP_YSTRIDE_ALIGN)
#define ODN_PDP_YADDR_ALIGNMASK ((1 << ODN_PDP_YADDR_ALIGN) - 1)
#define ODN_PDP_UADDR_ALIGNMASK ((1 << ODN_PDP_UADDR_ALIGN) - 1)
#define ODN_PDP_VADDR_ALIGNMASK ((1 << ODN_PDP_VADDR_ALIGN) - 1)
#define ODN_PDP_YSTRIDE_ALIGNMASK ((1 << ODN_PDP_YSTRIDE_ALIGN) - 1)

/* Field Values (some are reserved for future use) */
#define ODN_PDP_SURF_PIXFMT_RGB332 0x3
#define ODN_PDP_SURF_PIXFMT_ARGB4444 0x4
#define ODN_PDP_SURF_PIXFMT_ARGB1555 0x5
#define ODN_PDP_SURF_PIXFMT_RGB888 0x6
#define ODN_PDP_SURF_PIXFMT_RGB565 0x7
#define ODN_PDP_SURF_PIXFMT_ARGB8888 0x8
#define ODN_PDP_SURF_PIXFMT_420_PL8 0x9
#define ODN_PDP_SURF_PIXFMT_420_PL8IVU 0xA
#define ODN_PDP_SURF_PIXFMT_420_PL8IUV 0xB
#define ODN_PDP_SURF_PIXFMT_422_UY0VY1_8888 0xC
#define ODN_PDP_SURF_PIXFMT_422_VY0UY1_8888 0xD
#define ODN_PDP_SURF_PIXFMT_422_Y0UY1V_8888 0xE
#define ODN_PDP_SURF_PIXFMT_422_Y0VY1U_8888 0xF
#define ODN_PDP_SURF_PIXFMT_AYUV8888 0x10
#define ODN_PDP_SURF_PIXFMT_YUV101010 0x15
#define ODN_PDP_SURF_PIXFMT_RGB101010 0x17
#define ODN_PDP_SURF_PIXFMT_420_PL10IUV 0x18
#define ODN_PDP_SURF_PIXFMT_420_PL10IVU 0x19
#define ODN_PDP_SURF_PIXFMT_422_PL10IUV 0x1A
#define ODN_PDP_SURF_PIXFMT_422_PL10IVU 0x1B
#define ODN_PDP_SURF_PIXFMT_RGB121212 0x1E
#define ODN_PDP_SURF_PIXFMT_RGB161616 0x1F

#define ODN_PDP_CTRL_CKEYSRC_PREV 0x0
#define ODN_PDP_CTRL_CKEYSRC_CUR 0x1

#define ODN_PDP_MEMCTRL_MEMREFRESH_ALWAYS 0x0
#define ODN_PDP_MEMCTRL_MEMREFRESH_HBLNK 0x1
#define ODN_PDP_MEMCTRL_MEMREFRESH_VBLNK 0x2
#define ODN_PDP_MEMCTRL_MEMREFRESH_BOTH 0x3

#define ODN_PDP_3D_CTRL_BLENDSEL_BGND_WITH_POS0 0x0
#define ODN_PDP_3D_CTRL_BLENDSEL_POS0_WITH_POS1 0x1
#define ODN_PDP_3D_CTRL_BLENDSEL_POS1_WITH_POS2 0x2
#define ODN_PDP_3D_CTRL_BLENDSEL_POS2_WITH_POS3 0x3
#define ODN_PDP_3D_CTRL_BLENDSEL_POS3_WITH_POS4 0x4
#define ODN_PDP_3D_CTRL_BLENDSEL_POS4_WITH_POS5 0x5
#define ODN_PDP_3D_CTRL_BLENDSEL_POS5_WITH_POS6 0x6
#define ODN_PDP_3D_CTRL_BLENDSEL_POS6_WITH_POS7 0x7

#define ODN_PDP_UADDR_UV_STRIDE_EQUAL_TO_Y_STRIDE 0x0
#define ODN_PDP_UADDR_UV_STRIDE_EQUAL_TO_DOUBLE_Y_STRIDE 0x1
#define ODN_PDP_UADDR_UV_STRIDE_EQUAL_TO_HALF_Y_STRIDE 0x2

#define ODN_PDP_PROCAMP_OUTPUT_OFFSET_FRACTIONAL_BITS 1
#define ODN_PDP_PROCAMP_COEFFICIENT_FRACTIONAL_BITS 10

/*---------------------------------------------------------------------------*/

#endif /* ODN_PDP_REGS_H */
