/*************************************************************************/ /*!
@File
@Title          OS agnostic implementation of Debug Info interface.
@Copyright      Copyright (c) Imagination Technologies Ltd. All Rights Reserved
@License        Dual MIT/GPLv2

The contents of this file are subject to the MIT license as set out below.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

Alternatively, the contents of this file may be used under the terms of
the GNU General Public License Version 2 ("GPL") in which case the provisions
of GPL are applicable instead of those above.

If you wish to allow use of your version of this file only under the terms of
GPL, and not to allow others to use your version of this file under the terms
of the MIT license, indicate your decision by deleting the provisions above
and replace them with the notice and other provisions required by GPL as set
out in the file called "GPL-COPYING" included in this distribution. If you do
not delete the provisions above, a recipient may use your version of this file
under the terms of either the MIT license or GPL.

This License is also included in this distribution in the file called
"MIT-COPYING".

EXCEPT AS OTHERWISE STATED IN A NEGOTIATED AGREEMENT: (A) THE SOFTWARE IS
PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
PURPOSE AND NONINFRINGEMENT; AND (B) IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ /**************************************************************************/

#ifndef PVR_IMPL_BRG_H
#define PVR_IMPL_BRG_H

#include "pvrsrv_error.h"
#include "lock_types.h"

typedef struct DI_CONTEXT_TAG DI_CONTEXT;
typedef struct DI_ENTRY_DESC DI_ENTRY_DESC;

PVRSRV_ERROR PVRDIImplBrgRegister(void);

/*! @Function DICreateContextKM
 *
 * @Description
 * Creates DI context which among others also creates a TL stream for reading
 * entries.
 *
 * @Output pszStreamName: name of the TL stream created in this context
 * @Output ppsContext: pointer to the new context
 *
 * @Return PVRSRV_ERROR error code
 *         PVRSRV_OK in case of a success
 *         PVRSRV_ERROR_INVALID_PARAMS if any of the parameters is invalid
 *         PVRSRV_ERROR_OUT_OF_MEMORY if any of the memory allocations failed
 *         error codes returned by TLStreamCreate()
 */
PVRSRV_ERROR DICreateContextKM(IMG_CHAR *pszStreamName,
			       DI_CONTEXT **ppsContext);

/*! @Function DIDestroyContextKM
 *
 * @Description
 * Destroy the DI context and all underlying dependencies.
 *
 * @Input psContext: pointer to the context
 *
 * @Return PVRSRV_ERROR error code
 *         PVRSRV_OK in case of a success
 *         PVRSRV_ERROR_INVALID_PARAMS if invalid context pointer given
 */
PVRSRV_ERROR DIDestroyContextKM(DI_CONTEXT *psContext);

PVRSRV_ERROR DIReadEntryKM(DI_CONTEXT *psContext, const IMG_CHAR *pszEntryPath,
			   IMG_UINT64 ui64Offset, IMG_UINT64 ui64Size);

PVRSRV_ERROR DIWriteEntryKM(DI_CONTEXT *psContext, const IMG_CHAR *pszEntryPath,
			    IMG_UINT32 ui32ValueSize, const IMG_CHAR *pszValue);

PVRSRV_ERROR DIListAllEntriesKM(DI_CONTEXT *psContext);

#ifdef PVR_TESTING_UTILS
PVRSRV_ERROR DIContextRefCount(DI_CONTEXT *psContext, ATOMIC_T **ppiAtomic);
#endif /* PVR_TESTING_UTILS */

#endif /* PVR_IMPL_BRG_H */
