/**************************************************************************/ /*!
@File
@Title          Implementation Callbacks for Physmem (PMR) abstraction
@Copyright      Copyright (c) Imagination Technologies Ltd. All Rights Reserved
@Description    Part of the memory management. This file is for definitions
                that are private to the world of PMRs, but that need to be
                shared between pmr.c itself and the modules that implement the
                callbacks for the PMR.
@License        Dual MIT/GPLv2

The contents of this file are subject to the MIT license as set out below.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

Alternatively, the contents of this file may be used under the terms of
the GNU General Public License Version 2 ("GPL") in which case the provisions
of GPL are applicable instead of those above.

If you wish to allow use of your version of this file only under the terms of
GPL, and not to allow others to use your version of this file under the terms
of the MIT license, indicate your decision by deleting the provisions above
and replace them with the notice and other provisions required by GPL as set
out in the file called "GPL-COPYING" included in this distribution. If you do
not delete the provisions above, a recipient may use your version of this file
under the terms of either the MIT license or GPL.

This License is also included in this distribution in the file called
"MIT-COPYING".

EXCEPT AS OTHERWISE STATED IN A NEGOTIATED AGREEMENT: (A) THE SOFTWARE IS
PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
PURPOSE AND NONINFRINGEMENT; AND (B) IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ /***************************************************************************/

#ifndef SRVSRV_PMR_IMPL_H
#define SRVSRV_PMR_IMPL_H

#include "img_types.h"
#include "img_defs.h"
#include "pvrsrv_error.h"

/*! Physical Memory Resource type.
 */
typedef struct _PMR_ PMR;

/*! Per-flavour callbacks need to be shared with generic implementation
 * (pmr.c).
 */
typedef void *PMR_IMPL_PRIVDATA;

/*! Type for holding flags passed to the PMR factory.
 */
typedef PVRSRV_MEMALLOCFLAGS_T PMR_FLAGS_T;

/*! Mapping table for the allocation.
 *
 * PMR's can be sparse in which case not all the logical addresses in it are
 * valid. The mapping table translates logical offsets into physical offsets.
 *
 * This table is always passed to the PMR factory regardless if the memory is
 * sparse or not. In case of non-sparse memory all virtual offsets are mapped
 * to physical offsets.
 */
typedef struct _PMR_MAPPING_TABLE_ PMR_MAPPING_TABLE;

/*! Private data passed to the ::PFN_MMAP_FN function.
 */
typedef void *PMR_MMAP_DATA;

#define PMR_IMPL_TYPES                                                      \
	X(NONE), X(OSMEM), X(LMA), X(DMABUF), X(EXTMEM), X(DC), X(TDFWMEM), \
		X(TDSECBUF), X(LAST)

/*! PMR factory type.
 */
typedef enum _PMR_IMPL_TYPE_ {
#define X(type) PMR_TYPE_##type
	PMR_IMPL_TYPES
#undef X
} PMR_IMPL_TYPE;

/*************************************************************************/ /*!
@Brief          Callback function type PFN_LOCK_PHYS_ADDRESSES_FN

@Description    Called to lock down the physical addresses for all pages
                allocated for a PMR.
                The default implementation is to simply increment a
                lock-count for debugging purposes.
                If overridden, the PFN_LOCK_PHYS_ADDRESSES_FN function will
                be called when someone first requires a physical address,
                and the PFN_UNLOCK_PHYS_ADDRESSES_FN counterpart will be
                called when the last such reference is released.
                The PMR implementation may assume that physical addresses
                will have been "locked" in this manner before any call is
                made to the pfnDevPhysAddr() callback

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)

@Return         PVRSRV_OK if the operation was successful, an error code
                otherwise.
*/ /**************************************************************************/
typedef PVRSRV_ERROR (*PFN_LOCK_PHYS_ADDRESSES_FN)(PMR_IMPL_PRIVDATA pvPriv);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_UNLOCK_PHYS_ADDRESSES_FN

@Description    Called to release the lock taken on the physical addresses
                for all pages allocated for a PMR.
                The default implementation is to simply decrement a
                lock-count for debugging purposes.
                If overridden, the PFN_UNLOCK_PHYS_ADDRESSES_FN will be
                called when the last reference taken on the PMR is
                released.

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)

@Return         PVRSRV_OK if the operation was successful, an error code
                otherwise.
*/ /**************************************************************************/
typedef PVRSRV_ERROR (*PFN_UNLOCK_PHYS_ADDRESSES_FN)(PMR_IMPL_PRIVDATA pvPriv);

#if defined(PVRSRV_SUPPORT_IPA_FEATURE)
/*************************************************************************/ /*!
@Brief          Callback function type PFN_DEV_PHYS_ADDR_FN

@Description    Called to obtain one or more physical addresses for given
                offsets within a PMR.

                The PFN_LOCK_PHYS_ADDRESSES_FN callback (if overridden) is
                guaranteed to have been called prior to calling the
                PFN_DEV_PHYS_ADDR_FN callback and the caller promises not to
                rely on the physical address thus obtained after the
                PFN_UNLOCK_PHYS_ADDRESSES_FN callback is called.

   Implementation of this callback is mandatory.

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)
@Input          ui32Log2PageSize      The log2 page size.
@Input          ui32NumOfAddr         The number of addresses to be returned
@Input          puiOffset             The offset from the start of the PMR
                                      (in bytes) for which the physical
                                      address is required. Where multiple
                                      addresses are requested, this will
                                      contain a list of offsets.
@Input          ui64IPAPolicyValue    The Intermediate Physical Address (IPA)
                                      Policy value to be applied to
                                      the physical address
@Input          ui64IPAClearMask      The IPA Clear mask to be applied to
                                      the physical address when setting policy.
@Output         pbValid               List of boolean flags indicating which
                                      addresses in the returned list
                                      (psDevAddrPtr) are valid (for sparse
                                      allocations, not all pages may have a
                                      physical backing)
@Output         psDevAddrPtr          Returned list of physical addresses

@Return         PVRSRV_OK if the operation was successful, an error code
                otherwise.
*/ /**************************************************************************/
#else
/*************************************************************************/ /*!
@Brief          Callback function type PFN_DEV_PHYS_ADDR_FN

@Description    Called to obtain one or more physical addresses for given
                offsets within a PMR.

                The PFN_LOCK_PHYS_ADDRESSES_FN callback (if overridden) is
                guaranteed to have been called prior to calling the
                PFN_DEV_PHYS_ADDR_FN callback and the caller promises not to
                rely on the physical address thus obtained after the
                PFN_UNLOCK_PHYS_ADDRESSES_FN callback is called.

   Implementation of this callback is mandatory.

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)
@Input          ui32Log2PageSize      The log2 page size.
@Input          ui32NumOfAddr         The number of addresses to be returned
@Input          puiOffset             The offset from the start of the PMR
                                      (in bytes) for which the physical
                                      address is required. Where multiple
                                      addresses are requested, this will
                                      contain a list of offsets.
@Output         pbValid               List of boolean flags indicating which
                                      addresses in the returned list
                                      (psDevAddrPtr) are valid (for sparse
                                      allocations, not all pages may have a
                                      physical backing)
@Output         psDevAddrPtr          Returned list of physical addresses

@Return         PVRSRV_OK if the operation was successful, an error code
                otherwise.
*/ /**************************************************************************/
#endif
typedef PVRSRV_ERROR (*PFN_DEV_PHYS_ADDR_FN)(
	PMR_IMPL_PRIVDATA pvPriv, IMG_UINT32 ui32Log2PageSize,
	IMG_UINT32 ui32NumOfAddr, IMG_DEVMEM_OFFSET_T *puiOffset,
#if defined(PVRSRV_SUPPORT_IPA_FEATURE)
	IMG_UINT64 ui64IPAPolicyValue, IMG_UINT64 ui64IPAClearMask,
#endif
	IMG_BOOL *pbValid, IMG_DEV_PHYADDR *psDevAddrPtr);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_ACQUIRE_KERNEL_MAPPING_DATA_FN

@Description    Called to obtain a kernel-accessible address (mapped to a
                virtual address if required) for the PMR for use internally
                in Services.

    Implementation of this function for the (default) PMR factory providing
    OS-allocations is mandatory (the driver will expect to be able to call
    this function for OS-provided allocations).
    For other PMR factories, implementation of this function is only necessary
    where an MMU mapping is required for the Kernel to be able to access the
    allocated memory.
    If no mapping is needed, this function can remain unimplemented and the
    pfn may be set to NULL.
@Input          pvPriv                Private data (which was generated by
                                      the PMR factory when PMR was created)
@Input          uiOffset              Offset from the beginning of the PMR
                                      at which mapping is to start
@Input          uiSize                Size of mapping (in bytes)
@Output         ppvKernelAddressOut   Mapped kernel address
@Output         phHandleOut           Returned handle of the new mapping
@Input          ulFlags               Mapping flags

@Return         PVRSRV_OK if the mapping was successful, an error code
                otherwise.
*/ /**************************************************************************/
typedef PVRSRV_ERROR (*PFN_ACQUIRE_KERNEL_MAPPING_DATA_FN)(
	PMR_IMPL_PRIVDATA pvPriv, size_t uiOffset, size_t uiSize,
	void **ppvKernelAddressOut, IMG_HANDLE *phHandleOut,
	PMR_FLAGS_T ulFlags);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_RELEASE_KERNEL_MAPPING_DATA_FN

@Description    Called to release a mapped kernel virtual address

   Implementation of this callback is mandatory if
   PFN_ACQUIRE_KERNEL_MAPPING_DATA_FN is provided for the PMR factory,
   otherwise this function can remain unimplemented and the pfn may be set
   to NULL.

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)
@Input          hHandle               Handle of the mapping to be released

@Return         None
*/ /**************************************************************************/
typedef void (*PFN_RELEASE_KERNEL_MAPPING_DATA_FN)(PMR_IMPL_PRIVDATA pvPriv,
						   IMG_HANDLE hHandle);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_READ_BYTES_FN

@Description    Called to read bytes from an unmapped allocation

   Implementation of this callback is optional - where it is not provided,
   the driver will use PFN_ACQUIRE_KERNEL_MAPPING_DATA_FN to map the entire
   PMR (if an MMU mapping is required for the Kernel to be able to access the
   allocated memory).

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)
@Input          uiOffset              Offset from the beginning of the PMR at
                                      which to begin reading
@Output         pcBuffer              Buffer in which to return the read data
@Input          uiBufSz               Number of bytes to be read
@Output         puiNumBytes           Number of bytes actually read (may be
                                      less than uiBufSz)

@Return         PVRSRV_OK if the read was successful, an error code
                otherwise.
*/ /**************************************************************************/
typedef PVRSRV_ERROR (*PFN_READ_BYTES_FN)(PMR_IMPL_PRIVDATA pvPriv,
					  IMG_DEVMEM_OFFSET_T uiOffset,
					  IMG_UINT8 *pcBuffer, size_t uiBufSz,
					  size_t *puiNumBytes);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_WRITE_BYTES_FN

@Description    Called to write bytes into an unmapped allocation

   Implementation of this callback is optional - where it is not provided,
   the driver will use PFN_ACQUIRE_KERNEL_MAPPING_DATA_FN to map the entire
   PMR (if an MMU mapping is required for the Kernel to be able to access the
   allocated memory).

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)
@Input          uiOffset              Offset from the beginning of the PMR at
                                      which to begin writing
@Input          pcBuffer              Buffer containing the data to be written
@Input          uiBufSz               Number of bytes to be written
@Output         puiNumBytes           Number of bytes actually written (may be
                                      less than uiBufSz)

@Return         PVRSRV_OK if the write was successful, an error code
                otherwise.
*/ /**************************************************************************/
typedef PVRSRV_ERROR (*PFN_WRITE_BYTES_FN)(PMR_IMPL_PRIVDATA pvPriv,
					   IMG_DEVMEM_OFFSET_T uiOffset,
					   IMG_UINT8 *pcBuffer, size_t uiBufSz,
					   size_t *puiNumBytes);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_CHANGE_SPARSE_MEM_FN

@Description    Called to modify the physical backing for a given sparse
                allocation.
                The caller provides a list of the pages within the sparse
                allocation which should be backed with a physical allocation
                and a list of the pages which do not require backing.

   Implementation of this callback is mandatory.

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)
@Input          psPMR                 The PMR of the sparse allocation to be
                                      modified
@Input          ui32AllocPageCount    The number of pages specified in
                                      pai32AllocIndices
@Input          pai32AllocIndices     The list of pages in the sparse
                                      allocation that should be backed with a
                                      physical allocation. Pages are
                                      referenced by their index within the
                                      sparse allocation (e.g. in a 10 page
                                      allocation, pages are denoted by
                                      indices 0 to 9)
@Input          ui32FreePageCount     The number of pages specified in
                                      pai32FreeIndices
@Input          pai32FreeIndices      The list of pages in the sparse
                                      allocation that do not require
                                      a physical allocation.
@Input          ui32Flags             Allocation flags

@Return         PVRSRV_OK if the sparse allocation physical backing was updated
                successfully, an error code otherwise.
*/ /**************************************************************************/
typedef PVRSRV_ERROR (*PFN_CHANGE_SPARSE_MEM_FN)(PMR_IMPL_PRIVDATA pPriv,
						 const PMR *psPMR,
						 IMG_UINT32 ui32AllocPageCount,
						 IMG_UINT32 *pai32AllocIndices,
						 IMG_UINT32 ui32FreePageCount,
						 IMG_UINT32 *pai32FreeIndices,
						 IMG_UINT32 uiFlags);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_CHANGE_SPARSE_MEM_CPU_MAP_FN

@Description    Called to modify which pages are mapped for a given sparse
                allocation.
                The caller provides a list of the pages within the sparse
                allocation which should be given a CPU mapping and a list
                of the pages which do not require a CPU mapping.

   Implementation of this callback is mandatory.

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)
@Input          psPMR                 The PMR of the sparse allocation to be
                                      modified
@Input          sCpuVAddrBase         The virtual base address of the sparse
                                      allocation
@Input          ui32AllocPageCount    The number of pages specified in
                                      pai32AllocIndices
@Input          pai32AllocIndices     The list of pages in the sparse
                                      allocation that should be given a CPU
                                      mapping. Pages are referenced by their
                                      index within the sparse allocation (e.g.
                                      in a 10 page allocation, pages are
                                      denoted by indices 0 to 9)
@Input          ui32FreePageCount     The number of pages specified in
                                      pai32FreeIndices
@Input          pai32FreeIndices      The list of pages in the sparse
                                      allocation that do not require a CPU
                                      mapping.

@Return         PVRSRV_OK if the page mappings were updated successfully, an
                error code otherwise.
*/ /**************************************************************************/
typedef PVRSRV_ERROR (*PFN_CHANGE_SPARSE_MEM_CPU_MAP_FN)(
	PMR_IMPL_PRIVDATA pPriv, const PMR *psPMR, IMG_UINT64 sCpuVAddrBase,
	IMG_UINT32 ui32AllocPageCount, IMG_UINT32 *pai32AllocIndices,
	IMG_UINT32 ui32FreePageCount, IMG_UINT32 *pai32FreeIndices);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_MMAP_FN

@Description    Called to map pages in the specified PMR.

   Implementation of this callback is optional.
   Where it is provided, it will be used in place of OSMMapPMRGeneric().

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)
@Input          psPMR                 The PMR of the allocation to be mapped
@Input          pMMapData             OS-specific data to describe how mapping
                                      should be performed

@Return         PVRSRV_OK if the mapping was successful, an error code
                otherwise.
*/ /**************************************************************************/
typedef PVRSRV_ERROR (*PFN_MMAP_FN)(PMR_IMPL_PRIVDATA pPriv, PMR *psPMR,
				    PMR_MMAP_DATA pMMapData);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_FINALIZE_FN

@Description    Called to destroy the PMR.
                This callback will be called only when all references to
                the PMR have been dropped.
                The PMR was created via a call to PhysmemNewRamBackedPMR()
                and is destroyed via this callback.

   Implementation of this callback is mandatory.

@Input          pvPriv                Private data (which was generated by the
                                      PMR factory when PMR was created)

@Return         None
*/ /**************************************************************************/
typedef void (*PFN_FINALIZE_FN)(PMR_IMPL_PRIVDATA pvPriv);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_ACQUIRE_PMR_FACTORY_LOCK_FN

@Description    Called to acquire the PMR factory's global lock, if it has one,
                hence callback optional. Factories which support entry points
                in addition to the normal bridge calls, for example, from the
                native OS that manipulate the PMR reference count should
                create a factory lock and implementations for these call backs.

   Implementation of this callback is optional.

@Return         None
*/
/*****************************************************************************/
typedef void (*PFN_ACQUIRE_PMR_FACTORY_LOCK_FN)(void);

/*************************************************************************/ /*!
@Brief          Callback function type PFN_RELEASE_PMR_FACTORY_LOCK_FN

@Description    Called to release the PMR factory's global lock acquired by calling
                pfn_acquire_pmr_factory_lock callback.

   Implementation of this callback is optional.

@Return         None
*/ /**************************************************************************/
typedef void (*PFN_RELEASE_PMR_FACTORY_LOCK_FN)(void);

#ifdef SUPPORT_PMR_DEFERRED_FREE
/*************************************************************************/ /*!
@Brief          Callback function type PFN_ZOMBIFY_FN

@Description    Called to perform factory actions necessary when PMR becomes
                a zombie PMR.

                This function should at least adjust the driver/process memory
                stats to reflect the amount of memory is occupied by the zombie
                PMRs and at the same time subtract the memory from the main
                memory stat the pages are accounted under.

   Implementation of this callback is required when SUPPORT_PMR_DEFERRED_FREE=1.

@Return         PVRSRV_OK if the operation was successful, an error code
                otherwise.
*/ /**************************************************************************/
typedef PVRSRV_ERROR (*PFN_ZOMBIFY_FN)(PMR_IMPL_PRIVDATA pvPriv, PMR *psPMR);
#endif

/*! PMR factory callback table.
 */
struct _PMR_IMPL_FUNCTAB_ {
	/*! Callback function pointer, see ::PFN_LOCK_PHYS_ADDRESSES_FN */
	PFN_LOCK_PHYS_ADDRESSES_FN pfnLockPhysAddresses;
	/*! Callback function pointer, see ::PFN_UNLOCK_PHYS_ADDRESSES_FN */
	PFN_UNLOCK_PHYS_ADDRESSES_FN pfnUnlockPhysAddresses;

	/*! Callback function pointer, see ::PFN_DEV_PHYS_ADDR_FN */
	PFN_DEV_PHYS_ADDR_FN pfnDevPhysAddr;

	/*! Callback function pointer, see ::PFN_ACQUIRE_KERNEL_MAPPING_DATA_FN */
	PFN_ACQUIRE_KERNEL_MAPPING_DATA_FN pfnAcquireKernelMappingData;
	/*! Callback function pointer, see ::PFN_RELEASE_KERNEL_MAPPING_DATA_FN */
	PFN_RELEASE_KERNEL_MAPPING_DATA_FN pfnReleaseKernelMappingData;

	/*! Callback function pointer, see ::PFN_READ_BYTES_FN */
	PFN_READ_BYTES_FN pfnReadBytes;
	/*! Callback function pointer, see ::PFN_WRITE_BYTES_FN */
	PFN_WRITE_BYTES_FN pfnWriteBytes;

	/*! Callback function pointer, see ::PFN_CHANGE_SPARSE_MEM_FN */
	PFN_CHANGE_SPARSE_MEM_FN pfnChangeSparseMem;
	/*! Callback function pointer, see ::PFN_CHANGE_SPARSE_MEM_CPU_MAP_FN */
	PFN_CHANGE_SPARSE_MEM_CPU_MAP_FN pfnChangeSparseMemCPUMap;

	/*! Callback function pointer, see ::PFN_MMAP_FN */
	PFN_MMAP_FN pfnMMap;

	/*! Callback function pointer, see ::PFN_FINALIZE_FN */
	PFN_FINALIZE_FN pfnFinalize;

	/*! Callback function pointer, see ::PFN_ACQUIRE_PMR_FACTORY_LOCK_FN */
	PFN_ACQUIRE_PMR_FACTORY_LOCK_FN pfnGetPMRFactoryLock;

	/*! Callback function pointer, see ::PFN_RELEASE_PMR_FACTORY_LOCK_FN */
	PFN_RELEASE_PMR_FACTORY_LOCK_FN pfnReleasePMRFactoryLock;

#ifdef SUPPORT_PMR_DEFERRED_FREE
	/*! Callback function pointer, see ::PFN_ZOMBIFY_FN */
	PFN_ZOMBIFY_FN pfnZombify;
#endif
};

/*! PMR factory callback table.
 */
typedef struct _PMR_IMPL_FUNCTAB_ PMR_IMPL_FUNCTAB;

#endif /* SRVSRV_PMR_IMPL_H */
