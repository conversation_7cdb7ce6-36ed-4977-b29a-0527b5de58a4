/*
 * @File        pvr_sync_ioctl_common.h
 * @Title       Kernel driver for Android's sync mechanism
 * @Codingstyle LinuxKernel
 * @Copyright   Copyright (c) Imagination Technologies Ltd. All Rights Reserved
 * @License     Dual MIT/GPLv2
 *
 * The contents of this file are subject to the MIT license as set out below.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * Alternatively, the contents of this file may be used under the terms of
 * the GNU General Public License Version 2 ("GPL") in which case the provisions
 * of GPL are applicable instead of those above.
 *
 * If you wish to allow use of your version of this file only under the terms of
 * GPL, and not to allow others to use your version of this file under the terms
 * of the MIT license, indicate your decision by deleting the provisions above
 * and replace them with the notice and other provisions required by GPL as set
 * out in the file called "GPL-COPYING" included in this distribution. If you do
 * not delete the provisions above, a recipient may use your version of this file
 * under the terms of either the MIT license or GPL.
 *
 * This License is also included in this distribution in the file called
 * "MIT-COPYING".
 *
 * EXCEPT AS OTHERWISE STATED IN A NEGOTIATED AGREEMENT: (A) THE SOFTWARE IS
 * PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
 * PURPOSE AND NONINFRINGEMENT; AND (B) IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

#ifndef _PVR_SYNC_IOCTL_COMMON_H
#define _PVR_SYNC_IOCTL_COMMON_H

struct file;

/* Functions provided by pvr_sync_ioctl_common.c */

int pvr_sync_open_common(void *connection_data, void *file_handle);
int pvr_sync_close_common(void *connection_data);
void *pvr_sync_get_api_priv_common(struct file *file);

#if defined(USE_PVRSYNC_DEVNODE)
#define PVR_SYNC_IOCTL_DISPATCH_DECL(name)                  \
	int pvr_sync_ioctl_common_##name(struct file *file, \
					 void __user *user_data)
#else /* defined(USE_PVRSYNC_DEVNODE) */
#define PVR_SYNC_IOCTL_DISPATCH_DECL(name) \
	int pvr_sync_ioctl_common_##name(struct file *file, void *user_data)
#endif /* defined(USE_PVRSYNC_DEVNODE) */

PVR_SYNC_IOCTL_DISPATCH_DECL(rename);
PVR_SYNC_IOCTL_DISPATCH_DECL(force_sw_only);
PVR_SYNC_IOCTL_DISPATCH_DECL(force_exp_only);
PVR_SYNC_IOCTL_DISPATCH_DECL(sw_create_fence);
PVR_SYNC_IOCTL_DISPATCH_DECL(create_export_fence);
PVR_SYNC_IOCTL_DISPATCH_DECL(sw_inc);

struct pvr_sync_file_data;

/* Functions required by pvr_sync_ioctl_common.c */

bool pvr_sync_set_private_data(void *connection_data,
			       struct pvr_sync_file_data *fdata);

struct pvr_sync_file_data *
pvr_sync_connection_private_data(void *connection_data);

struct pvr_sync_file_data *pvr_sync_get_private_data(struct file *file);

bool pvr_sync_is_timeline(struct file *file);

#endif /* _PVR_SYNC_IOCTL_COMMON_H */
