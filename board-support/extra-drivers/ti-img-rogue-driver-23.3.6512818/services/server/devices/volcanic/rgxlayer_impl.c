/*************************************************************************/ /*!
@File
@Title          DDK implementation of the Services abstraction layer
@Copyright      Copyright (c) Imagination Technologies Ltd. All Rights Reserved
@Description    DDK implementation of the Services abstraction layer
@License        Dual MIT/GPLv2

The contents of this file are subject to the MIT license as set out below.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

Alternatively, the contents of this file may be used under the terms of
the GNU General Public License Version 2 ("GPL") in which case the provisions
of GPL are applicable instead of those above.

If you wish to allow use of your version of this file only under the terms of
GPL, and not to allow others to use your version of this file under the terms
of the MIT license, indicate your decision by deleting the provisions above
and replace them with the notice and other provisions required by GPL as set
out in the file called "GPL-COPYING" included in this distribution. If you do
not delete the provisions above, a recipient may use your version of this file
under the terms of either the MIT license or GPL.

This License is also included in this distribution in the file called
"MIT-COPYING".

EXCEPT AS OTHERWISE STATED IN A NEGOTIATED AGREEMENT: (A) THE SOFTWARE IS
PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
PURPOSE AND NONINFRINGEMENT; AND (B) IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ /**************************************************************************/

#include "rgxlayer_impl.h"
#include "osfunc.h"
#include "pdump_km.h"
#include "rgxfwutils.h"
#include "rgxfwimageutils.h"
#include "cache_km.h"
#include "km/rgxdefs_km.h"

#if defined(PDUMP)
#if defined(__linux__)
#include <linux/version.h>

#if (LINUX_VERSION_CODE >= KERNEL_VERSION(5, 15, 0))
#include <linux/stdarg.h>
#else
#include <stdarg.h>
#endif /* LINUX_VERSION_CODE >= KERNEL_VERSION(5, 15, 0) */
#else
#include <stdarg.h>
#endif /* __linux__ */
#endif

#define MAX_NUM_COHERENCY_TESTS (10)
IMG_BOOL RGXDoFWSlaveBoot(const void *hPrivate)
{
	PVRSRV_RGXDEV_INFO *psDevInfo;
	PVRSRV_DEVICE_NODE *psDeviceNode;

	PVR_ASSERT(hPrivate != NULL);
	psDevInfo = ((RGX_LAYER_PARAMS *)hPrivate)->psDevInfo;

	if (psDevInfo->ui32CoherencyTestsDone >= MAX_NUM_COHERENCY_TESTS) {
		return IMG_FALSE;
	}

	psDeviceNode = psDevInfo->psDeviceNode;
#if !defined(NO_HARDWARE)
	return (PVRSRVSystemSnoopingOfCPUCache(psDeviceNode->psDevConfig) &&
		PVRSRVSystemSnoopingOfDeviceCache(psDeviceNode->psDevConfig));
#else
	return IMG_FALSE;
#endif
}

/*
 * The fabric coherency test is performed when platform supports fabric coherency
 * either in the form of ACE-lite or Full-ACE. This test is done quite early
 * with the firmware processor quiescent and makes exclusive use of the slave
 * port interface for reading/writing through the device memory hierarchy. The
 * rationale for the test is to ensure that what the CPU writes to its dcache
 * is visible to the GPU via coherency snoop miss/hit and vice-versa without
 * any intervening cache maintenance by the writing agent.
 */
PVRSRV_ERROR RGXFabricCoherencyTest(const void *hPrivate)
{
	PVRSRV_RGXDEV_INFO *psDevInfo;
	IMG_UINT32 *pui32FabricCohTestBufferCpuVA = NULL;
	IMG_UINT32 *pui32FabricCohCcTestBufferCpuVA = NULL;
	IMG_UINT32 *pui32FabricCohNcTestBufferCpuVA = NULL;
	DEVMEM_MEMDESC *psFabricCohTestBufferMemDesc = NULL;
	DEVMEM_MEMDESC *psFabricCohCcTestBufferMemDesc = NULL;
	DEVMEM_MEMDESC *psFabricCohNcTestBufferMemDesc = NULL;
	RGXFWIF_DEV_VIRTADDR sFabricCohCcTestBufferDevVA;
	RGXFWIF_DEV_VIRTADDR sFabricCohNcTestBufferDevVA;
	RGXFWIF_DEV_VIRTADDR *psFabricCohTestBufferDevVA = NULL;
	IMG_DEVMEM_SIZE_T uiFabricCohTestBlockSize = sizeof(IMG_UINT64);
	IMG_DEVMEM_ALIGN_T uiFabricCohTestBlockAlign = sizeof(IMG_UINT64);
	IMG_UINT64 ui64SegOutAddrTopCached = 0;
	IMG_UINT64 ui64SegOutAddrTopUncached = 0;
	IMG_UINT32 ui32OddEven;
	IMG_UINT32 ui32OddEvenSeed = 1;
	PVRSRV_ERROR eError = PVRSRV_OK;
	IMG_BOOL bFullTestPassed = IMG_TRUE;
	IMG_BOOL bExit = IMG_FALSE;
#if defined(DEBUG)
	IMG_BOOL bSubTestPassed = IMG_FALSE;
#endif
	enum TEST_TYPE {
		CPU_WRITE_GPU_READ_SM = 0,
		GPU_WRITE_CPU_READ_SM,
		CPU_WRITE_GPU_READ_SH,
		GPU_WRITE_CPU_READ_SH
	} eTestType;

	PVR_ASSERT(hPrivate != NULL);
	psDevInfo = ((RGX_LAYER_PARAMS *)hPrivate)->psDevInfo;

	PVR_LOG(("Starting fabric coherency test ....."));

	/* Size and align are 'expanded' because we request an export align allocation */
	eError = DevmemExportalignAdjustSizeAndAlign(
		DevmemGetHeapLog2PageSize(psDevInfo->psFirmwareMainHeap),
		&uiFabricCohTestBlockSize, &uiFabricCohTestBlockAlign);
	if (eError != PVRSRV_OK) {
		PVR_DPF((
			PVR_DBG_ERROR,
			"DevmemExportalignAdjustSizeAndAlign() error: %s, exiting",
			PVRSRVGetErrorString(eError)));
		goto e0;
	}

	/* Allocate, acquire cpu address and set firmware address for cc=1 buffer */
	eError = DevmemFwAllocateExportable(
		psDevInfo->psDeviceNode, uiFabricCohTestBlockSize,
		uiFabricCohTestBlockAlign,
		PVRSRV_MEMALLOCFLAG_DEVICE_FLAG(PMMETA_PROTECT) |
			PVRSRV_MEMALLOCFLAG_KERNEL_CPU_MAPPABLE |
			PVRSRV_MEMALLOCFLAG_ZERO_ON_ALLOC |
			PVRSRV_MEMALLOCFLAG_GPU_CACHE_COHERENT |
			PVRSRV_MEMALLOCFLAG_CPU_CACHE_INCOHERENT |
			PVRSRV_MEMALLOCFLAG_GPU_READABLE |
			PVRSRV_MEMALLOCFLAG_GPU_WRITEABLE |
			PVRSRV_MEMALLOCFLAG_CPU_READABLE |
			PVRSRV_MEMALLOCFLAG_CPU_WRITEABLE |
			PVRSRV_MEMALLOCFLAG_PHYS_HEAP_HINT(FW_MAIN),
		"FwExFabricCoherencyCcTestBuffer",
		&psFabricCohCcTestBufferMemDesc);
	if (eError != PVRSRV_OK) {
		PVR_DPF((PVR_DBG_ERROR,
			 "DevmemFwAllocateExportable() error: %s, exiting",
			 PVRSRVGetErrorString(eError)));
		goto e0;
	}

	eError = DevmemAcquireCpuVirtAddr(
		psFabricCohCcTestBufferMemDesc,
		(void **)&pui32FabricCohCcTestBufferCpuVA);
	if (eError != PVRSRV_OK) {
		PVR_DPF((PVR_DBG_ERROR,
			 "DevmemAcquireCpuVirtAddr() error: %s, exiting",
			 PVRSRVGetErrorString(eError)));
		goto e1;
	}

	/* Create a FW address which is uncached in the Meta DCache and in the SLC using the Meta bootloader segment.
	   This segment is the only one configured correctly out of reset (when this test is meant to be executed) */
	eError = RGXSetFirmwareAddress(&sFabricCohCcTestBufferDevVA,
				       psFabricCohCcTestBufferMemDesc, 0,
				       RFW_FWADDR_FLAG_NONE);
	PVR_LOG_GOTO_IF_ERROR(eError, "RGXSetFirmwareAddress:1", e2);

	/* Undo most of the FW mappings done by RGXSetFirmwareAddress */
	sFabricCohCcTestBufferDevVA.ui32Addr &=
		~RGXFW_SEGMMU_DATA_META_CACHE_MASK;
	sFabricCohCcTestBufferDevVA.ui32Addr &=
		~RGXFW_SEGMMU_DATA_VIVT_SLC_CACHE_MASK;
	sFabricCohCcTestBufferDevVA.ui32Addr -= RGXFW_SEGMMU_DATA_BASE_ADDRESS;

	/* Map the buffer in the bootloader segment as uncached */
	sFabricCohCcTestBufferDevVA.ui32Addr |= RGXFW_BOOTLDR_META_ADDR;
	sFabricCohCcTestBufferDevVA.ui32Addr |= RGXFW_SEGMMU_DATA_META_UNCACHED;

	/* Allocate, acquire cpu address and set firmware address for cc=0 buffer */
	eError = DevmemFwAllocateExportable(
		psDevInfo->psDeviceNode, uiFabricCohTestBlockSize,
		uiFabricCohTestBlockAlign,
		PVRSRV_MEMALLOCFLAG_DEVICE_FLAG(PMMETA_PROTECT) |
			PVRSRV_MEMALLOCFLAG_KERNEL_CPU_MAPPABLE |
			PVRSRV_MEMALLOCFLAG_ZERO_ON_ALLOC |
			PVRSRV_MEMALLOCFLAG_GPU_CACHE_INCOHERENT |
			PVRSRV_MEMALLOCFLAG_CPU_CACHE_INCOHERENT |
			PVRSRV_MEMALLOCFLAG_GPU_READABLE |
			PVRSRV_MEMALLOCFLAG_GPU_WRITEABLE |
			PVRSRV_MEMALLOCFLAG_CPU_READABLE |
			PVRSRV_MEMALLOCFLAG_CPU_WRITEABLE |
			PVRSRV_MEMALLOCFLAG_PHYS_HEAP_HINT(FW_MAIN),
		"FwExFabricCoherencyNcTestBuffer",
		&psFabricCohNcTestBufferMemDesc);
	if (eError != PVRSRV_OK) {
		PVR_DPF((PVR_DBG_ERROR,
			 "DevmemFwAllocateExportable() error: %s, exiting",
			 PVRSRVGetErrorString(eError)));
		goto e3;
	}

	eError = DevmemAcquireCpuVirtAddr(
		psFabricCohNcTestBufferMemDesc,
		(void **)&pui32FabricCohNcTestBufferCpuVA);
	if (eError != PVRSRV_OK) {
		PVR_DPF((PVR_DBG_ERROR,
			 "DevmemAcquireCpuVirtAddr() error: %s, exiting",
			 PVRSRVGetErrorString(eError)));
		goto e4;
	}

	eError = RGXSetFirmwareAddress(&sFabricCohNcTestBufferDevVA,
				       psFabricCohNcTestBufferMemDesc, 0,
				       RFW_FWADDR_FLAG_NONE);
	PVR_LOG_GOTO_IF_ERROR(eError, "RGXSetFirmwareAddress:2", e5);

	/* Undo most of the FW mappings done by RGXSetFirmwareAddress */
	sFabricCohNcTestBufferDevVA.ui32Addr &=
		~RGXFW_SEGMMU_DATA_META_CACHE_MASK;
	sFabricCohNcTestBufferDevVA.ui32Addr &=
		~RGXFW_SEGMMU_DATA_VIVT_SLC_CACHE_MASK;
	sFabricCohNcTestBufferDevVA.ui32Addr -= RGXFW_SEGMMU_DATA_BASE_ADDRESS;

	/* Map the buffer in the bootloader segment as uncached */
	sFabricCohNcTestBufferDevVA.ui32Addr |= RGXFW_BOOTLDR_META_ADDR;
	sFabricCohNcTestBufferDevVA.ui32Addr |= RGXFW_SEGMMU_DATA_META_UNCACHED;

	/* Obtain the META segment addresses corresponding to cached and uncached windows into SLC */
	ui64SegOutAddrTopCached = RGXFW_SEGMMU_OUTADDR_TOP_VIVT_SLC_CACHED(
		MMU_CONTEXT_MAPPING_FWIF);
	ui64SegOutAddrTopUncached = RGXFW_SEGMMU_OUTADDR_TOP_VIVT_SLC_UNCACHED(
		MMU_CONTEXT_MAPPING_FWIF);

	/* At the top level, we perform snoop-miss (i.e. to verify slave port) & snoop-hit (i.e. to verify ACE) test.
	   NOTE: For now, skip snoop-miss test as Services currently forces all firmware allocations to be coherent */
	for (eTestType = CPU_WRITE_GPU_READ_SH;
	     eTestType <= GPU_WRITE_CPU_READ_SH && bExit == IMG_FALSE;
	     eTestType++) {
		IMG_CPU_PHYADDR sCpuPhyAddr;
		IMG_BOOL bValid;
		PMR *psPMR;

		if (eTestType == CPU_WRITE_GPU_READ_SM) {
			/* All snoop miss test must bypass the SLC, here memory is region of coherence so
			   configure META to use SLC bypass cache policy for the bootloader segment. Note
			   this cannot be done on a cache-coherent (i.e. CC=1) VA, as this violates ACE
			   standard as one cannot issue a non-coherent request into the bus fabric for
			   an allocation's VA that is cache-coherent in SLC, so use non-coherent buffer */
			RGXWriteMetaRegThroughSP(hPrivate,
						 META_CR_MMCU_SEGMENTn_OUTA1(6),
						 (ui64SegOutAddrTopUncached |
						  RGXFW_BOOTLDR_DEVV_ADDR) >>
							 32);
			pui32FabricCohTestBufferCpuVA =
				pui32FabricCohNcTestBufferCpuVA;
			psFabricCohTestBufferMemDesc =
				psFabricCohNcTestBufferMemDesc;
			psFabricCohTestBufferDevVA =
				&sFabricCohNcTestBufferDevVA;
		} else if (eTestType == CPU_WRITE_GPU_READ_SH) {
			/* All snoop hit test must obviously use SLC, here SLC is region of coherence so
			   configure META not to bypass the SLC for the bootloader segment */
			RGXWriteMetaRegThroughSP(hPrivate,
						 META_CR_MMCU_SEGMENTn_OUTA1(6),
						 (ui64SegOutAddrTopCached |
						  RGXFW_BOOTLDR_DEVV_ADDR) >>
							 32);
			pui32FabricCohTestBufferCpuVA =
				pui32FabricCohCcTestBufferCpuVA;
			psFabricCohTestBufferMemDesc =
				psFabricCohCcTestBufferMemDesc;
			psFabricCohTestBufferDevVA =
				&sFabricCohCcTestBufferDevVA;
		}

		if (eTestType == GPU_WRITE_CPU_READ_SH &&
		    !PVRSRVSystemSnoopingOfDeviceCache(
			    psDevInfo->psDeviceNode->psDevConfig)) {
			/* Cannot perform this test if there is no snooping of device cache */
			continue;
		}

		/* Acquire underlying PMR CpuPA in preparation for cache maintenance */
		(void)DevmemLocalGetImportHandle(psFabricCohTestBufferMemDesc,
						 (void **)&psPMR);
		eError = PMR_CpuPhysAddr(psPMR, OSGetPageShift(), 1, 0,
					 &sCpuPhyAddr, &bValid);
		if (eError != PVRSRV_OK || bValid == IMG_FALSE) {
			PVR_DPF((PVR_DBG_ERROR,
				 "PMR_CpuPhysAddr error: %s, exiting",
				 PVRSRVGetErrorString(eError)));
			bExit = IMG_TRUE;
			continue;
		}

		/* Here we do two passes mostly to account for the effects of using a different
		   seed (i.e. ui32OddEvenSeed) value to read and write */
		for (ui32OddEven = 1; ui32OddEven < 3 && bExit == IMG_FALSE;
		     ui32OddEven++) {
			IMG_UINT32 i;

#if defined(DEBUG)
			switch (eTestType) {
			case CPU_WRITE_GPU_READ_SM:
				PVR_LOG((
					"CPU:Write/GPU:Read Snoop Miss Test: starting [run #%u]",
					ui32OddEven));
				break;
			case GPU_WRITE_CPU_READ_SM:
				PVR_LOG((
					"GPU:Write/CPU:Read Snoop Miss Test: starting [run #%u]",
					ui32OddEven));
				break;
			case CPU_WRITE_GPU_READ_SH:
				PVR_LOG((
					"CPU:Write/GPU:Read Snoop Hit  Test: starting [run #%u]",
					ui32OddEven));
				break;
			case GPU_WRITE_CPU_READ_SH:
				PVR_LOG((
					"GPU:Write/CPU:Read Snoop Hit  Test: starting [run #%u]",
					ui32OddEven));
				break;
			default:
				PVR_LOG(("Internal error, exiting test"));
				eError = PVRSRV_ERROR_INIT_FAILURE;
				bExit = IMG_TRUE;
				continue;
			}
#endif

			/* Do multiple sub-dword cache line tests */
			for (i = 0; i < 2 && bExit == IMG_FALSE; i++) {
				IMG_UINT32 ui32FWAddr;
				IMG_UINT32 ui32FWValue;
				IMG_UINT32 ui32FWValue2;
				IMG_UINT32 ui32LastFWValue = ~0;
				IMG_UINT32 ui32Offset = i * sizeof(IMG_UINT32);

				/* Calculate next address and seed value to write/read from slave-port */
				ui32FWAddr =
					psFabricCohTestBufferDevVA->ui32Addr +
					ui32Offset;
				ui32OddEvenSeed += 1;

				if (eTestType == GPU_WRITE_CPU_READ_SM ||
				    eTestType == GPU_WRITE_CPU_READ_SH) {
					/* Clean dcache to ensure there is no stale data in dcache that might over-write
					   what we are about to write via slave-port here because if it drains from the CPU
					   dcache before we read it, it would corrupt what we are going to read back via
					   the CPU */
					CacheOpValExec(psPMR, 0, ui32Offset,
						       sizeof(IMG_UINT32),
						       PVRSRV_CACHE_OP_CLEAN);

					/* Calculate a new value to write */
					ui32FWValue = i + ui32OddEvenSeed;

					/* Write the value using the RGX slave-port interface */
					eError = RGXWriteFWModuleAddr(
						psDevInfo, ui32FWAddr,
						ui32FWValue);
					if (eError != PVRSRV_OK) {
						PVR_DPF((
							PVR_DBG_ERROR,
							"RGXWriteFWModuleAddr error: %s, exiting",
							PVRSRVGetErrorString(
								eError)));
						bExit = IMG_TRUE;
						continue;
					}

					/* Read back value using RGX slave-port interface, this is used
					   as a sort of memory barrier for the above write */
					eError = RGXReadFWModuleAddr(
						psDevInfo, ui32FWAddr,
						&ui32FWValue2);
					if (eError != PVRSRV_OK) {
						PVR_DPF((
							PVR_DBG_ERROR,
							"RGXReadFWModuleAddr error: %s, exiting",
							PVRSRVGetErrorString(
								eError)));
						bExit = IMG_TRUE;
						continue;
					} else if (ui32FWValue !=
						   ui32FWValue2) {
						//IMG_UINT32 ui32FWValue3;
						//RGXReadFWModuleAddr(psDevInfo, 0xC1F00000, &ui32FWValue3);

						/* Fatal error, we should abort */
						PVR_DPF((
							PVR_DBG_ERROR,
							"At Offset: %d, RAW via SlavePort failed: expected: %x, got: %x",
							i, ui32FWValue,
							ui32FWValue2));
						eError =
							PVRSRV_ERROR_INIT_FAILURE;
						bExit = IMG_TRUE;
						continue;
					}

					if (!PVRSRVSystemSnoopingOfDeviceCache(
						    psDevInfo->psDeviceNode
							    ->psDevConfig)) {
						/* Invalidate dcache to ensure that any prefetched data by the CPU from this memory
						   region is discarded before we read (i.e. next read must trigger a cache miss).
						   If there is snooping of device cache, then any prefetching done by the CPU
						   will reflect the most up to date datum writing by GPU into said location,
						   that is to say prefetching must be coherent so CPU d-flush is not needed */
						CacheOpValExec(
							psPMR, 0, ui32Offset,
							sizeof(IMG_UINT32),
							PVRSRV_CACHE_OP_INVALIDATE);
					}
				} else {
					IMG_UINT32 ui32RAWCpuValue;

					/* Ensures line is in dcache */
					ui32FWValue = IMG_UINT32_MAX;

					/* Dirty allocation in dcache */
					ui32RAWCpuValue = i + ui32OddEvenSeed;
					pui32FabricCohTestBufferCpuVA[i] =
						i + ui32OddEvenSeed;

					/* Flush possible cpu store-buffer(ing) on LMA */
					OSWriteMemoryBarrier(
						&pui32FabricCohTestBufferCpuVA
							[i]);

					if (eTestType ==
					    CPU_WRITE_GPU_READ_SM) {
						/* Flush dcache to force subsequent incoming CPU-bound snoop to miss so
						   memory is coherent before the SlavePort reads */
						CacheOpValExec(
							psPMR, 0, ui32Offset,
							sizeof(IMG_UINT32),
							PVRSRV_CACHE_OP_FLUSH);
					}

					/* Read back value using RGX slave-port interface */
					eError = RGXReadFWModuleAddr(
						psDevInfo, ui32FWAddr,
						&ui32FWValue);
					if (eError != PVRSRV_OK) {
						PVR_DPF((
							PVR_DBG_ERROR,
							"RGXReadFWModuleAddr error: %s, exiting",
							PVRSRVGetErrorString(
								eError)));
						bExit = IMG_TRUE;
						continue;
					}

					/* Being mostly paranoid here, verify that CPU RAW operation is valid
					   after the above slave port read */
					CacheOpValExec(
						psPMR, 0, ui32Offset,
						sizeof(IMG_UINT32),
						PVRSRV_CACHE_OP_INVALIDATE);
					if (pui32FabricCohTestBufferCpuVA[i] !=
					    ui32RAWCpuValue) {
						/* Fatal error, we should abort */
						PVR_DPF((
							PVR_DBG_ERROR,
							"At Offset: %d, RAW by CPU failed: expected: %x, got: %x",
							i, ui32RAWCpuValue,
							pui32FabricCohTestBufferCpuVA
								[i]));
						eError =
							PVRSRV_ERROR_INIT_FAILURE;
						bExit = IMG_TRUE;
						continue;
					}
				}

				/* Compare to see if sub-test passed */
				if (pui32FabricCohTestBufferCpuVA[i] ==
				    ui32FWValue) {
#if defined(DEBUG)
					bSubTestPassed = IMG_TRUE;
#endif
				} else {
					bFullTestPassed = IMG_FALSE;
					eError = PVRSRV_ERROR_INIT_FAILURE;
#if defined(DEBUG)
					bSubTestPassed = IMG_FALSE;
#endif
					if (ui32LastFWValue != ui32FWValue) {
#if defined(DEBUG)
						PVR_LOG((
							"At Offset: %d, Expected: %x, Got: %x",
							i,
							(eTestType & 0x1) ?
								ui32FWValue :
								pui32FabricCohTestBufferCpuVA
									[i],
							(eTestType & 0x1) ?
								pui32FabricCohTestBufferCpuVA
									[i] :
								ui32FWValue));
#endif
					} else {
						PVR_DPF((
							PVR_DBG_ERROR,
							"test encountered unexpected error, exiting"));
						eError =
							PVRSRV_ERROR_INIT_FAILURE;
						bExit = IMG_TRUE;
						continue;
					}
				}

				ui32LastFWValue =
					(eTestType & 0x1) ?
						ui32FWValue :
						pui32FabricCohTestBufferCpuVA[i];
			}

#if defined(DEBUG)
			if (bExit) {
				continue;
			}

			switch (eTestType) {
			case CPU_WRITE_GPU_READ_SM:
				PVR_LOG((
					"CPU:Write/GPU:Read Snoop Miss Test: completed [run #%u]: %s",
					ui32OddEven,
					bSubTestPassed ? "PASSED" : "FAILED"));
				break;
			case GPU_WRITE_CPU_READ_SM:
				PVR_LOG((
					"GPU:Write/CPU:Read Snoop Miss Test: completed [run #%u]: %s",
					ui32OddEven,
					bSubTestPassed ? "PASSED" : "FAILED"));
				break;
			case CPU_WRITE_GPU_READ_SH:
				PVR_LOG((
					"CPU:Write/GPU:Read Snoop Hit Test: completed [run #%u]: %s",
					ui32OddEven,
					bSubTestPassed ? "PASSED" : "FAILED"));
				break;
			case GPU_WRITE_CPU_READ_SH:
				PVR_LOG((
					"GPU:Write/CPU:Read Snoop Hit Test: completed [run #%u]: %s",
					ui32OddEven,
					bSubTestPassed ? "PASSED" : "FAILED"));
				break;
			default:
				PVR_LOG(("Internal error, exiting test"));
				eError = PVRSRV_ERROR_INIT_FAILURE;
				bExit = IMG_TRUE;
				continue;
			}
#endif
		}
	}

	/* Release and free NC/CC test buffers */
	RGXUnsetFirmwareAddress(psFabricCohCcTestBufferMemDesc);
e5:
	DevmemReleaseCpuVirtAddr(psFabricCohCcTestBufferMemDesc);
e4:
	DevmemFwUnmapAndFree(psDevInfo, psFabricCohCcTestBufferMemDesc);

e3:
	RGXUnsetFirmwareAddress(psFabricCohNcTestBufferMemDesc);
e2:
	DevmemReleaseCpuVirtAddr(psFabricCohNcTestBufferMemDesc);
e1:
	DevmemFwUnmapAndFree(psDevInfo, psFabricCohNcTestBufferMemDesc);

e0:
	/* Restore bootloader segment settings */
	RGXWriteMetaRegThroughSP(
		hPrivate, META_CR_MMCU_SEGMENTn_OUTA1(6),
		(ui64SegOutAddrTopCached | RGXFW_BOOTLDR_DEVV_ADDR) >> 32);

	bFullTestPassed = bExit ? IMG_FALSE : bFullTestPassed;
	if (bFullTestPassed) {
		PVR_LOG(("fabric coherency test: PASSED"));
		psDevInfo->ui32CoherencyTestsDone = MAX_NUM_COHERENCY_TESTS + 1;
	} else {
		PVR_LOG(("fabric coherency test: FAILED"));
		psDevInfo->ui32CoherencyTestsDone++;
	}

	return eError;
}

#if defined(SUPPORT_VALIDATION)

#if !defined(RGX_CR_FIRMWARE_PROCESSOR_LS)
#define RGX_CR_FIRMWARE_PROCESSOR_LS (0x01A0U)
#define RGX_CR_FIRMWARE_PROCESSOR_LS_ENABLE_EN (0x00000001U)
#endif

#if !defined(RGX_CR_POWER_EVENT)
#define RGX_CR_POWER_EVENT (0x0038U)
#define RGX_CR_POWER_EVENT_GPU_ID_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFF1F))
#define RGX_CR_POWER_EVENT_DOMAIN_SPU0_SHIFT (9U)
#define RGX_CR_POWER_EVENT_DOMAIN_CLUSTER0_SHIFT (8U)
#define RGX_CR_POWER_EVENT_DOMAIN_CLUSTER_CLUSTER0_SHIFT (32U)
#define RGX_CR_POWER_EVENT_TYPE_SHIFT (0U)
#define RGX_CR_POWER_EVENT_TYPE_POWER_DOWN (0x00000000U)
#define RGX_CR_POWER_EVENT_REQ_EN (0x00000002U)
#define RGX_CR_POWER_EVENT2 (0x0060U)
#endif

#if !defined(RGX_CR_DCE_ENABLE)
#define RGX_CR_DCE_ENABLE (0xF020U)
#endif

/*!
 *******************************************************************************

 @Function		RGXStartValidation

 @Description	Called from RGXStart for validation builds
 ******************************************************************************/
PVRSRV_ERROR RGXStartValidation(const void *hPrivate)
{
	PVRSRV_ERROR eError = PVRSRV_OK;
	RGX_LAYER_PARAMS *psParams = (RGX_LAYER_PARAMS *)hPrivate;
	PVRSRV_RGXDEV_INFO *psDevInfo = psParams->psDevInfo;

	if (psDevInfo->ui32ValidationFlags & RGX_VAL_LS_EN) {
		/* Set the dual LS mode */
		RGXWriteReg32(hPrivate, RGX_CR_FIRMWARE_PROCESSOR_LS,
			      RGX_CR_FIRMWARE_PROCESSOR_LS_ENABLE_EN);
		(void)RGXReadReg32(hPrivate, RGX_CR_FIRMWARE_PROCESSOR_LS);
	}

	return eError;
}

/*!
 *******************************************************************************

 @Function		RGXStopValidation

 @Description	Called from RGXStop for validation builds
 ******************************************************************************/
PVRSRV_ERROR RGXStopValidation(const void *hPrivate)
{
	PVRSRV_ERROR eError = PVRSRV_OK;
#if defined(SUPPORT_VALIDATION) && !defined(TC_MEMORY_CONFIG)
	RGX_LAYER_PARAMS *psParams = (RGX_LAYER_PARAMS *)hPrivate;
	PVRSRV_RGXDEV_INFO *psDevInfo = psParams->psDevInfo;

	if ((RGX_DEVICE_GET_FEATURE_VALUE(hPrivate, POWER_ISLAND_VERSION) >=
	     4) &&
	    RGX_DEVICE_HAS_FEATURE(hPrivate, RISCV_FW_PROCESSOR)) {
		/* Wait for SLC to signal IDLE */
		eError = RGXPollReg32(
			hPrivate, RGX_CR_SLC_IDLE,
			RGX_CR_SLC_IDLE_MASKFULL ^ (CR_IDLE_UNSELECTED_MASK),
			RGX_CR_SLC_IDLE_MASKFULL ^ (CR_IDLE_UNSELECTED_MASK));
		if (eError != PVRSRV_OK)
			return eError;
	}

	/* Power off any enabled SPUs */
	if (BITMASK_HAS(
		    psDevInfo->ui32DeviceFlags,
		    RGXKM_DEVICE_STATE_ENABLE_SPU_UNITS_POWER_MASK_CHANGE_EN)) {
		if (RGX_DEVICE_GET_FEATURE_VALUE(hPrivate,
						 POWER_ISLAND_VERSION) >= 3) {
			IMG_UINT64 ui64GPU_MASK_CLRMSK =
				(IMG_UINT64_C(0xFFFFFFFFFFFF00FF));
			IMG_UINT64 ui64PowUnitOffMask;
			IMG_UINT64 ui64RegVal;

			ui64PowUnitOffMask = (1 << RGX_DEVICE_GET_FEATURE_VALUE(
						      hPrivate, NUM_CLUSTERS)) -
					     1;
			ui64RegVal =
				(~ui64GPU_MASK_CLRMSK) | // GPU_MASK specifies all cores
				(~RGX_CR_POWER_EVENT_GPU_ID_CLRMSK) | // GPU_ID all set means use the GPU_MASK
				(ui64PowUnitOffMask
				 << RGX_CR_POWER_EVENT_DOMAIN_CLUSTER_CLUSTER0_SHIFT) |
				RGX_CR_POWER_EVENT_TYPE_POWER_DOWN;

			RGXWriteReg64(hPrivate, RGX_CR_POWER_EVENT, ui64RegVal);

			RGXWriteReg64(hPrivate, RGX_CR_POWER_EVENT,
				      ui64RegVal | RGX_CR_POWER_EVENT_REQ_EN);
		} else if (RGX_DEVICE_GET_FEATURE_VALUE(
				   hPrivate, POWER_ISLAND_VERSION) == 2) {
			IMG_UINT64 ui64GPU_MASK_CLRMSK =
				(IMG_UINT64_C(0x00FFFFFFFFFFFFFF));
			IMG_UINT64 ui64PowUnitOffMask;
			IMG_UINT64 ui64RegVal;

			ui64PowUnitOffMask = (1 << RGX_DEVICE_GET_FEATURE_VALUE(
						      hPrivate, NUM_CLUSTERS)) -
					     1;
			ui64RegVal =
				(~ui64GPU_MASK_CLRMSK) | // GPU_MASK specifies all cores
				(~RGX_CR_POWER_EVENT_GPU_ID_CLRMSK) | // GPU_ID all set means use the GPU_MASK
				(ui64PowUnitOffMask
				 << RGX_CR_POWER_EVENT_DOMAIN_CLUSTER0_SHIFT) |
				RGX_CR_POWER_EVENT_TYPE_POWER_DOWN;

			if (RGX_DEVICE_HAS_FEATURE_VALUE(hPrivate,
							 RAY_TRACING_ARCH) &&
			    RGX_DEVICE_GET_FEATURE_VALUE(
				    hPrivate, RAY_TRACING_ARCH) > 2) {
				RGXWriteReg64(hPrivate, RGX_CR_POWER_EVENT2, 0);
			}

			RGXWriteReg64(hPrivate, RGX_CR_POWER_EVENT, ui64RegVal);

			RGXWriteReg64(hPrivate, RGX_CR_POWER_EVENT,
				      ui64RegVal | RGX_CR_POWER_EVENT_REQ_EN);
		} else {
			IMG_UINT32 ui32PowUnitOffMask;
			IMG_UINT32 ui32RegVal;

			ui32PowUnitOffMask = (1 << RGX_DEVICE_GET_FEATURE_VALUE(
						      hPrivate, NUM_SPU)) -
					     1;
			ui32RegVal = (ui32PowUnitOffMask
				      << RGX_CR_POWER_EVENT_DOMAIN_SPU0_SHIFT) |
				     RGX_CR_POWER_EVENT_TYPE_POWER_DOWN;

			RGXWriteReg32(hPrivate, RGX_CR_POWER_EVENT, ui32RegVal);

			RGXWriteReg32(hPrivate, RGX_CR_POWER_EVENT,
				      ui32RegVal | RGX_CR_POWER_EVENT_REQ_EN);
		}

		/* Poll on complete */
		eError = RGXPollReg32(hPrivate, RGX_CR_EVENT_STATUS,
				      RGX_CR_EVENT_STATUS_POWER_COMPLETE_EN,
				      RGX_CR_EVENT_STATUS_POWER_COMPLETE_EN);
		if (eError != PVRSRV_OK)
			return eError;

		/* Update the SPU_ENABLE mask */
		if (RGX_DEVICE_GET_FEATURE_VALUE(hPrivate,
						 POWER_ISLAND_VERSION) == 1) {
			RGXWriteReg32(hPrivate, RGX_CR_SPU_ENABLE, 0);
		}
		RGXWriteReg32(hPrivate, RGX_CR_DCE_ENABLE, 0);
	}
#else
	PVR_UNREFERENCED_PARAMETER(hPrivate);
#endif
	return eError;
}
#endif /* defined(SUPPORT_VALIDATION) */
