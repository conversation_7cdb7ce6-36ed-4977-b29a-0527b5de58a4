/*************************************************************************/ /*!
@Copyright      Copyright (c) Imagination Technologies Ltd. All Rights Reserved
@License        Dual MIT/GPLv2

The contents of this file are subject to the MIT license as set out below.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

Alternatively, the contents of this file may be used under the terms of
the GNU General Public License Version 2 ("GPL") in which case the provisions
of GPL are applicable instead of those above.

If you wish to allow use of your version of this file only under the terms of
GPL, and not to allow others to use your version of this file under the terms
of the MIT license, indicate your decision by deleting the provisions above
and replace them with the notice and other provisions required by GPL as set
out in the file called "GPL-COPYING" included in this distribution. If you do
not delete the provisions above, a recipient may use your version of this file
under the terms of either the MIT license or GPL.

This License is also included in this distribution in the file called
"MIT-COPYING".

EXCEPT AS OTHERWISE STATED IN A NEGOTIATED AGREEMENT: (A) THE SOFTWARE IS
PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
PURPOSE AND NONINFRINGEMENT; AND (B) IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ /**************************************************************************/

/* Autogenerated - don't edit. Generated from aon_regs.def. Regconv 0.2_r110 */

#ifndef _PLATO_AON_REGS_H_
#define _PLATO_AON_REGS_H_

/*
	Register CR_BM_STATUS
*/
#define PLATO_AON_CR_BM_STATUS 0x0000
#define PLATO_CR_GPIO_DEBUG_MASK 0x00000020U
#define PLATO_CR_GPIO_DEBUG_SHIFT 5
#define PLATO_CR_GPIO_DEBUG_SIGNED 0

#define PLATO_CR_SAFE_MODE_MASK 0x00000010U
#define PLATO_CR_SAFE_MODE_SHIFT 4
#define PLATO_CR_SAFE_MODE_SIGNED 0

#define PLATO_CR_BM_DIS_MASK 0x00000008U
#define PLATO_CR_BM_DIS_SHIFT 3
#define PLATO_CR_BM_DIS_SIGNED 0

#define PLATO_CR_BM_DR_MASK 0x00000004U
#define PLATO_CR_BM_DR_SHIFT 2
#define PLATO_CR_BM_DR_SIGNED 0

#define PLATO_CR_BM_CPU_MASK 0x00000002U
#define PLATO_CR_BM_CPU_SHIFT 1
#define PLATO_CR_BM_CPU_SIGNED 0

#define PLATO_CR_BM_PCI_MASK 0x00000001U
#define PLATO_CR_BM_PCI_SHIFT 0
#define PLATO_CR_BM_PCI_SIGNED 0

/*
	Register CR_PD_CORE_CTRL
*/
#define PLATO_AON_CR_PD_CORE_CTRL 0x0004
#define PLATO_CR_PD_CORE_ISO_DLY_MASK 0x0000F000U
#define PLATO_CR_PD_CORE_ISO_DLY_SHIFT 12
#define PLATO_CR_PD_CORE_ISO_DLY_SIGNED 0

#define PLATO_CR_PD_CORE_CKE_DLY_MASK 0x00000F00U
#define PLATO_CR_PD_CORE_CKE_DLY_SHIFT 8
#define PLATO_CR_PD_CORE_CKE_DLY_SIGNED 0

#define PLATO_CR_PD_CORE_RST_DLY_MASK 0x000000F0U
#define PLATO_CR_PD_CORE_RST_DLY_SHIFT 4
#define PLATO_CR_PD_CORE_RST_DLY_SIGNED 0

#define PLATO_CR_PD_CORE_POWER_DLY_MASK 0x0000000FU
#define PLATO_CR_PD_CORE_POWER_DLY_SHIFT 0
#define PLATO_CR_PD_CORE_POWER_DLY_SIGNED 0

/*
	Register CR_ISO_CTRL
*/
#define PLATO_AON_CR_ISO_CTRL 0x0008
#define PLATO_CR_DDR_RET_EN_MASK 0x00000100U
#define PLATO_CR_DDR_RET_EN_SHIFT 8
#define PLATO_CR_DDR_RET_EN_SIGNED 0

#define PLATO_CR_GPU_CLK_E_MASK 0x00000010U
#define PLATO_CR_GPU_CLK_E_SHIFT 4
#define PLATO_CR_GPU_CLK_E_SIGNED 0

#define PLATO_CR_HDMI_PHY_ISO_E_MASK 0x00000008U
#define PLATO_CR_HDMI_PHY_ISO_E_SHIFT 3
#define PLATO_CR_HDMI_PHY_ISO_E_SIGNED 0

#define PLATO_CR_DDR_B_PHY_ISO_E_MASK 0x00000004U
#define PLATO_CR_DDR_B_PHY_ISO_E_SHIFT 2
#define PLATO_CR_DDR_B_PHY_ISO_E_SIGNED 0

#define PLATO_CR_USB_PHY_ISO_E_MASK 0x00000002U
#define PLATO_CR_USB_PHY_ISO_E_SHIFT 1
#define PLATO_CR_USB_PHY_ISO_E_SIGNED 0

#define PLATO_CR_GPU_ISO_E_MASK 0x00000001U
#define PLATO_CR_GPU_ISO_E_SHIFT 0
#define PLATO_CR_GPU_ISO_E_SIGNED 0

/*
	Register CR_RESET_CTRL
*/
#define PLATO_AON_CR_RESET_CTRL 0x000C
#define PLATO_CR_EFUSE_RESET_N_MASK 0x00200000U
#define PLATO_CR_EFUSE_RESET_N_SHIFT 21
#define PLATO_CR_EFUSE_RESET_N_SIGNED 0

#define PLATO_CR_VOLT_RESET_N_MASK 0x00100000U
#define PLATO_CR_VOLT_RESET_N_SHIFT 20
#define PLATO_CR_VOLT_RESET_N_SIGNED 0

#define PLATO_CR_TEMP_RESET_N_MASK 0x00080000U
#define PLATO_CR_TEMP_RESET_N_SHIFT 19
#define PLATO_CR_TEMP_RESET_N_SIGNED 0

#define PLATO_CR_SPI_RESET_N_MASK 0x00040000U
#define PLATO_CR_SPI_RESET_N_SHIFT 18
#define PLATO_CR_SPI_RESET_N_SIGNED 0

#define PLATO_CR_I2C_RESET_N_MASK 0x00020000U
#define PLATO_CR_I2C_RESET_N_SHIFT 17
#define PLATO_CR_I2C_RESET_N_SIGNED 0

#define PLATO_CR_UART_RESET_N_MASK 0x00010000U
#define PLATO_CR_UART_RESET_N_SHIFT 16
#define PLATO_CR_UART_RESET_N_SIGNED 0

#define PLATO_CR_DDR_B_CTRL_RESET_N_MASK 0x00000800U
#define PLATO_CR_DDR_B_CTRL_RESET_N_SHIFT 11
#define PLATO_CR_DDR_B_CTRL_RESET_N_SIGNED 0

#define PLATO_CR_DDR_B_DATA_RESET_N_MASK 0x00000400U
#define PLATO_CR_DDR_B_DATA_RESET_N_SHIFT 10
#define PLATO_CR_DDR_B_DATA_RESET_N_SIGNED 0

#define PLATO_CR_DDR_A_CTRL_RESET_N_MASK 0x00000200U
#define PLATO_CR_DDR_A_CTRL_RESET_N_SHIFT 9
#define PLATO_CR_DDR_A_CTRL_RESET_N_SIGNED 0

#define PLATO_CR_DDR_A_DATA_RESET_N_MASK 0x00000100U
#define PLATO_CR_DDR_A_DATA_RESET_N_SHIFT 8
#define PLATO_CR_DDR_A_DATA_RESET_N_SIGNED 0

#define PLATO_CR_SOFT_RESET_REQ_NOCPU_MASK 0x00000040U
#define PLATO_CR_SOFT_RESET_REQ_NOCPU_SHIFT 6
#define PLATO_CR_SOFT_RESET_REQ_NOCPU_SIGNED 0

#define PLATO_CR_DISPLAY_RESET_MASK 0x00000020U
#define PLATO_CR_DISPLAY_RESET_SHIFT 5
#define PLATO_CR_DISPLAY_RESET_SIGNED 0

#define PLATO_CR_USB_PHY_RESET_MASK 0x00000010U
#define PLATO_CR_USB_PHY_RESET_SHIFT 4
#define PLATO_CR_USB_PHY_RESET_SIGNED 0

#define PLATO_CR_USB_PMU_RESET_N_MASK 0x00000008U
#define PLATO_CR_USB_PMU_RESET_N_SHIFT 3
#define PLATO_CR_USB_PMU_RESET_N_SIGNED 0

#define PLATO_CR_USB_CTRL_RESET_N_MASK 0x00000004U
#define PLATO_CR_USB_CTRL_RESET_N_SHIFT 2
#define PLATO_CR_USB_CTRL_RESET_N_SIGNED 0

#define PLATO_CR_GPU_RESET_N_MASK 0x00000002U
#define PLATO_CR_GPU_RESET_N_SHIFT 1
#define PLATO_CR_GPU_RESET_N_SIGNED 0

#define PLATO_CR_SOFT_RESET_REQ_MASK 0x00000001U
#define PLATO_CR_SOFT_RESET_REQ_SHIFT 0
#define PLATO_CR_SOFT_RESET_REQ_SIGNED 0

/*
	Register CR_OVERRIDE_RESET_CTRL
*/
#define PLATO_AON_CR_OVERRIDE_RESET_CTRL 0x0010
#define PLATO_CR_CPU_NOC_RESET_N_MASK 0x00001000U
#define PLATO_CR_CPU_NOC_RESET_N_SHIFT 12
#define PLATO_CR_CPU_NOC_RESET_N_SIGNED 0

#define PLATO_CR_CPU_NOC_RESET_SEL_MASK 0x00000800U
#define PLATO_CR_CPU_NOC_RESET_SEL_SHIFT 11
#define PLATO_CR_CPU_NOC_RESET_SEL_SIGNED 0

#define PLATO_CR_CPU_CLK_EN_MASK 0x00000400U
#define PLATO_CR_CPU_CLK_EN_SHIFT 10
#define PLATO_CR_CPU_CLK_EN_SIGNED 0

#define PLATO_CR_CPU_CLK_EN_SEL_MASK 0x00000200U
#define PLATO_CR_CPU_CLK_EN_SEL_SHIFT 9
#define PLATO_CR_CPU_CLK_EN_SEL_SIGNED 0

#define PLATO_CR_WDT_RESET_N_MASK 0x00000100U
#define PLATO_CR_WDT_RESET_N_SHIFT 8
#define PLATO_CR_WDT_RESET_N_SIGNED 0

#define PLATO_CR_WDT_RESET_SEL_MASK 0x00000080U
#define PLATO_CR_WDT_RESET_SEL_SHIFT 7
#define PLATO_CR_WDT_RESET_SEL_SIGNED 0

#define PLATO_CR_PCI_PHY_RESET_N_MASK 0x00000040U
#define PLATO_CR_PCI_PHY_RESET_N_SHIFT 6
#define PLATO_CR_PCI_PHY_RESET_N_SIGNED 0

#define PLATO_CR_PCI_CTRL_RESET_N_MASK 0x00000020U
#define PLATO_CR_PCI_CTRL_RESET_N_SHIFT 5
#define PLATO_CR_PCI_CTRL_RESET_N_SIGNED 0

#define PLATO_CR_PCI_RESET_SEL_MASK 0x00000010U
#define PLATO_CR_PCI_RESET_SEL_SHIFT 4
#define PLATO_CR_PCI_RESET_SEL_SIGNED 0

#define PLATO_CR_REG_RESET_N_MASK 0x00000008U
#define PLATO_CR_REG_RESET_N_SHIFT 3
#define PLATO_CR_REG_RESET_N_SIGNED 0

#define PLATO_CR_REG_RESET_SEL_MASK 0x00000004U
#define PLATO_CR_REG_RESET_SEL_SHIFT 2
#define PLATO_CR_REG_RESET_SEL_SIGNED 0

#define PLATO_CR_CPU_RESET_N_MASK 0x00000002U
#define PLATO_CR_CPU_RESET_N_SHIFT 1
#define PLATO_CR_CPU_RESET_N_SIGNED 0

#define PLATO_CR_CPU_RESET_SEL_MASK 0x00000001U
#define PLATO_CR_CPU_RESET_SEL_SHIFT 0
#define PLATO_CR_CPU_RESET_SEL_SIGNED 0

/*
	Register CR_USB_PMU_CTRL
*/
#define PLATO_AON_CR_USB_PMU_CTRL 0x0020
#define PLATO_CR_USB_PM_POWER_STATE_REQUEST_MASK 0x00000003U
#define PLATO_CR_USB_PM_POWER_STATE_REQUEST_SHIFT 0
#define PLATO_CR_USB_PM_POWER_STATE_REQUEST_SIGNED 0

/*
	Register CR_USB_PMU_STATUS
*/
#define PLATO_AON_CR_USB_PMU_STATUS 0x0024
#define PLATO_CR_PME_GENERATION_U3PMU_MASK 0x00000080U
#define PLATO_CR_PME_GENERATION_U3PMU_SHIFT 7
#define PLATO_CR_PME_GENERATION_U3PMU_SIGNED 0

#define PLATO_CR_PME_GENERATION_U2PMU_MASK 0x00000040U
#define PLATO_CR_PME_GENERATION_U2PMU_SHIFT 6
#define PLATO_CR_PME_GENERATION_U2PMU_SIGNED 0

#define PLATO_CR_CONNECT_STATE_U3PMU_MASK 0x00000020U
#define PLATO_CR_CONNECT_STATE_U3PMU_SHIFT 5
#define PLATO_CR_CONNECT_STATE_U3PMU_SIGNED 0

#define PLATO_CR_CONNECT_STATE_U2PMU_MASK 0x00000010U
#define PLATO_CR_CONNECT_STATE_U2PMU_SHIFT 4
#define PLATO_CR_CONNECT_STATE_U2PMU_SIGNED 0

#define PLATO_CR_CURRENT_POWER_STATE_U3PMU_MASK 0x0000000CU
#define PLATO_CR_CURRENT_POWER_STATE_U3PMU_SHIFT 2
#define PLATO_CR_CURRENT_POWER_STATE_U3PMU_SIGNED 0

#define PLATO_CR_CURRENT_POWER_STATE_U2PMU_MASK 0x00000003U
#define PLATO_CR_CURRENT_POWER_STATE_U2PMU_SHIFT 0
#define PLATO_CR_CURRENT_POWER_STATE_U2PMU_SIGNED 0

/*
	Register CR_USB_SUSPEND_CLK_CTRL
*/
#define PLATO_AON_CR_USB_SUSPEND_CLK_CTRL 0x0028
#define PLATO_CR_USB_SUSP_CLK_EN_MASK 0x00001000U
#define PLATO_CR_USB_SUSP_CLK_EN_SHIFT 12
#define PLATO_CR_USB_SUSP_CLK_EN_SIGNED 0

#define PLATO_CR_USB_SUSP_CLK_DIV_MASK 0x000003FFU
#define PLATO_CR_USB_SUSP_CLK_DIV_SHIFT 0
#define PLATO_CR_USB_SUSP_CLK_DIV_SIGNED 0

/*
	Register CR_USBPHY_CLK_CFG
*/
#define PLATO_AON_CR_USBPHY_CLK_CFG 0x002C
#define PLATO_CR_USBP_SSC_RANGE_MASK 0x0000001CU
#define PLATO_CR_USBP_SSC_RANGE_SHIFT 2
#define PLATO_CR_USBP_SSC_RANGE_SIGNED 0

#define PLATO_CR_USBP_RETENABLEN_MASK 0x00000002U
#define PLATO_CR_USBP_RETENABLEN_SHIFT 1
#define PLATO_CR_USBP_RETENABLEN_SIGNED 0

#define PLATO_CR_USBP_REF_SSP_EN_MASK 0x00000001U
#define PLATO_CR_USBP_REF_SSP_EN_SHIFT 0
#define PLATO_CR_USBP_REF_SSP_EN_SIGNED 0

/*
	Register CR_USBPHY_DTCT_ADJ
*/
#define PLATO_AON_CR_USBPHY_DTCT_ADJ 0x0030
#define PLATO_CR_USBP_LOS_BIAS_MASK 0x00380000U
#define PLATO_CR_USBP_LOS_BIAS_SHIFT 19
#define PLATO_CR_USBP_LOS_BIAS_SIGNED 0

#define PLATO_CR_USBP_LOS_MASK_VAL_MASK 0x0007FE00U
#define PLATO_CR_USBP_LOS_MASK_VAL_SHIFT 9
#define PLATO_CR_USBP_LOS_MASK_VAL_SIGNED 0

#define PLATO_CR_USBP_OTGTUNE_MASK 0x000001C0U
#define PLATO_CR_USBP_OTGTUNE_SHIFT 6
#define PLATO_CR_USBP_OTGTUNE_SIGNED 0

#define PLATO_CR_USBP_COMPDISTUNE_MASK 0x00000038U
#define PLATO_CR_USBP_COMPDISTUNE_SHIFT 3
#define PLATO_CR_USBP_COMPDISTUNE_SIGNED 0

#define PLATO_CR_USBP_SQRXTUNE_MASK 0x00000007U
#define PLATO_CR_USBP_SQRXTUNE_SHIFT 0
#define PLATO_CR_USBP_SQRXTUNE_SIGNED 0

/*
	Register CR_USBPHY_CUR_ADJ
*/
#define PLATO_AON_CR_USBPHY_CUR_ADJ 0x0034
#define PLATO_CR_USBP_TXPREEMPPULSETUNE_MASK 0x00000100U
#define PLATO_CR_USBP_TXPREEMPPULSETUNE_SHIFT 8
#define PLATO_CR_USBP_TXPREEMPPULSETUNE_SIGNED 0

#define PLATO_CR_USBP_TXPREEMPAMPTUNE_MASK 0x000000C0U
#define PLATO_CR_USBP_TXPREEMPAMPTUNE_SHIFT 6
#define PLATO_CR_USBP_TXPREEMPAMPTUNE_SIGNED 0

#define PLATO_CR_USBP_TXFSLSTUNE_MASK 0x0000003CU
#define PLATO_CR_USBP_TXFSLSTUNE_SHIFT 2
#define PLATO_CR_USBP_TXFSLSTUNE_SIGNED 0

#define PLATO_CR_USBP_TXRESTUNE_MASK 0x00000003U
#define PLATO_CR_USBP_TXRESTUNE_SHIFT 0
#define PLATO_CR_USBP_TXRESTUNE_SIGNED 0

/*
	Register CR_USBPHY_VADJ
*/
#define PLATO_AON_CR_USBPHY_VADJ 0x0038
#define PLATO_CR_USBP_TXVBOOST_LVL_MASK 0x38000000U
#define PLATO_CR_USBP_TXVBOOST_LVL_SHIFT 27
#define PLATO_CR_USBP_TXVBOOST_LVL_SIGNED 0

#define PLATO_CR_USBP_TXSWING_FULL_MASK 0x07F00000U
#define PLATO_CR_USBP_TXSWING_FULL_SHIFT 20
#define PLATO_CR_USBP_TXSWING_FULL_SIGNED 0

#define PLATO_CR_USBP_TXDEEMPH_6DB_MASK 0x000FC000U
#define PLATO_CR_USBP_TXDEEMPH_6DB_SHIFT 14
#define PLATO_CR_USBP_TXDEEMPH_6DB_SIGNED 0

#define PLATO_CR_USBP_TXDEEMPH_3P5DB_MASK 0x00003F00U
#define PLATO_CR_USBP_TXDEEMPH_3P5DB_SHIFT 8
#define PLATO_CR_USBP_TXDEEMPH_3P5DB_SIGNED 0

#define PLATO_CR_USBP_TXRISETUNE_MASK 0x000000C0U
#define PLATO_CR_USBP_TXRISETUNE_SHIFT 6
#define PLATO_CR_USBP_TXRISETUNE_SIGNED 0

#define PLATO_CR_USBP_TXVREFTUNE_MASK 0x0000003CU
#define PLATO_CR_USBP_TXVREFTUNE_SHIFT 2
#define PLATO_CR_USBP_TXVREFTUNE_SIGNED 0

#define PLATO_CR_USBP_TXHSXVTUNE_MASK 0x00000003U
#define PLATO_CR_USBP_TXHSXVTUNE_SHIFT 0
#define PLATO_CR_USBP_TXHSXVTUNE_SIGNED 0

/*
	Register CR_OVERRIDE_GPIO_DEBUG
*/
#define PLATO_AON_CR_OVERRIDE_GPIO_DEBUG 0x0040
#define PLATO_CR_GPIO_DEBUG_SEL_MASK 0x00000002U
#define PLATO_CR_GPIO_DEBUG_SEL_SHIFT 1
#define PLATO_CR_GPIO_DEBUG_SEL_SIGNED 0

#define PLATO_CR_GPIO_DEBUG_SW_MASK 0x00000001U
#define PLATO_CR_GPIO_DEBUG_SW_SHIFT 0
#define PLATO_CR_GPIO_DEBUG_SW_SIGNED 0

/*
	Register CR_GPU_GPIO_SEL
*/
#define PLATO_AON_CR_GPU_GPIO_SEL 0x0044
#define PLATO_CR_GPU_GPIO_SEL_MASK 0x00000001U
#define PLATO_CR_GPU_GPIO_SEL_SHIFT 0
#define PLATO_CR_GPU_GPIO_SEL_SIGNED 0

/*
	Register CR_GPU_PLL_CTRL_0
*/
#define PLATO_AON_CR_GPU_PLL_CTRL_0 0x0060
#define PLATO_CR_GPU_PLL_PD_MASK 0x10000000U
#define PLATO_CR_GPU_PLL_PD_SHIFT 28
#define PLATO_CR_GPU_PLL_PD_SIGNED 0

#define PLATO_CR_GPU_PLL_POSTDIV2_MASK 0x07000000U
#define PLATO_CR_GPU_PLL_POSTDIV2_SHIFT 24
#define PLATO_CR_GPU_PLL_POSTDIV2_SIGNED 0

#define PLATO_CR_GPU_PLL_POSTDIV1_MASK 0x00700000U
#define PLATO_CR_GPU_PLL_POSTDIV1_SHIFT 20
#define PLATO_CR_GPU_PLL_POSTDIV1_SIGNED 0

#define PLATO_CR_GPU_PLL_REFDIV_MASK 0x0003F000U
#define PLATO_CR_GPU_PLL_REFDIV_SHIFT 12
#define PLATO_CR_GPU_PLL_REFDIV_SIGNED 0

#define PLATO_CR_GPU_PLL_FBDIV_MASK 0x00000FFFU
#define PLATO_CR_GPU_PLL_FBDIV_SHIFT 0
#define PLATO_CR_GPU_PLL_FBDIV_SIGNED 0

/*
	Register CR_GPU_PLL_CTRL_1
*/
#define PLATO_AON_CR_GPU_PLL_CTRL_1 0x0064
#define PLATO_CR_GPU_PLL_DSMPD_MASK 0x01000000U
#define PLATO_CR_GPU_PLL_DSMPD_SHIFT 24
#define PLATO_CR_GPU_PLL_DSMPD_SIGNED 0

#define PLATO_CR_GPU_PLL_FRAC_MASK 0x00FFFFFFU
#define PLATO_CR_GPU_PLL_FRAC_SHIFT 0
#define PLATO_CR_GPU_PLL_FRAC_SIGNED 0

/*
	Register CR_DDR_PLL_CTRL_0
*/
#define PLATO_AON_CR_DDR_PLL_CTRL_0 0x0068
#define PLATO_CR_DDR_PLL_PD_MASK 0x10000000U
#define PLATO_CR_DDR_PLL_PD_SHIFT 28
#define PLATO_CR_DDR_PLL_PD_SIGNED 0

#define PLATO_CR_DDR_PLL_POSTDIV2_MASK 0x07000000U
#define PLATO_CR_DDR_PLL_POSTDIV2_SHIFT 24
#define PLATO_CR_DDR_PLL_POSTDIV2_SIGNED 0

#define PLATO_CR_DDR_PLL_POSTDIV1_MASK 0x00700000U
#define PLATO_CR_DDR_PLL_POSTDIV1_SHIFT 20
#define PLATO_CR_DDR_PLL_POSTDIV1_SIGNED 0

#define PLATO_CR_DDR_PLL_REFDIV_MASK 0x0003F000U
#define PLATO_CR_DDR_PLL_REFDIV_SHIFT 12
#define PLATO_CR_DDR_PLL_REFDIV_SIGNED 0

#define PLATO_CR_DDR_PLL_FBDIV_MASK 0x00000FFFU
#define PLATO_CR_DDR_PLL_FBDIV_SHIFT 0
#define PLATO_CR_DDR_PLL_FBDIV_SIGNED 0

/*
	Register CR_DDR_PLL_CTRL_1
*/
#define PLATO_AON_CR_DDR_PLL_CTRL_1 0x006C
#define PLATO_CR_DDR_PLL_DSMPD_MASK 0x01000000U
#define PLATO_CR_DDR_PLL_DSMPD_SHIFT 24
#define PLATO_CR_DDR_PLL_DSMPD_SIGNED 0

#define PLATO_CR_DDR_PLL_FRAC_MASK 0x00FFFFFFU
#define PLATO_CR_DDR_PLL_FRAC_SHIFT 0
#define PLATO_CR_DDR_PLL_FRAC_SIGNED 0

/*
	Register CR_PLL_STATUS
*/
#define PLATO_AON_CR_PLL_STATUS 0x0070
#define PLATO_CR_DDR_PLL_LOCK_MASK 0x00000002U
#define PLATO_CR_DDR_PLL_LOCK_SHIFT 1
#define PLATO_CR_DDR_PLL_LOCK_SIGNED 0

#define PLATO_CR_GPU_PLL_LOCK_MASK 0x00000001U
#define PLATO_CR_GPU_PLL_LOCK_SHIFT 0
#define PLATO_CR_GPU_PLL_LOCK_SIGNED 0

/*
	Register CR_PLL_BYPASS
*/
#define PLATO_AON_CR_PLL_BYPASS 0x0080
#define PLATO_CR_PLL_BYPASS_MASK 0x00000001U
#define PLATO_CR_PLL_BYPASS_SHIFT 0
#define PLATO_CR_PLL_BYPASS_SIGNED 0

/*
	Register CR_CPU_CLK_CTRL
*/
#define PLATO_AON_CR_CPU_CLK_CTRL 0x0084
#define PLATO_CR_CPUV1_DIV_0_MASK 0x00000700U
#define PLATO_CR_CPUV1_DIV_0_SHIFT 8
#define PLATO_CR_CPUV1_DIV_0_SIGNED 0

#define PLATO_CR_CPUV0_DIV_0_MASK 0x00000030U
#define PLATO_CR_CPUV0_DIV_0_SHIFT 4
#define PLATO_CR_CPUV0_DIV_0_SIGNED 0

#define PLATO_CR_CS_CPU_0_SW_MASK 0x00000001U
#define PLATO_CR_CS_CPU_0_SW_SHIFT 0
#define PLATO_CR_CS_CPU_0_SW_SIGNED 0

/*
	Register CR_NOC_CLK_CTRL
*/
#define PLATO_AON_CR_NOC_CLK_CTRL 0x0088
#define PLATO_CR_NOCV1_DIV_0_MASK 0x00000070U
#define PLATO_CR_NOCV1_DIV_0_SHIFT 4
#define PLATO_CR_NOCV1_DIV_0_SIGNED 0

#define PLATO_CR_NOCV0_DIV_0_MASK 0x00000003U
#define PLATO_CR_NOCV0_DIV_0_SHIFT 0
#define PLATO_CR_NOCV0_DIV_0_SIGNED 0

/*
	Register CR_BOOT_VECTOR
*/
#define PLATO_AON_CR_BOOT_VECTOR 0x0090
#define PLATO_CR_BOOT_VECTOR_MASK 0xFFFFFFFFU
#define PLATO_CR_BOOT_VECTOR_SHIFT 0
#define PLATO_CR_BOOT_VECTOR_SIGNED 0

/*
	Register CR_TESTBENCH_ADDRESS
*/
#define PLATO_AON_CR_TESTBENCH_ADDRESS 0x1800
#define PLATO_CR_TESTBENCH_ADDRESS_MASK 0xFFFFFFFFU
#define PLATO_CR_TESTBENCH_ADDRESS_SHIFT 0
#define PLATO_CR_TESTBENCH_ADDRESS_SIGNED 0

/*
	Register CR_TESTBENCH_WDATA
*/
#define PLATO_AON_CR_TESTBENCH_WDATA 0x1804
#define PLATO_CR_TESTBENCH_WDATA_MASK 0xFFFFFFFFU
#define PLATO_CR_TESTBENCH_WDATA_SHIFT 0
#define PLATO_CR_TESTBENCH_WDATA_SIGNED 0

/*
	Register CR_TESTBENCH_RDATA
*/
#define PLATO_AON_CR_TESTBENCH_RDATA 0x1808
#define PLATO_CR_TESTBENCH_RDATA_MASK 0xFFFFFFFFU
#define PLATO_CR_TESTBENCH_RDATA_SHIFT 0
#define PLATO_CR_TESTBENCH_RDATA_SIGNED 0

/*
	Register CR_TESTBENCH_COMMAND
*/
#define PLATO_AON_CR_TESTBENCH_COMMAND 0x180C
#define PLATO_CR_TESTBENCH_COMMAND_MASK 0xFFFFFFFFU
#define PLATO_CR_TESTBENCH_COMMAND_SHIFT 0
#define PLATO_CR_TESTBENCH_COMMAND_SIGNED 0

/*
	Register CR_TESTBENCH_STATUS
*/
#define PLATO_AON_CR_TESTBENCH_STATUS 0x1810
#define PLATO_CR_TESTBENCH_STATUS_MASK 0xFFFFFFFFU
#define PLATO_CR_TESTBENCH_STATUS_SHIFT 0
#define PLATO_CR_TESTBENCH_STATUS_SIGNED 0

/*
	Register CR_TESTBENCH_RESULT
*/
#define PLATO_AON_CR_TESTBENCH_RESULT 0x1814
#define PLATO_CR_TESTBENCH_RESULT_MASK 0xFFFFFFFFU
#define PLATO_CR_TESTBENCH_RESULT_SHIFT 0
#define PLATO_CR_TESTBENCH_RESULT_SIGNED 0

/*
	Register CR_TESTBENCH_RUNNING
*/
#define PLATO_AON_CR_TESTBENCH_RUNNING 0x1818
#define PLATO_CR_TESTBENCH_RUNNING_MASK 0xFFFFFFFFU
#define PLATO_CR_TESTBENCH_RUNNING_SHIFT 0
#define PLATO_CR_TESTBENCH_RUNNING_SIGNED 0

/*
	Register CR_TESTBENCH_MARKER
*/
#define PLATO_AON_CR_TESTBENCH_MARKER 0x181C
#define PLATO_CR_TESTBENCH_MARKER_MASK 0xFFFFFFFFU
#define PLATO_CR_TESTBENCH_MARKER_SHIFT 0
#define PLATO_CR_TESTBENCH_MARKER_SIGNED 0

#endif /* _PLATO_AON_REGS_H_ */

/*****************************************************************************
 End of file (plato_aon_regs.h)
*****************************************************************************/
