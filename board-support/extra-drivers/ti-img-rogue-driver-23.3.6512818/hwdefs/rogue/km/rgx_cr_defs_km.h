/*************************************************************************/ /*!
@Title          Hardware definition file rgx_cr_defs_km.h
@Brief          The file contains auto-generated hardware definitions without
                BVNC-specific compile time conditionals.
@Copyright      Copyright (c) Imagination Technologies Ltd. All Rights Reserved
@License        Dual MIT/GPLv2

The contents of this file are subject to the MIT license as set out below.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

Alternatively, the contents of this file may be used under the terms of
the GNU General Public License Version 2 ("GPL") in which case the provisions
of GPL are applicable instead of those above.

If you wish to allow use of your version of this file only under the terms of
GPL, and not to allow others to use your version of this file under the terms
of the MIT license, indicate your decision by deleting the provisions above
and replace them with the notice and other provisions required by GPL as set
out in the file called "GPL-COPYING" included in this distribution. If you do
not delete the provisions above, a recipient may use your version of this file
under the terms of either the MIT license or GPL.

This License is also included in this distribution in the file called
"MIT-COPYING".

EXCEPT AS OTHERWISE STATED IN A NEGOTIATED AGREEMENT: (A) THE SOFTWARE IS
PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
PURPOSE AND NONINFRINGEMENT; AND (B) IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ /**************************************************************************/

/*               ****   Autogenerated C -- do not edit    ****               */

/*
 */

#ifndef RGX_CR_DEFS_KM_H
#define RGX_CR_DEFS_KM_H

#if !defined(IMG_EXPLICIT_INCLUDE_HWDEFS)
#error This file may only be included if explicitly defined
#endif

#include "img_types.h"
#include "img_defs.h"

#define RGX_CR_DEFS_KM_REVISION 1

/*
    Register RGX_CR_RASTERISATION_INDIRECT
*/
#define RGX_CR_RASTERISATION_INDIRECT (0x8238U)
#define RGX_CR_RASTERISATION_INDIRECT_MASKFULL \
	(IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_RASTERISATION_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_RASTERISATION_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFF0U)

/*
    Register RGX_CR_USC_INDIRECT
*/
#define RGX_CR_USC_INDIRECT (0x8000U)
#define RGX_CR_USC_INDIRECT_MASKFULL (IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_USC_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_USC_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFF0U)

/*
    Register RGX_CR_PBE_INDIRECT
*/
#define RGX_CR_PBE_INDIRECT (0x83E0U)
#define RGX_CR_PBE_INDIRECT_MASKFULL (IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_PBE_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_PBE_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFF0U)

/*
    Register RGX_CR_PBE_PERF_INDIRECT
*/
#define RGX_CR_PBE_PERF_INDIRECT (0x83D8U)
#define RGX_CR_PBE_PERF_INDIRECT_MASKFULL (IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_PBE_PERF_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_PBE_PERF_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFF0U)

/*
    Register RGX_CR_TPU_PERF_INDIRECT
*/
#define RGX_CR_TPU_PERF_INDIRECT (0x83F0U)
#define RGX_CR_TPU_PERF_INDIRECT_MASKFULL (IMG_UINT64_C(0x0000000000000007))
#define RGX_CR_TPU_PERF_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_TPU_PERF_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFF8U)

/*
    Register RGX_CR_RASTERISATION_PERF_INDIRECT
*/
#define RGX_CR_RASTERISATION_PERF_INDIRECT (0x8318U)
#define RGX_CR_RASTERISATION_PERF_INDIRECT_MASKFULL \
	(IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_RASTERISATION_PERF_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_RASTERISATION_PERF_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFF0U)

/*
    Register RGX_CR_TPU_MCU_L0_PERF_INDIRECT
*/
#define RGX_CR_TPU_MCU_L0_PERF_INDIRECT (0x8028U)
#define RGX_CR_TPU_MCU_L0_PERF_INDIRECT_MASKFULL \
	(IMG_UINT64_C(0x0000000000000007))
#define RGX_CR_TPU_MCU_L0_PERF_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_TPU_MCU_L0_PERF_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFF8U)

/*
    Register RGX_CR_USC_PERF_INDIRECT
*/
#define RGX_CR_USC_PERF_INDIRECT (0x8030U)
#define RGX_CR_USC_PERF_INDIRECT_MASKFULL (IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_USC_PERF_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_USC_PERF_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFF0U)

/*
    Register RGX_CR_BLACKPEARL_INDIRECT
*/
#define RGX_CR_BLACKPEARL_INDIRECT (0x8388U)
#define RGX_CR_BLACKPEARL_INDIRECT_MASKFULL (IMG_UINT64_C(0x0000000000000003))
#define RGX_CR_BLACKPEARL_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_BLACKPEARL_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFFCU)

/*
    Register RGX_CR_BLACKPEARL_PERF_INDIRECT
*/
#define RGX_CR_BLACKPEARL_PERF_INDIRECT (0x83F8U)
#define RGX_CR_BLACKPEARL_PERF_INDIRECT_MASKFULL \
	(IMG_UINT64_C(0x0000000000000003))
#define RGX_CR_BLACKPEARL_PERF_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_BLACKPEARL_PERF_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFFCU)

/*
    Register RGX_CR_TEXAS3_PERF_INDIRECT
*/
#define RGX_CR_TEXAS3_PERF_INDIRECT (0x83D0U)
#define RGX_CR_TEXAS3_PERF_INDIRECT_MASKFULL (IMG_UINT64_C(0x0000000000000007))
#define RGX_CR_TEXAS3_PERF_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_TEXAS3_PERF_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFF8U)

/*
    Register RGX_CR_TEXAS_PERF_INDIRECT
*/
#define RGX_CR_TEXAS_PERF_INDIRECT (0x8288U)
#define RGX_CR_TEXAS_PERF_INDIRECT_MASKFULL (IMG_UINT64_C(0x0000000000000003))
#define RGX_CR_TEXAS_PERF_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_TEXAS_PERF_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFFCU)

/*
    Register RGX_CR_BX_TU_PERF_INDIRECT
*/
#define RGX_CR_BX_TU_PERF_INDIRECT (0xC900U)
#define RGX_CR_BX_TU_PERF_INDIRECT_MASKFULL (IMG_UINT64_C(0x0000000000000003))
#define RGX_CR_BX_TU_PERF_INDIRECT_ADDRESS_SHIFT (0U)
#define RGX_CR_BX_TU_PERF_INDIRECT_ADDRESS_CLRMSK (0xFFFFFFFCU)

/*
    Register RGX_CR_CLK_CTRL
*/
#define RGX_CR_CLK_CTRL (0x0000U)
#define RGX_CR_CLK_CTRL__PBE2_XE__MASKFULL (IMG_UINT64_C(0xFFFFFF003F3FFFFF))
#define RGX_CR_CLK_CTRL__S7_INFRA__MASKFULL (IMG_UINT64_C(0xCFCF03000F3F3F0F))
#define RGX_CR_CLK_CTRL_MASKFULL (IMG_UINT64_C(0xFFFFFF003F3FFFFF))
#define RGX_CR_CLK_CTRL_BIF_TEXAS_SHIFT (62U)
#define RGX_CR_CLK_CTRL_BIF_TEXAS_CLRMSK (IMG_UINT64_C(0x3FFFFFFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_BIF_TEXAS_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_BIF_TEXAS_ON (IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_CLK_CTRL_BIF_TEXAS_AUTO (IMG_UINT64_C(0x8000000000000000))
#define RGX_CR_CLK_CTRL_BIF_TEXAS__S7_INFRA__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_BIF_TEXAS__S7_INFRA__ON \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_CLK_CTRL_BIF_TEXAS__S7_INFRA__AUTO \
	(IMG_UINT64_C(0x8000000000000000))
#define RGX_CR_CLK_CTRL_BIF_TEXAS__PBE2_XE__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_BIF_TEXAS__PBE2_XE__ON \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_CLK_CTRL_BIF_TEXAS__PBE2_XE__AUTO \
	(IMG_UINT64_C(0x8000000000000000))
#define RGX_CR_CLK_CTRL_IPP_SHIFT (60U)
#define RGX_CR_CLK_CTRL_IPP_CLRMSK (IMG_UINT64_C(0xCFFFFFFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_IPP_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_IPP_ON (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_CLK_CTRL_IPP_AUTO (IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_CLK_CTRL_IPP__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_IPP__S7_INFRA__ON (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_CLK_CTRL_IPP__S7_INFRA__AUTO (IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_CLK_CTRL_IPP__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_IPP__PBE2_XE__ON (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_CLK_CTRL_IPP__PBE2_XE__AUTO (IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_CLK_CTRL_FBC_SHIFT (58U)
#define RGX_CR_CLK_CTRL_FBC_CLRMSK (IMG_UINT64_C(0xF3FFFFFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_FBC_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_FBC_ON (IMG_UINT64_C(0x0400000000000000))
#define RGX_CR_CLK_CTRL_FBC_AUTO (IMG_UINT64_C(0x0800000000000000))
#define RGX_CR_CLK_CTRL_FBC__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_FBC__S7_INFRA__ON (IMG_UINT64_C(0x0400000000000000))
#define RGX_CR_CLK_CTRL_FBC__S7_INFRA__AUTO (IMG_UINT64_C(0x0800000000000000))
#define RGX_CR_CLK_CTRL_FBC__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_FBC__PBE2_XE__ON (IMG_UINT64_C(0x0400000000000000))
#define RGX_CR_CLK_CTRL_FBC__PBE2_XE__AUTO (IMG_UINT64_C(0x0800000000000000))
#define RGX_CR_CLK_CTRL_FBDC_SHIFT (56U)
#define RGX_CR_CLK_CTRL_FBDC_CLRMSK (IMG_UINT64_C(0xFCFFFFFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_FBDC_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_FBDC_ON (IMG_UINT64_C(0x0100000000000000))
#define RGX_CR_CLK_CTRL_FBDC_AUTO (IMG_UINT64_C(0x0200000000000000))
#define RGX_CR_CLK_CTRL_FBDC__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_FBDC__S7_INFRA__ON (IMG_UINT64_C(0x0100000000000000))
#define RGX_CR_CLK_CTRL_FBDC__S7_INFRA__AUTO (IMG_UINT64_C(0x0200000000000000))
#define RGX_CR_CLK_CTRL_FBDC__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_FBDC__PBE2_XE__ON (IMG_UINT64_C(0x0100000000000000))
#define RGX_CR_CLK_CTRL_FBDC__PBE2_XE__AUTO (IMG_UINT64_C(0x0200000000000000))
#define RGX_CR_CLK_CTRL_FB_TLCACHE_SHIFT (54U)
#define RGX_CR_CLK_CTRL_FB_TLCACHE_CLRMSK (IMG_UINT64_C(0xFF3FFFFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_FB_TLCACHE_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_FB_TLCACHE_ON (IMG_UINT64_C(0x0040000000000000))
#define RGX_CR_CLK_CTRL_FB_TLCACHE_AUTO (IMG_UINT64_C(0x0080000000000000))
#define RGX_CR_CLK_CTRL_FB_TLCACHE__S7_INFRA__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_FB_TLCACHE__S7_INFRA__ON \
	(IMG_UINT64_C(0x0040000000000000))
#define RGX_CR_CLK_CTRL_FB_TLCACHE__S7_INFRA__AUTO \
	(IMG_UINT64_C(0x0080000000000000))
#define RGX_CR_CLK_CTRL_FB_TLCACHE__PBE2_XE__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_FB_TLCACHE__PBE2_XE__ON \
	(IMG_UINT64_C(0x0040000000000000))
#define RGX_CR_CLK_CTRL_FB_TLCACHE__PBE2_XE__AUTO \
	(IMG_UINT64_C(0x0080000000000000))
#define RGX_CR_CLK_CTRL_USCS_SHIFT (52U)
#define RGX_CR_CLK_CTRL_USCS_CLRMSK (IMG_UINT64_C(0xFFCFFFFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_USCS_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_USCS_ON (IMG_UINT64_C(0x0010000000000000))
#define RGX_CR_CLK_CTRL_USCS_AUTO (IMG_UINT64_C(0x0020000000000000))
#define RGX_CR_CLK_CTRL_USCS__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_USCS__S7_INFRA__ON (IMG_UINT64_C(0x0010000000000000))
#define RGX_CR_CLK_CTRL_USCS__S7_INFRA__AUTO (IMG_UINT64_C(0x0020000000000000))
#define RGX_CR_CLK_CTRL_USCS__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_USCS__PBE2_XE__ON (IMG_UINT64_C(0x0010000000000000))
#define RGX_CR_CLK_CTRL_USCS__PBE2_XE__AUTO (IMG_UINT64_C(0x0020000000000000))
#define RGX_CR_CLK_CTRL_PBE_SHIFT (50U)
#define RGX_CR_CLK_CTRL_PBE_CLRMSK (IMG_UINT64_C(0xFFF3FFFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_PBE_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_PBE_ON (IMG_UINT64_C(0x0004000000000000))
#define RGX_CR_CLK_CTRL_PBE_AUTO (IMG_UINT64_C(0x0008000000000000))
#define RGX_CR_CLK_CTRL_PBE__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_PBE__S7_INFRA__ON (IMG_UINT64_C(0x0004000000000000))
#define RGX_CR_CLK_CTRL_PBE__S7_INFRA__AUTO (IMG_UINT64_C(0x0008000000000000))
#define RGX_CR_CLK_CTRL_PBE__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_PBE__PBE2_XE__ON (IMG_UINT64_C(0x0004000000000000))
#define RGX_CR_CLK_CTRL_PBE__PBE2_XE__AUTO (IMG_UINT64_C(0x0008000000000000))
#define RGX_CR_CLK_CTRL_MCU_L1_SHIFT (48U)
#define RGX_CR_CLK_CTRL_MCU_L1_CLRMSK (IMG_UINT64_C(0xFFFCFFFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_MCU_L1_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_MCU_L1_ON (IMG_UINT64_C(0x0001000000000000))
#define RGX_CR_CLK_CTRL_MCU_L1_AUTO (IMG_UINT64_C(0x0002000000000000))
#define RGX_CR_CLK_CTRL_MCU_L1__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_MCU_L1__S7_INFRA__ON (IMG_UINT64_C(0x0001000000000000))
#define RGX_CR_CLK_CTRL_MCU_L1__S7_INFRA__AUTO \
	(IMG_UINT64_C(0x0002000000000000))
#define RGX_CR_CLK_CTRL_MCU_L1__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_MCU_L1__PBE2_XE__ON (IMG_UINT64_C(0x0001000000000000))
#define RGX_CR_CLK_CTRL_MCU_L1__PBE2_XE__AUTO (IMG_UINT64_C(0x0002000000000000))
#define RGX_CR_CLK_CTRL_CDM_SHIFT (46U)
#define RGX_CR_CLK_CTRL_CDM_CLRMSK (IMG_UINT64_C(0xFFFF3FFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_CDM_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_CDM_ON (IMG_UINT64_C(0x0000400000000000))
#define RGX_CR_CLK_CTRL_CDM_AUTO (IMG_UINT64_C(0x0000800000000000))
#define RGX_CR_CLK_CTRL_CDM__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_CDM__S7_INFRA__ON (IMG_UINT64_C(0x0000400000000000))
#define RGX_CR_CLK_CTRL_CDM__S7_INFRA__AUTO (IMG_UINT64_C(0x0000800000000000))
#define RGX_CR_CLK_CTRL_CDM__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_CDM__PBE2_XE__ON (IMG_UINT64_C(0x0000400000000000))
#define RGX_CR_CLK_CTRL_CDM__PBE2_XE__AUTO (IMG_UINT64_C(0x0000800000000000))
#define RGX_CR_CLK_CTRL_SIDEKICK_SHIFT (44U)
#define RGX_CR_CLK_CTRL_SIDEKICK_CLRMSK (IMG_UINT64_C(0xFFFFCFFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_SIDEKICK_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_SIDEKICK_ON (IMG_UINT64_C(0x0000100000000000))
#define RGX_CR_CLK_CTRL_SIDEKICK_AUTO (IMG_UINT64_C(0x0000200000000000))
#define RGX_CR_CLK_CTRL_SIDEKICK__S7_INFRA__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_SIDEKICK__S7_INFRA__ON \
	(IMG_UINT64_C(0x0000100000000000))
#define RGX_CR_CLK_CTRL_SIDEKICK__S7_INFRA__AUTO \
	(IMG_UINT64_C(0x0000200000000000))
#define RGX_CR_CLK_CTRL_SIDEKICK__PBE2_XE__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_SIDEKICK__PBE2_XE__ON (IMG_UINT64_C(0x0000100000000000))
#define RGX_CR_CLK_CTRL_SIDEKICK__PBE2_XE__AUTO \
	(IMG_UINT64_C(0x0000200000000000))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK_SHIFT (42U)
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK_CLRMSK (IMG_UINT64_C(0xFFFFF3FFFFFFFFFF))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK_ON (IMG_UINT64_C(0x0000040000000000))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK_AUTO (IMG_UINT64_C(0x0000080000000000))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK__S7_INFRA__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK__S7_INFRA__ON \
	(IMG_UINT64_C(0x0000040000000000))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK__S7_INFRA__AUTO \
	(IMG_UINT64_C(0x0000080000000000))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK__PBE2_XE__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK__PBE2_XE__ON \
	(IMG_UINT64_C(0x0000040000000000))
#define RGX_CR_CLK_CTRL_BIF_SIDEKICK__PBE2_XE__AUTO \
	(IMG_UINT64_C(0x0000080000000000))
#define RGX_CR_CLK_CTRL_BIF_SHIFT (40U)
#define RGX_CR_CLK_CTRL_BIF_CLRMSK (IMG_UINT64_C(0xFFFFFCFFFFFFFFFF))
#define RGX_CR_CLK_CTRL_BIF_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_BIF_ON (IMG_UINT64_C(0x0000010000000000))
#define RGX_CR_CLK_CTRL_BIF_AUTO (IMG_UINT64_C(0x0000020000000000))
#define RGX_CR_CLK_CTRL_BIF__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_BIF__S7_INFRA__ON (IMG_UINT64_C(0x0000010000000000))
#define RGX_CR_CLK_CTRL_BIF__S7_INFRA__AUTO (IMG_UINT64_C(0x0000020000000000))
#define RGX_CR_CLK_CTRL_BIF__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_BIF__PBE2_XE__ON (IMG_UINT64_C(0x0000010000000000))
#define RGX_CR_CLK_CTRL_BIF__PBE2_XE__AUTO (IMG_UINT64_C(0x0000020000000000))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX_SHIFT (28U)
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX_CLRMSK (IMG_UINT64_C(0xFFFFFFFFCFFFFFFF))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX_ON (IMG_UINT64_C(0x0000000010000000))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX_AUTO (IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX__S7_INFRA__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX__S7_INFRA__ON \
	(IMG_UINT64_C(0x0000000010000000))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX__S7_INFRA__AUTO \
	(IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX__PBE2_XE__OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX__PBE2_XE__ON \
	(IMG_UINT64_C(0x0000000010000000))
#define RGX_CR_CLK_CTRL_TPU_MCU_DEMUX__PBE2_XE__AUTO \
	(IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_CLK_CTRL_MCU_L0_SHIFT (26U)
#define RGX_CR_CLK_CTRL_MCU_L0_CLRMSK (IMG_UINT64_C(0xFFFFFFFFF3FFFFFF))
#define RGX_CR_CLK_CTRL_MCU_L0_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_MCU_L0_ON (IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_CLK_CTRL_MCU_L0_AUTO (IMG_UINT64_C(0x0000000008000000))
#define RGX_CR_CLK_CTRL_MCU_L0__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_MCU_L0__S7_INFRA__ON (IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_CLK_CTRL_MCU_L0__S7_INFRA__AUTO \
	(IMG_UINT64_C(0x0000000008000000))
#define RGX_CR_CLK_CTRL_MCU_L0__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_MCU_L0__PBE2_XE__ON (IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_CLK_CTRL_MCU_L0__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000008000000))
#define RGX_CR_CLK_CTRL_TPU_SHIFT (24U)
#define RGX_CR_CLK_CTRL_TPU_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFCFFFFFF))
#define RGX_CR_CLK_CTRL_TPU_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TPU_ON (IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_CLK_CTRL_TPU_AUTO (IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_CLK_CTRL_TPU__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TPU__S7_INFRA__ON (IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_CLK_CTRL_TPU__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_CLK_CTRL_TPU__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TPU__PBE2_XE__ON (IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_CLK_CTRL_TPU__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_CLK_CTRL_USC_SHIFT (20U)
#define RGX_CR_CLK_CTRL_USC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFCFFFFF))
#define RGX_CR_CLK_CTRL_USC_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_USC_ON (IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_CLK_CTRL_USC_AUTO (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_CLK_CTRL_USC__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_USC__S7_INFRA__ON (IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_CLK_CTRL_USC__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_CLK_CTRL_USC__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_USC__PBE2_XE__ON (IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_CLK_CTRL_USC__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_CLK_CTRL_TLA_SHIFT (18U)
#define RGX_CR_CLK_CTRL_TLA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFF3FFFF))
#define RGX_CR_CLK_CTRL_TLA_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TLA_ON (IMG_UINT64_C(0x0000000000040000))
#define RGX_CR_CLK_CTRL_TLA_AUTO (IMG_UINT64_C(0x0000000000080000))
#define RGX_CR_CLK_CTRL_TLA__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TLA__S7_INFRA__ON (IMG_UINT64_C(0x0000000000040000))
#define RGX_CR_CLK_CTRL_TLA__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000080000))
#define RGX_CR_CLK_CTRL_TLA__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TLA__PBE2_XE__ON (IMG_UINT64_C(0x0000000000040000))
#define RGX_CR_CLK_CTRL_TLA__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000080000))
#define RGX_CR_CLK_CTRL_SLC_SHIFT (16U)
#define RGX_CR_CLK_CTRL_SLC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFCFFFF))
#define RGX_CR_CLK_CTRL_SLC_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_SLC_ON (IMG_UINT64_C(0x0000000000010000))
#define RGX_CR_CLK_CTRL_SLC_AUTO (IMG_UINT64_C(0x0000000000020000))
#define RGX_CR_CLK_CTRL_SLC__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_SLC__S7_INFRA__ON (IMG_UINT64_C(0x0000000000010000))
#define RGX_CR_CLK_CTRL_SLC__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000020000))
#define RGX_CR_CLK_CTRL_SLC__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_SLC__PBE2_XE__ON (IMG_UINT64_C(0x0000000000010000))
#define RGX_CR_CLK_CTRL_SLC__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000020000))
#define RGX_CR_CLK_CTRL_UVS_SHIFT (14U)
#define RGX_CR_CLK_CTRL_UVS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFF3FFF))
#define RGX_CR_CLK_CTRL_UVS_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_UVS_ON (IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_CLK_CTRL_UVS_AUTO (IMG_UINT64_C(0x0000000000008000))
#define RGX_CR_CLK_CTRL_UVS__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_UVS__S7_INFRA__ON (IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_CLK_CTRL_UVS__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000008000))
#define RGX_CR_CLK_CTRL_UVS__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_UVS__PBE2_XE__ON (IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_CLK_CTRL_UVS__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000008000))
#define RGX_CR_CLK_CTRL_PDS_SHIFT (12U)
#define RGX_CR_CLK_CTRL_PDS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFCFFF))
#define RGX_CR_CLK_CTRL_PDS_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_PDS_ON (IMG_UINT64_C(0x0000000000001000))
#define RGX_CR_CLK_CTRL_PDS_AUTO (IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_CLK_CTRL_PDS__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_PDS__S7_INFRA__ON (IMG_UINT64_C(0x0000000000001000))
#define RGX_CR_CLK_CTRL_PDS__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_CLK_CTRL_PDS__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_PDS__PBE2_XE__ON (IMG_UINT64_C(0x0000000000001000))
#define RGX_CR_CLK_CTRL_PDS__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_CLK_CTRL_VDM_SHIFT (10U)
#define RGX_CR_CLK_CTRL_VDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFF3FF))
#define RGX_CR_CLK_CTRL_VDM_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_VDM_ON (IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_CLK_CTRL_VDM_AUTO (IMG_UINT64_C(0x0000000000000800))
#define RGX_CR_CLK_CTRL_VDM__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_VDM__S7_INFRA__ON (IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_CLK_CTRL_VDM__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000000800))
#define RGX_CR_CLK_CTRL_VDM__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_VDM__PBE2_XE__ON (IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_CLK_CTRL_VDM__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000000800))
#define RGX_CR_CLK_CTRL_PM_SHIFT (8U)
#define RGX_CR_CLK_CTRL_PM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFCFF))
#define RGX_CR_CLK_CTRL_PM_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_PM_ON (IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_CLK_CTRL_PM_AUTO (IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_CLK_CTRL_PM__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_PM__S7_INFRA__ON (IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_CLK_CTRL_PM__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_CLK_CTRL_PM__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_PM__PBE2_XE__ON (IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_CLK_CTRL_PM__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_CLK_CTRL_GPP_SHIFT (6U)
#define RGX_CR_CLK_CTRL_GPP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFF3F))
#define RGX_CR_CLK_CTRL_GPP_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_GPP_ON (IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_CLK_CTRL_GPP_AUTO (IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_CLK_CTRL_GPP__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_GPP__S7_INFRA__ON (IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_CLK_CTRL_GPP__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_CLK_CTRL_GPP__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_GPP__PBE2_XE__ON (IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_CLK_CTRL_GPP__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_CLK_CTRL_TE_SHIFT (4U)
#define RGX_CR_CLK_CTRL_TE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFCF))
#define RGX_CR_CLK_CTRL_TE_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TE_ON (IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_CLK_CTRL_TE_AUTO (IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_CLK_CTRL_TE__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TE__S7_INFRA__ON (IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_CLK_CTRL_TE__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_CLK_CTRL_TE__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TE__PBE2_XE__ON (IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_CLK_CTRL_TE__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_CLK_CTRL_TSP_SHIFT (2U)
#define RGX_CR_CLK_CTRL_TSP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFF3))
#define RGX_CR_CLK_CTRL_TSP_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TSP_ON (IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_CLK_CTRL_TSP_AUTO (IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_CLK_CTRL_TSP__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TSP__S7_INFRA__ON (IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_CLK_CTRL_TSP__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_CLK_CTRL_TSP__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_TSP__PBE2_XE__ON (IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_CLK_CTRL_TSP__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_CLK_CTRL_ISP_SHIFT (0U)
#define RGX_CR_CLK_CTRL_ISP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFC))
#define RGX_CR_CLK_CTRL_ISP_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_ISP_ON (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_CLK_CTRL_ISP_AUTO (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_CLK_CTRL_ISP__S7_INFRA__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_ISP__S7_INFRA__ON (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_CLK_CTRL_ISP__S7_INFRA__AUTO (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_CLK_CTRL_ISP__PBE2_XE__OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL_ISP__PBE2_XE__ON (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_CLK_CTRL_ISP__PBE2_XE__AUTO (IMG_UINT64_C(0x0000000000000002))

/*
    Register RGX_CR_CLK_STATUS
*/
#define RGX_CR_CLK_STATUS (0x0008U)
#define RGX_CR_CLK_STATUS__PBE2_XE__MASKFULL (IMG_UINT64_C(0x00000001FFF077FF))
#define RGX_CR_CLK_STATUS__S7_INFRA__MASKFULL (IMG_UINT64_C(0x00000001B3101773))
#define RGX_CR_CLK_STATUS_MASKFULL (IMG_UINT64_C(0x00000001FFF077FF))
#define RGX_CR_CLK_STATUS_MCU_FBTC_SHIFT (32U)
#define RGX_CR_CLK_STATUS_MCU_FBTC_CLRMSK (IMG_UINT64_C(0xFFFFFFFEFFFFFFFF))
#define RGX_CR_CLK_STATUS_MCU_FBTC_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_MCU_FBTC_RUNNING (IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_CLK_STATUS_MCU_FBTC__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_MCU_FBTC__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_CLK_STATUS_MCU_FBTC__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_MCU_FBTC__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_CLK_STATUS_BIF_TEXAS_SHIFT (31U)
#define RGX_CR_CLK_STATUS_BIF_TEXAS_CLRMSK (IMG_UINT64_C(0xFFFFFFFF7FFFFFFF))
#define RGX_CR_CLK_STATUS_BIF_TEXAS_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_BIF_TEXAS_RUNNING (IMG_UINT64_C(0x0000000080000000))
#define RGX_CR_CLK_STATUS_BIF_TEXAS__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_BIF_TEXAS__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000080000000))
#define RGX_CR_CLK_STATUS_BIF_TEXAS__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_BIF_TEXAS__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000080000000))
#define RGX_CR_CLK_STATUS_IPP_SHIFT (30U)
#define RGX_CR_CLK_STATUS_IPP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFBFFFFFFF))
#define RGX_CR_CLK_STATUS_IPP_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_IPP_RUNNING (IMG_UINT64_C(0x0000000040000000))
#define RGX_CR_CLK_STATUS_IPP__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_IPP__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000040000000))
#define RGX_CR_CLK_STATUS_IPP__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_IPP__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000040000000))
#define RGX_CR_CLK_STATUS_FBC_SHIFT (29U)
#define RGX_CR_CLK_STATUS_FBC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFDFFFFFFF))
#define RGX_CR_CLK_STATUS_FBC_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_FBC_RUNNING (IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_CLK_STATUS_FBC__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_FBC__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_CLK_STATUS_FBC__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_FBC__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_CLK_STATUS_FBDC_SHIFT (28U)
#define RGX_CR_CLK_STATUS_FBDC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFEFFFFFFF))
#define RGX_CR_CLK_STATUS_FBDC_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_FBDC_RUNNING (IMG_UINT64_C(0x0000000010000000))
#define RGX_CR_CLK_STATUS_FBDC__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_FBDC__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000010000000))
#define RGX_CR_CLK_STATUS_FBDC__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_FBDC__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000010000000))
#define RGX_CR_CLK_STATUS_FB_TLCACHE_SHIFT (27U)
#define RGX_CR_CLK_STATUS_FB_TLCACHE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFF7FFFFFF))
#define RGX_CR_CLK_STATUS_FB_TLCACHE_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_FB_TLCACHE_RUNNING (IMG_UINT64_C(0x0000000008000000))
#define RGX_CR_CLK_STATUS_FB_TLCACHE__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_FB_TLCACHE__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000008000000))
#define RGX_CR_CLK_STATUS_FB_TLCACHE__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_FB_TLCACHE__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000008000000))
#define RGX_CR_CLK_STATUS_USCS_SHIFT (26U)
#define RGX_CR_CLK_STATUS_USCS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFBFFFFFF))
#define RGX_CR_CLK_STATUS_USCS_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_USCS_RUNNING (IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_CLK_STATUS_USCS__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_USCS__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_CLK_STATUS_USCS__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_USCS__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_CLK_STATUS_PBE_SHIFT (25U)
#define RGX_CR_CLK_STATUS_PBE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFDFFFFFF))
#define RGX_CR_CLK_STATUS_PBE_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_PBE_RUNNING (IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_CLK_STATUS_PBE__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_PBE__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_CLK_STATUS_PBE__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_PBE__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_CLK_STATUS_MCU_L1_SHIFT (24U)
#define RGX_CR_CLK_STATUS_MCU_L1_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFEFFFFFF))
#define RGX_CR_CLK_STATUS_MCU_L1_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_MCU_L1_RUNNING (IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_CLK_STATUS_MCU_L1__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_MCU_L1__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_CLK_STATUS_MCU_L1__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_MCU_L1__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_CLK_STATUS_CDM_SHIFT (23U)
#define RGX_CR_CLK_STATUS_CDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFF7FFFFF))
#define RGX_CR_CLK_STATUS_CDM_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_CDM_RUNNING (IMG_UINT64_C(0x0000000000800000))
#define RGX_CR_CLK_STATUS_CDM__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_CDM__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000800000))
#define RGX_CR_CLK_STATUS_CDM__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_CDM__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000800000))
#define RGX_CR_CLK_STATUS_SIDEKICK_SHIFT (22U)
#define RGX_CR_CLK_STATUS_SIDEKICK_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFBFFFFF))
#define RGX_CR_CLK_STATUS_SIDEKICK_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_SIDEKICK_RUNNING (IMG_UINT64_C(0x0000000000400000))
#define RGX_CR_CLK_STATUS_SIDEKICK__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_SIDEKICK__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000400000))
#define RGX_CR_CLK_STATUS_SIDEKICK__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_SIDEKICK__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000400000))
#define RGX_CR_CLK_STATUS_BIF_SIDEKICK_SHIFT (21U)
#define RGX_CR_CLK_STATUS_BIF_SIDEKICK_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_CLK_STATUS_BIF_SIDEKICK_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_BIF_SIDEKICK_RUNNING \
	(IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_CLK_STATUS_BIF_SIDEKICK__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_BIF_SIDEKICK__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_CLK_STATUS_BIF_SIDEKICK__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_BIF_SIDEKICK__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_CLK_STATUS_BIF_SHIFT (20U)
#define RGX_CR_CLK_STATUS_BIF_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFEFFFFF))
#define RGX_CR_CLK_STATUS_BIF_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_BIF_RUNNING (IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_CLK_STATUS_BIF__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_BIF__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_CLK_STATUS_BIF__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_BIF__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_CLK_STATUS_TPU_MCU_DEMUX_SHIFT (14U)
#define RGX_CR_CLK_STATUS_TPU_MCU_DEMUX_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFBFFF))
#define RGX_CR_CLK_STATUS_TPU_MCU_DEMUX_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TPU_MCU_DEMUX_RUNNING \
	(IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_CLK_STATUS_TPU_MCU_DEMUX__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TPU_MCU_DEMUX__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_CLK_STATUS_TPU_MCU_DEMUX__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TPU_MCU_DEMUX__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_CLK_STATUS_MCU_L0_SHIFT (13U)
#define RGX_CR_CLK_STATUS_MCU_L0_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFDFFF))
#define RGX_CR_CLK_STATUS_MCU_L0_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_MCU_L0_RUNNING (IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_CLK_STATUS_MCU_L0__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_MCU_L0__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_CLK_STATUS_MCU_L0__PBE2_XE__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_MCU_L0__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_CLK_STATUS_TPU_SHIFT (12U)
#define RGX_CR_CLK_STATUS_TPU_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFEFFF))
#define RGX_CR_CLK_STATUS_TPU_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TPU_RUNNING (IMG_UINT64_C(0x0000000000001000))
#define RGX_CR_CLK_STATUS_TPU__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TPU__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000001000))
#define RGX_CR_CLK_STATUS_TPU__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TPU__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000001000))
#define RGX_CR_CLK_STATUS_USC_SHIFT (10U)
#define RGX_CR_CLK_STATUS_USC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFBFF))
#define RGX_CR_CLK_STATUS_USC_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_USC_RUNNING (IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_CLK_STATUS_USC__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_USC__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_CLK_STATUS_USC__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_USC__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_CLK_STATUS_TLA_SHIFT (9U)
#define RGX_CR_CLK_STATUS_TLA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFDFF))
#define RGX_CR_CLK_STATUS_TLA_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TLA_RUNNING (IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_CLK_STATUS_TLA__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TLA__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_CLK_STATUS_TLA__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TLA__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_CLK_STATUS_SLC_SHIFT (8U)
#define RGX_CR_CLK_STATUS_SLC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFEFF))
#define RGX_CR_CLK_STATUS_SLC_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_SLC_RUNNING (IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_CLK_STATUS_SLC__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_SLC__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_CLK_STATUS_SLC__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_SLC__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_CLK_STATUS_UVS_SHIFT (7U)
#define RGX_CR_CLK_STATUS_UVS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFF7F))
#define RGX_CR_CLK_STATUS_UVS_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_UVS_RUNNING (IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_CLK_STATUS_UVS__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_UVS__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_CLK_STATUS_UVS__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_UVS__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_CLK_STATUS_PDS_SHIFT (6U)
#define RGX_CR_CLK_STATUS_PDS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFBF))
#define RGX_CR_CLK_STATUS_PDS_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_PDS_RUNNING (IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_CLK_STATUS_PDS__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_PDS__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_CLK_STATUS_PDS__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_PDS__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_CLK_STATUS_VDM_SHIFT (5U)
#define RGX_CR_CLK_STATUS_VDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_CLK_STATUS_VDM_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_VDM_RUNNING (IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_CLK_STATUS_VDM__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_VDM__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_CLK_STATUS_VDM__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_VDM__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_CLK_STATUS_PM_SHIFT (4U)
#define RGX_CR_CLK_STATUS_PM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFEF))
#define RGX_CR_CLK_STATUS_PM_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_PM_RUNNING (IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_CLK_STATUS_PM__S7_INFRA__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_PM__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_CLK_STATUS_PM__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_PM__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_CLK_STATUS_GPP_SHIFT (3U)
#define RGX_CR_CLK_STATUS_GPP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFF7))
#define RGX_CR_CLK_STATUS_GPP_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_GPP_RUNNING (IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_CLK_STATUS_GPP__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_GPP__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_CLK_STATUS_GPP__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_GPP__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_CLK_STATUS_TE_SHIFT (2U)
#define RGX_CR_CLK_STATUS_TE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_CR_CLK_STATUS_TE_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TE_RUNNING (IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_CLK_STATUS_TE__S7_INFRA__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TE__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_CLK_STATUS_TE__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TE__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_CLK_STATUS_TSP_SHIFT (1U)
#define RGX_CR_CLK_STATUS_TSP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_CLK_STATUS_TSP_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TSP_RUNNING (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_CLK_STATUS_TSP__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TSP__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_CLK_STATUS_TSP__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_TSP__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_CLK_STATUS_ISP_SHIFT (0U)
#define RGX_CR_CLK_STATUS_ISP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_CLK_STATUS_ISP_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_ISP_RUNNING (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_CLK_STATUS_ISP__S7_INFRA__GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_ISP__S7_INFRA__RUNNING \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_CLK_STATUS_ISP__PBE2_XE__GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS_ISP__PBE2_XE__RUNNING \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_CORE_ID
*/
#define RGX_CR_CORE_ID__PBVNC (0x0020U)
#define RGX_CR_CORE_ID__PBVNC__MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_CORE_ID__PBVNC__BRANCH_ID_SHIFT (48U)
#define RGX_CR_CORE_ID__PBVNC__BRANCH_ID_CLRMSK \
	(IMG_UINT64_C(0x0000FFFFFFFFFFFF))
#define RGX_CR_CORE_ID__PBVNC__VERSION_ID_SHIFT (32U)
#define RGX_CR_CORE_ID__PBVNC__VERSION_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFF0000FFFFFFFF))
#define RGX_CR_CORE_ID__PBVNC__NUMBER_OF_SCALABLE_UNITS_SHIFT (16U)
#define RGX_CR_CORE_ID__PBVNC__NUMBER_OF_SCALABLE_UNITS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF0000FFFF))
#define RGX_CR_CORE_ID__PBVNC__CONFIG_ID_SHIFT (0U)
#define RGX_CR_CORE_ID__PBVNC__CONFIG_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_CORE_ID
*/
#define RGX_CR_CORE_ID (0x0018U)
#define RGX_CR_CORE_ID_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_CORE_ID_ID_SHIFT (16U)
#define RGX_CR_CORE_ID_ID_CLRMSK (0x0000FFFFU)
#define RGX_CR_CORE_ID_CONFIG_SHIFT (0U)
#define RGX_CR_CORE_ID_CONFIG_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_CORE_REVISION
*/
#define RGX_CR_CORE_REVISION (0x0020U)
#define RGX_CR_CORE_REVISION_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_CORE_REVISION_DESIGNER_SHIFT (24U)
#define RGX_CR_CORE_REVISION_DESIGNER_CLRMSK (0x00FFFFFFU)
#define RGX_CR_CORE_REVISION_MAJOR_SHIFT (16U)
#define RGX_CR_CORE_REVISION_MAJOR_CLRMSK (0xFF00FFFFU)
#define RGX_CR_CORE_REVISION_MINOR_SHIFT (8U)
#define RGX_CR_CORE_REVISION_MINOR_CLRMSK (0xFFFF00FFU)
#define RGX_CR_CORE_REVISION_MAINTENANCE_SHIFT (0U)
#define RGX_CR_CORE_REVISION_MAINTENANCE_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_DESIGNER_REV_FIELD1
*/
#define RGX_CR_DESIGNER_REV_FIELD1 (0x0028U)
#define RGX_CR_DESIGNER_REV_FIELD1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_DESIGNER_REV_FIELD1_DESIGNER_REV_FIELD1_SHIFT (0U)
#define RGX_CR_DESIGNER_REV_FIELD1_DESIGNER_REV_FIELD1_CLRMSK (0x00000000U)

/*
    Register RGX_CR_DESIGNER_REV_FIELD2
*/
#define RGX_CR_DESIGNER_REV_FIELD2 (0x0030U)
#define RGX_CR_DESIGNER_REV_FIELD2_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_DESIGNER_REV_FIELD2_DESIGNER_REV_FIELD2_SHIFT (0U)
#define RGX_CR_DESIGNER_REV_FIELD2_DESIGNER_REV_FIELD2_CLRMSK (0x00000000U)

/*
    Register RGX_CR_CHANGESET_NUMBER
*/
#define RGX_CR_CHANGESET_NUMBER (0x0040U)
#define RGX_CR_CHANGESET_NUMBER_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_CHANGESET_NUMBER_CHANGESET_NUMBER_SHIFT (0U)
#define RGX_CR_CHANGESET_NUMBER_CHANGESET_NUMBER_CLRMSK \
	(IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_SOC_TIMER_GRAY
*/
#define RGX_CR_SOC_TIMER_GRAY (0x00E0U)
#define RGX_CR_SOC_TIMER_GRAY_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_SOC_TIMER_GRAY_VALUE_SHIFT (0U)
#define RGX_CR_SOC_TIMER_GRAY_VALUE_CLRMSK (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_SOC_TIMER_BINARY
*/
#define RGX_CR_SOC_TIMER_BINARY (0x00E8U)
#define RGX_CR_SOC_TIMER_BINARY_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_SOC_TIMER_BINARY_VALUE_SHIFT (0U)
#define RGX_CR_SOC_TIMER_BINARY_VALUE_CLRMSK (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_CLK_XTPLUS_CTRL
*/
#define RGX_CR_CLK_XTPLUS_CTRL (0x0080U)
#define RGX_CR_CLK_XTPLUS_CTRL_MASKFULL (IMG_UINT64_C(0x0000003FFFFF0000))
#define RGX_CR_CLK_XTPLUS_CTRL_TDM_SHIFT (36U)
#define RGX_CR_CLK_XTPLUS_CTRL_TDM_CLRMSK (IMG_UINT64_C(0xFFFFFFCFFFFFFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_TDM_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_TDM_ON (IMG_UINT64_C(0x0000001000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_TDM_AUTO (IMG_UINT64_C(0x0000002000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_ASTC_SHIFT (34U)
#define RGX_CR_CLK_XTPLUS_CTRL_ASTC_CLRMSK (IMG_UINT64_C(0xFFFFFFF3FFFFFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_ASTC_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_ASTC_ON (IMG_UINT64_C(0x0000000400000000))
#define RGX_CR_CLK_XTPLUS_CTRL_ASTC_AUTO (IMG_UINT64_C(0x0000000800000000))
#define RGX_CR_CLK_XTPLUS_CTRL_IPF_SHIFT (32U)
#define RGX_CR_CLK_XTPLUS_CTRL_IPF_CLRMSK (IMG_UINT64_C(0xFFFFFFFCFFFFFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_IPF_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_IPF_ON (IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_CLK_XTPLUS_CTRL_IPF_AUTO (IMG_UINT64_C(0x0000000200000000))
#define RGX_CR_CLK_XTPLUS_CTRL_COMPUTE_SHIFT (30U)
#define RGX_CR_CLK_XTPLUS_CTRL_COMPUTE_CLRMSK (IMG_UINT64_C(0xFFFFFFFF3FFFFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_COMPUTE_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_COMPUTE_ON (IMG_UINT64_C(0x0000000040000000))
#define RGX_CR_CLK_XTPLUS_CTRL_COMPUTE_AUTO (IMG_UINT64_C(0x0000000080000000))
#define RGX_CR_CLK_XTPLUS_CTRL_PIXEL_SHIFT (28U)
#define RGX_CR_CLK_XTPLUS_CTRL_PIXEL_CLRMSK (IMG_UINT64_C(0xFFFFFFFFCFFFFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_PIXEL_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_PIXEL_ON (IMG_UINT64_C(0x0000000010000000))
#define RGX_CR_CLK_XTPLUS_CTRL_PIXEL_AUTO (IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_CLK_XTPLUS_CTRL_VERTEX_SHIFT (26U)
#define RGX_CR_CLK_XTPLUS_CTRL_VERTEX_CLRMSK (IMG_UINT64_C(0xFFFFFFFFF3FFFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_VERTEX_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_VERTEX_ON (IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_CLK_XTPLUS_CTRL_VERTEX_AUTO (IMG_UINT64_C(0x0000000008000000))
#define RGX_CR_CLK_XTPLUS_CTRL_USCPS_SHIFT (24U)
#define RGX_CR_CLK_XTPLUS_CTRL_USCPS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFCFFFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_USCPS_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_USCPS_ON (IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_CLK_XTPLUS_CTRL_USCPS_AUTO (IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_CLK_XTPLUS_CTRL_PDS_SHARED_SHIFT (22U)
#define RGX_CR_CLK_XTPLUS_CTRL_PDS_SHARED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFF3FFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_PDS_SHARED_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_PDS_SHARED_ON (IMG_UINT64_C(0x0000000000400000))
#define RGX_CR_CLK_XTPLUS_CTRL_PDS_SHARED_AUTO \
	(IMG_UINT64_C(0x0000000000800000))
#define RGX_CR_CLK_XTPLUS_CTRL_BIF_BLACKPEARL_SHIFT (20U)
#define RGX_CR_CLK_XTPLUS_CTRL_BIF_BLACKPEARL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFCFFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_BIF_BLACKPEARL_OFF \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_BIF_BLACKPEARL_ON \
	(IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_CLK_XTPLUS_CTRL_BIF_BLACKPEARL_AUTO \
	(IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_CLK_XTPLUS_CTRL_USC_SHARED_SHIFT (18U)
#define RGX_CR_CLK_XTPLUS_CTRL_USC_SHARED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFF3FFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_USC_SHARED_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_USC_SHARED_ON (IMG_UINT64_C(0x0000000000040000))
#define RGX_CR_CLK_XTPLUS_CTRL_USC_SHARED_AUTO \
	(IMG_UINT64_C(0x0000000000080000))
#define RGX_CR_CLK_XTPLUS_CTRL_GEOMETRY_SHIFT (16U)
#define RGX_CR_CLK_XTPLUS_CTRL_GEOMETRY_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFCFFFF))
#define RGX_CR_CLK_XTPLUS_CTRL_GEOMETRY_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_CTRL_GEOMETRY_ON (IMG_UINT64_C(0x0000000000010000))
#define RGX_CR_CLK_XTPLUS_CTRL_GEOMETRY_AUTO (IMG_UINT64_C(0x0000000000020000))

/*
    Register RGX_CR_CLK_XTPLUS_STATUS
*/
#define RGX_CR_CLK_XTPLUS_STATUS (0x0088U)
#define RGX_CR_CLK_XTPLUS_STATUS_MASKFULL (IMG_UINT64_C(0x00000000000007FF))
#define RGX_CR_CLK_XTPLUS_STATUS_TDM_SHIFT (10U)
#define RGX_CR_CLK_XTPLUS_STATUS_TDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFBFF))
#define RGX_CR_CLK_XTPLUS_STATUS_TDM_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_TDM_RUNNING (IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_CLK_XTPLUS_STATUS_IPF_SHIFT (9U)
#define RGX_CR_CLK_XTPLUS_STATUS_IPF_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFDFF))
#define RGX_CR_CLK_XTPLUS_STATUS_IPF_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_IPF_RUNNING (IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_CLK_XTPLUS_STATUS_COMPUTE_SHIFT (8U)
#define RGX_CR_CLK_XTPLUS_STATUS_COMPUTE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFEFF))
#define RGX_CR_CLK_XTPLUS_STATUS_COMPUTE_GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_COMPUTE_RUNNING \
	(IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_CLK_XTPLUS_STATUS_ASTC_SHIFT (7U)
#define RGX_CR_CLK_XTPLUS_STATUS_ASTC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFF7F))
#define RGX_CR_CLK_XTPLUS_STATUS_ASTC_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_ASTC_RUNNING (IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_CLK_XTPLUS_STATUS_PIXEL_SHIFT (6U)
#define RGX_CR_CLK_XTPLUS_STATUS_PIXEL_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFBF))
#define RGX_CR_CLK_XTPLUS_STATUS_PIXEL_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_PIXEL_RUNNING \
	(IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_CLK_XTPLUS_STATUS_VERTEX_SHIFT (5U)
#define RGX_CR_CLK_XTPLUS_STATUS_VERTEX_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_CLK_XTPLUS_STATUS_VERTEX_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_VERTEX_RUNNING \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_CLK_XTPLUS_STATUS_USCPS_SHIFT (4U)
#define RGX_CR_CLK_XTPLUS_STATUS_USCPS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFEF))
#define RGX_CR_CLK_XTPLUS_STATUS_USCPS_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_USCPS_RUNNING \
	(IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_CLK_XTPLUS_STATUS_PDS_SHARED_SHIFT (3U)
#define RGX_CR_CLK_XTPLUS_STATUS_PDS_SHARED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFF7))
#define RGX_CR_CLK_XTPLUS_STATUS_PDS_SHARED_GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_PDS_SHARED_RUNNING \
	(IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_CLK_XTPLUS_STATUS_BIF_BLACKPEARL_SHIFT (2U)
#define RGX_CR_CLK_XTPLUS_STATUS_BIF_BLACKPEARL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_CR_CLK_XTPLUS_STATUS_BIF_BLACKPEARL_GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_BIF_BLACKPEARL_RUNNING \
	(IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_CLK_XTPLUS_STATUS_USC_SHARED_SHIFT (1U)
#define RGX_CR_CLK_XTPLUS_STATUS_USC_SHARED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_CLK_XTPLUS_STATUS_USC_SHARED_GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_USC_SHARED_RUNNING \
	(IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_CLK_XTPLUS_STATUS_GEOMETRY_SHIFT (0U)
#define RGX_CR_CLK_XTPLUS_STATUS_GEOMETRY_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_CLK_XTPLUS_STATUS_GEOMETRY_GATED \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_XTPLUS_STATUS_GEOMETRY_RUNNING \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_SOFT_RESET
*/
#define RGX_CR_SOFT_RESET (0x0100U)
#define RGX_CR_SOFT_RESET__PBE2_XE__MASKFULL (IMG_UINT64_C(0xFFEFFFFFFFFFFC3D))
#define RGX_CR_SOFT_RESET_MASKFULL (IMG_UINT64_C(0x00E7FFFFFFFFFC3D))
#define RGX_CR_SOFT_RESET__PBE2_XE__PHANTOM3_CORE_SHIFT (63U)
#define RGX_CR_SOFT_RESET__PBE2_XE__PHANTOM3_CORE_CLRMSK \
	(IMG_UINT64_C(0x7FFFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET__PBE2_XE__PHANTOM3_CORE_EN \
	(IMG_UINT64_C(0x8000000000000000))
#define RGX_CR_SOFT_RESET__PBE2_XE__PHANTOM2_CORE_SHIFT (62U)
#define RGX_CR_SOFT_RESET__PBE2_XE__PHANTOM2_CORE_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET__PBE2_XE__PHANTOM2_CORE_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_SOFT_RESET__PBE2_XE__BERNADO2_CORE_SHIFT (61U)
#define RGX_CR_SOFT_RESET__PBE2_XE__BERNADO2_CORE_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET__PBE2_XE__BERNADO2_CORE_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_SOFT_RESET__PBE2_XE__JONES_CORE_SHIFT (60U)
#define RGX_CR_SOFT_RESET__PBE2_XE__JONES_CORE_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET__PBE2_XE__JONES_CORE_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_SOFT_RESET__PBE2_XE__TILING_CORE_SHIFT (59U)
#define RGX_CR_SOFT_RESET__PBE2_XE__TILING_CORE_CLRMSK \
	(IMG_UINT64_C(0xF7FFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET__PBE2_XE__TILING_CORE_EN \
	(IMG_UINT64_C(0x0800000000000000))
#define RGX_CR_SOFT_RESET__PBE2_XE__TE3_SHIFT (58U)
#define RGX_CR_SOFT_RESET__PBE2_XE__TE3_CLRMSK \
	(IMG_UINT64_C(0xFBFFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET__PBE2_XE__TE3_EN (IMG_UINT64_C(0x0400000000000000))
#define RGX_CR_SOFT_RESET__PBE2_XE__VCE_SHIFT (57U)
#define RGX_CR_SOFT_RESET__PBE2_XE__VCE_CLRMSK \
	(IMG_UINT64_C(0xFDFFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET__PBE2_XE__VCE_EN (IMG_UINT64_C(0x0200000000000000))
#define RGX_CR_SOFT_RESET__PBE2_XE__VBS_SHIFT (56U)
#define RGX_CR_SOFT_RESET__PBE2_XE__VBS_CLRMSK \
	(IMG_UINT64_C(0xFEFFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET__PBE2_XE__VBS_EN (IMG_UINT64_C(0x0100000000000000))
#define RGX_CR_SOFT_RESET_DPX1_CORE_SHIFT (55U)
#define RGX_CR_SOFT_RESET_DPX1_CORE_CLRMSK (IMG_UINT64_C(0xFF7FFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_DPX1_CORE_EN (IMG_UINT64_C(0x0080000000000000))
#define RGX_CR_SOFT_RESET_DPX0_CORE_SHIFT (54U)
#define RGX_CR_SOFT_RESET_DPX0_CORE_CLRMSK (IMG_UINT64_C(0xFFBFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_DPX0_CORE_EN (IMG_UINT64_C(0x0040000000000000))
#define RGX_CR_SOFT_RESET_FBA_SHIFT (53U)
#define RGX_CR_SOFT_RESET_FBA_CLRMSK (IMG_UINT64_C(0xFFDFFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_FBA_EN (IMG_UINT64_C(0x0020000000000000))
#define RGX_CR_SOFT_RESET__PBE2_XE__FB_CDC_SHIFT (51U)
#define RGX_CR_SOFT_RESET__PBE2_XE__FB_CDC_CLRMSK \
	(IMG_UINT64_C(0xFFF7FFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET__PBE2_XE__FB_CDC_EN (IMG_UINT64_C(0x0008000000000000))
#define RGX_CR_SOFT_RESET_SH_SHIFT (50U)
#define RGX_CR_SOFT_RESET_SH_CLRMSK (IMG_UINT64_C(0xFFFBFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_SH_EN (IMG_UINT64_C(0x0004000000000000))
#define RGX_CR_SOFT_RESET_VRDM_SHIFT (49U)
#define RGX_CR_SOFT_RESET_VRDM_CLRMSK (IMG_UINT64_C(0xFFFDFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_VRDM_EN (IMG_UINT64_C(0x0002000000000000))
#define RGX_CR_SOFT_RESET_MCU_FBTC_SHIFT (48U)
#define RGX_CR_SOFT_RESET_MCU_FBTC_CLRMSK (IMG_UINT64_C(0xFFFEFFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_MCU_FBTC_EN (IMG_UINT64_C(0x0001000000000000))
#define RGX_CR_SOFT_RESET_PHANTOM1_CORE_SHIFT (47U)
#define RGX_CR_SOFT_RESET_PHANTOM1_CORE_CLRMSK \
	(IMG_UINT64_C(0xFFFF7FFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_PHANTOM1_CORE_EN (IMG_UINT64_C(0x0000800000000000))
#define RGX_CR_SOFT_RESET_PHANTOM0_CORE_SHIFT (46U)
#define RGX_CR_SOFT_RESET_PHANTOM0_CORE_CLRMSK \
	(IMG_UINT64_C(0xFFFFBFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_PHANTOM0_CORE_EN (IMG_UINT64_C(0x0000400000000000))
#define RGX_CR_SOFT_RESET_BERNADO1_CORE_SHIFT (45U)
#define RGX_CR_SOFT_RESET_BERNADO1_CORE_CLRMSK \
	(IMG_UINT64_C(0xFFFFDFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_BERNADO1_CORE_EN (IMG_UINT64_C(0x0000200000000000))
#define RGX_CR_SOFT_RESET_BERNADO0_CORE_SHIFT (44U)
#define RGX_CR_SOFT_RESET_BERNADO0_CORE_CLRMSK \
	(IMG_UINT64_C(0xFFFFEFFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_BERNADO0_CORE_EN (IMG_UINT64_C(0x0000100000000000))
#define RGX_CR_SOFT_RESET_IPP_SHIFT (43U)
#define RGX_CR_SOFT_RESET_IPP_CLRMSK (IMG_UINT64_C(0xFFFFF7FFFFFFFFFF))
#define RGX_CR_SOFT_RESET_IPP_EN (IMG_UINT64_C(0x0000080000000000))
#define RGX_CR_SOFT_RESET_BIF_TEXAS_SHIFT (42U)
#define RGX_CR_SOFT_RESET_BIF_TEXAS_CLRMSK (IMG_UINT64_C(0xFFFFFBFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_BIF_TEXAS_EN (IMG_UINT64_C(0x0000040000000000))
#define RGX_CR_SOFT_RESET_TORNADO_CORE_SHIFT (41U)
#define RGX_CR_SOFT_RESET_TORNADO_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFDFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_TORNADO_CORE_EN (IMG_UINT64_C(0x0000020000000000))
#define RGX_CR_SOFT_RESET_DUST_H_CORE_SHIFT (40U)
#define RGX_CR_SOFT_RESET_DUST_H_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFEFFFFFFFFFF))
#define RGX_CR_SOFT_RESET_DUST_H_CORE_EN (IMG_UINT64_C(0x0000010000000000))
#define RGX_CR_SOFT_RESET_DUST_G_CORE_SHIFT (39U)
#define RGX_CR_SOFT_RESET_DUST_G_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFF7FFFFFFFFF))
#define RGX_CR_SOFT_RESET_DUST_G_CORE_EN (IMG_UINT64_C(0x0000008000000000))
#define RGX_CR_SOFT_RESET_DUST_F_CORE_SHIFT (38U)
#define RGX_CR_SOFT_RESET_DUST_F_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFFBFFFFFFFFF))
#define RGX_CR_SOFT_RESET_DUST_F_CORE_EN (IMG_UINT64_C(0x0000004000000000))
#define RGX_CR_SOFT_RESET_DUST_E_CORE_SHIFT (37U)
#define RGX_CR_SOFT_RESET_DUST_E_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFFDFFFFFFFFF))
#define RGX_CR_SOFT_RESET_DUST_E_CORE_EN (IMG_UINT64_C(0x0000002000000000))
#define RGX_CR_SOFT_RESET_DUST_D_CORE_SHIFT (36U)
#define RGX_CR_SOFT_RESET_DUST_D_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFFEFFFFFFFFF))
#define RGX_CR_SOFT_RESET_DUST_D_CORE_EN (IMG_UINT64_C(0x0000001000000000))
#define RGX_CR_SOFT_RESET_DUST_C_CORE_SHIFT (35U)
#define RGX_CR_SOFT_RESET_DUST_C_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFFF7FFFFFFFF))
#define RGX_CR_SOFT_RESET_DUST_C_CORE_EN (IMG_UINT64_C(0x0000000800000000))
#define RGX_CR_SOFT_RESET_MMU_SHIFT (34U)
#define RGX_CR_SOFT_RESET_MMU_CLRMSK (IMG_UINT64_C(0xFFFFFFFBFFFFFFFF))
#define RGX_CR_SOFT_RESET_MMU_EN (IMG_UINT64_C(0x0000000400000000))
#define RGX_CR_SOFT_RESET_BIF1_SHIFT (33U)
#define RGX_CR_SOFT_RESET_BIF1_CLRMSK (IMG_UINT64_C(0xFFFFFFFDFFFFFFFF))
#define RGX_CR_SOFT_RESET_BIF1_EN (IMG_UINT64_C(0x0000000200000000))
#define RGX_CR_SOFT_RESET_GARTEN_SHIFT (32U)
#define RGX_CR_SOFT_RESET_GARTEN_CLRMSK (IMG_UINT64_C(0xFFFFFFFEFFFFFFFF))
#define RGX_CR_SOFT_RESET_GARTEN_EN (IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_SOFT_RESET_CPU_SHIFT (32U)
#define RGX_CR_SOFT_RESET_CPU_CLRMSK (IMG_UINT64_C(0xFFFFFFFEFFFFFFFF))
#define RGX_CR_SOFT_RESET_CPU_EN (IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_SOFT_RESET_RASCAL_CORE_SHIFT (31U)
#define RGX_CR_SOFT_RESET_RASCAL_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFFFF7FFFFFFF))
#define RGX_CR_SOFT_RESET_RASCAL_CORE_EN (IMG_UINT64_C(0x0000000080000000))
#define RGX_CR_SOFT_RESET_DUST_B_CORE_SHIFT (30U)
#define RGX_CR_SOFT_RESET_DUST_B_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFBFFFFFFF))
#define RGX_CR_SOFT_RESET_DUST_B_CORE_EN (IMG_UINT64_C(0x0000000040000000))
#define RGX_CR_SOFT_RESET_DUST_A_CORE_SHIFT (29U)
#define RGX_CR_SOFT_RESET_DUST_A_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFDFFFFFFF))
#define RGX_CR_SOFT_RESET_DUST_A_CORE_EN (IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_SOFT_RESET_FB_TLCACHE_SHIFT (28U)
#define RGX_CR_SOFT_RESET_FB_TLCACHE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFEFFFFFFF))
#define RGX_CR_SOFT_RESET_FB_TLCACHE_EN (IMG_UINT64_C(0x0000000010000000))
#define RGX_CR_SOFT_RESET_SLC_SHIFT (27U)
#define RGX_CR_SOFT_RESET_SLC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFF7FFFFFF))
#define RGX_CR_SOFT_RESET_SLC_EN (IMG_UINT64_C(0x0000000008000000))
#define RGX_CR_SOFT_RESET_TLA_SHIFT (26U)
#define RGX_CR_SOFT_RESET_TLA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFBFFFFFF))
#define RGX_CR_SOFT_RESET_TLA_EN (IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_SOFT_RESET_UVS_SHIFT (25U)
#define RGX_CR_SOFT_RESET_UVS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFDFFFFFF))
#define RGX_CR_SOFT_RESET_UVS_EN (IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_SOFT_RESET_TE_SHIFT (24U)
#define RGX_CR_SOFT_RESET_TE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFEFFFFFF))
#define RGX_CR_SOFT_RESET_TE_EN (IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_SOFT_RESET_GPP_SHIFT (23U)
#define RGX_CR_SOFT_RESET_GPP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFF7FFFFF))
#define RGX_CR_SOFT_RESET_GPP_EN (IMG_UINT64_C(0x0000000000800000))
#define RGX_CR_SOFT_RESET_FBDC_SHIFT (22U)
#define RGX_CR_SOFT_RESET_FBDC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFBFFFFF))
#define RGX_CR_SOFT_RESET_FBDC_EN (IMG_UINT64_C(0x0000000000400000))
#define RGX_CR_SOFT_RESET_FBC_SHIFT (21U)
#define RGX_CR_SOFT_RESET_FBC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_SOFT_RESET_FBC_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_SOFT_RESET_PM_SHIFT (20U)
#define RGX_CR_SOFT_RESET_PM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFEFFFFF))
#define RGX_CR_SOFT_RESET_PM_EN (IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_SOFT_RESET_PBE_SHIFT (19U)
#define RGX_CR_SOFT_RESET_PBE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFF7FFFF))
#define RGX_CR_SOFT_RESET_PBE_EN (IMG_UINT64_C(0x0000000000080000))
#define RGX_CR_SOFT_RESET_USC_SHARED_SHIFT (18U)
#define RGX_CR_SOFT_RESET_USC_SHARED_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFBFFFF))
#define RGX_CR_SOFT_RESET_USC_SHARED_EN (IMG_UINT64_C(0x0000000000040000))
#define RGX_CR_SOFT_RESET_MCU_L1_SHIFT (17U)
#define RGX_CR_SOFT_RESET_MCU_L1_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFDFFFF))
#define RGX_CR_SOFT_RESET_MCU_L1_EN (IMG_UINT64_C(0x0000000000020000))
#define RGX_CR_SOFT_RESET_BIF_SHIFT (16U)
#define RGX_CR_SOFT_RESET_BIF_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFEFFFF))
#define RGX_CR_SOFT_RESET_BIF_EN (IMG_UINT64_C(0x0000000000010000))
#define RGX_CR_SOFT_RESET_CDM_SHIFT (15U)
#define RGX_CR_SOFT_RESET_CDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFF7FFF))
#define RGX_CR_SOFT_RESET_CDM_EN (IMG_UINT64_C(0x0000000000008000))
#define RGX_CR_SOFT_RESET_VDM_SHIFT (14U)
#define RGX_CR_SOFT_RESET_VDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFBFFF))
#define RGX_CR_SOFT_RESET_VDM_EN (IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_SOFT_RESET_TESS_SHIFT (13U)
#define RGX_CR_SOFT_RESET_TESS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFDFFF))
#define RGX_CR_SOFT_RESET_TESS_EN (IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_SOFT_RESET_PDS_SHIFT (12U)
#define RGX_CR_SOFT_RESET_PDS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFEFFF))
#define RGX_CR_SOFT_RESET_PDS_EN (IMG_UINT64_C(0x0000000000001000))
#define RGX_CR_SOFT_RESET_ISP_SHIFT (11U)
#define RGX_CR_SOFT_RESET_ISP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFF7FF))
#define RGX_CR_SOFT_RESET_ISP_EN (IMG_UINT64_C(0x0000000000000800))
#define RGX_CR_SOFT_RESET_TSP_SHIFT (10U)
#define RGX_CR_SOFT_RESET_TSP_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFBFF))
#define RGX_CR_SOFT_RESET_TSP_EN (IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_SOFT_RESET_SYSARB_SHIFT (5U)
#define RGX_CR_SOFT_RESET_SYSARB_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_SOFT_RESET_SYSARB_EN (IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_SOFT_RESET_TPU_MCU_DEMUX_SHIFT (4U)
#define RGX_CR_SOFT_RESET_TPU_MCU_DEMUX_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFEF))
#define RGX_CR_SOFT_RESET_TPU_MCU_DEMUX_EN (IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_SOFT_RESET_MCU_L0_SHIFT (3U)
#define RGX_CR_SOFT_RESET_MCU_L0_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFF7))
#define RGX_CR_SOFT_RESET_MCU_L0_EN (IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_SOFT_RESET_TPU_SHIFT (2U)
#define RGX_CR_SOFT_RESET_TPU_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_CR_SOFT_RESET_TPU_EN (IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_SOFT_RESET_USC_SHIFT (0U)
#define RGX_CR_SOFT_RESET_USC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_SOFT_RESET_USC_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_SOFT_RESET2
*/
#define RGX_CR_SOFT_RESET2 (0x0108U)
#define RGX_CR_SOFT_RESET2_MASKFULL (IMG_UINT64_C(0x00000000001FFFFF))
#define RGX_CR_SOFT_RESET2_SPFILTER_SHIFT (12U)
#define RGX_CR_SOFT_RESET2_SPFILTER_CLRMSK (0xFFE00FFFU)
#define RGX_CR_SOFT_RESET2_TDM_SHIFT (11U)
#define RGX_CR_SOFT_RESET2_TDM_CLRMSK (0xFFFFF7FFU)
#define RGX_CR_SOFT_RESET2_TDM_EN (0x00000800U)
#define RGX_CR_SOFT_RESET2_ASTC_SHIFT (10U)
#define RGX_CR_SOFT_RESET2_ASTC_CLRMSK (0xFFFFFBFFU)
#define RGX_CR_SOFT_RESET2_ASTC_EN (0x00000400U)
#define RGX_CR_SOFT_RESET2_BLACKPEARL_SHIFT (9U)
#define RGX_CR_SOFT_RESET2_BLACKPEARL_CLRMSK (0xFFFFFDFFU)
#define RGX_CR_SOFT_RESET2_BLACKPEARL_EN (0x00000200U)
#define RGX_CR_SOFT_RESET2_USCPS_SHIFT (8U)
#define RGX_CR_SOFT_RESET2_USCPS_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_SOFT_RESET2_USCPS_EN (0x00000100U)
#define RGX_CR_SOFT_RESET2_IPF_SHIFT (7U)
#define RGX_CR_SOFT_RESET2_IPF_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_SOFT_RESET2_IPF_EN (0x00000080U)
#define RGX_CR_SOFT_RESET2_GEOMETRY_SHIFT (6U)
#define RGX_CR_SOFT_RESET2_GEOMETRY_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_SOFT_RESET2_GEOMETRY_EN (0x00000040U)
#define RGX_CR_SOFT_RESET2_USC_SHARED_SHIFT (5U)
#define RGX_CR_SOFT_RESET2_USC_SHARED_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_SOFT_RESET2_USC_SHARED_EN (0x00000020U)
#define RGX_CR_SOFT_RESET2_PDS_SHARED_SHIFT (4U)
#define RGX_CR_SOFT_RESET2_PDS_SHARED_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_SOFT_RESET2_PDS_SHARED_EN (0x00000010U)
#define RGX_CR_SOFT_RESET2_BIF_BLACKPEARL_SHIFT (3U)
#define RGX_CR_SOFT_RESET2_BIF_BLACKPEARL_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_SOFT_RESET2_BIF_BLACKPEARL_EN (0x00000008U)
#define RGX_CR_SOFT_RESET2_PIXEL_SHIFT (2U)
#define RGX_CR_SOFT_RESET2_PIXEL_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_SOFT_RESET2_PIXEL_EN (0x00000004U)
#define RGX_CR_SOFT_RESET2_CDM_SHIFT (1U)
#define RGX_CR_SOFT_RESET2_CDM_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SOFT_RESET2_CDM_EN (0x00000002U)
#define RGX_CR_SOFT_RESET2_VERTEX_SHIFT (0U)
#define RGX_CR_SOFT_RESET2_VERTEX_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SOFT_RESET2_VERTEX_EN (0x00000001U)

/*
    Register RGX_CR_EVENT_ENABLE
*/
#define RGX_CR_EVENT_ENABLE (0x0128U)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__MASKFULL \
	(IMG_UINT64_C(0x00000000E01DFFFF))
#define RGX_CR_EVENT_ENABLE__SIGNALS__MASKFULL \
	(IMG_UINT64_C(0x00000000E007FFFF))
#define RGX_CR_EVENT_ENABLE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_EVENT_ENABLE_TDM_FENCE_FINISHED_SHIFT (31U)
#define RGX_CR_EVENT_ENABLE_TDM_FENCE_FINISHED_CLRMSK (0x7FFFFFFFU)
#define RGX_CR_EVENT_ENABLE_TDM_FENCE_FINISHED_EN (0x80000000U)
#define RGX_CR_EVENT_ENABLE_TDM_BUFFER_STALL_SHIFT (30U)
#define RGX_CR_EVENT_ENABLE_TDM_BUFFER_STALL_CLRMSK (0xBFFFFFFFU)
#define RGX_CR_EVENT_ENABLE_TDM_BUFFER_STALL_EN (0x40000000U)
#define RGX_CR_EVENT_ENABLE_COMPUTE_SIGNAL_FAILURE_SHIFT (29U)
#define RGX_CR_EVENT_ENABLE_COMPUTE_SIGNAL_FAILURE_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_EVENT_ENABLE_COMPUTE_SIGNAL_FAILURE_EN (0x20000000U)
#define RGX_CR_EVENT_ENABLE_DPX_OUT_OF_MEMORY_SHIFT (28U)
#define RGX_CR_EVENT_ENABLE_DPX_OUT_OF_MEMORY_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_EVENT_ENABLE_DPX_OUT_OF_MEMORY_EN (0x10000000U)
#define RGX_CR_EVENT_ENABLE_DPX_MMU_PAGE_FAULT_SHIFT (27U)
#define RGX_CR_EVENT_ENABLE_DPX_MMU_PAGE_FAULT_CLRMSK (0xF7FFFFFFU)
#define RGX_CR_EVENT_ENABLE_DPX_MMU_PAGE_FAULT_EN (0x08000000U)
#define RGX_CR_EVENT_ENABLE_RPM_OUT_OF_MEMORY_SHIFT (26U)
#define RGX_CR_EVENT_ENABLE_RPM_OUT_OF_MEMORY_CLRMSK (0xFBFFFFFFU)
#define RGX_CR_EVENT_ENABLE_RPM_OUT_OF_MEMORY_EN (0x04000000U)
#define RGX_CR_EVENT_ENABLE_FBA_FC3_FINISHED_SHIFT (25U)
#define RGX_CR_EVENT_ENABLE_FBA_FC3_FINISHED_CLRMSK (0xFDFFFFFFU)
#define RGX_CR_EVENT_ENABLE_FBA_FC3_FINISHED_EN (0x02000000U)
#define RGX_CR_EVENT_ENABLE_FBA_FC2_FINISHED_SHIFT (24U)
#define RGX_CR_EVENT_ENABLE_FBA_FC2_FINISHED_CLRMSK (0xFEFFFFFFU)
#define RGX_CR_EVENT_ENABLE_FBA_FC2_FINISHED_EN (0x01000000U)
#define RGX_CR_EVENT_ENABLE_FBA_FC1_FINISHED_SHIFT (23U)
#define RGX_CR_EVENT_ENABLE_FBA_FC1_FINISHED_CLRMSK (0xFF7FFFFFU)
#define RGX_CR_EVENT_ENABLE_FBA_FC1_FINISHED_EN (0x00800000U)
#define RGX_CR_EVENT_ENABLE_FBA_FC0_FINISHED_SHIFT (22U)
#define RGX_CR_EVENT_ENABLE_FBA_FC0_FINISHED_CLRMSK (0xFFBFFFFFU)
#define RGX_CR_EVENT_ENABLE_FBA_FC0_FINISHED_EN (0x00400000U)
#define RGX_CR_EVENT_ENABLE_RDM_FC3_FINISHED_SHIFT (21U)
#define RGX_CR_EVENT_ENABLE_RDM_FC3_FINISHED_CLRMSK (0xFFDFFFFFU)
#define RGX_CR_EVENT_ENABLE_RDM_FC3_FINISHED_EN (0x00200000U)
#define RGX_CR_EVENT_ENABLE_RDM_FC2_FINISHED_SHIFT (20U)
#define RGX_CR_EVENT_ENABLE_RDM_FC2_FINISHED_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_EVENT_ENABLE_RDM_FC2_FINISHED_EN (0x00100000U)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__SAFETY_SHIFT (20U)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__SAFETY_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__SAFETY_EN (0x00100000U)
#define RGX_CR_EVENT_ENABLE_RDM_FC1_FINISHED_SHIFT (19U)
#define RGX_CR_EVENT_ENABLE_RDM_FC1_FINISHED_CLRMSK (0xFFF7FFFFU)
#define RGX_CR_EVENT_ENABLE_RDM_FC1_FINISHED_EN (0x00080000U)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__SLAVE_REQ_SHIFT (19U)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__SLAVE_REQ_CLRMSK (0xFFF7FFFFU)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__SLAVE_REQ_EN (0x00080000U)
#define RGX_CR_EVENT_ENABLE_RDM_FC0_FINISHED_SHIFT (18U)
#define RGX_CR_EVENT_ENABLE_RDM_FC0_FINISHED_CLRMSK (0xFFFBFFFFU)
#define RGX_CR_EVENT_ENABLE_RDM_FC0_FINISHED_EN (0x00040000U)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__TDM_CONTEXT_STORE_FINISHED_SHIFT (18U)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__TDM_CONTEXT_STORE_FINISHED_CLRMSK \
	(0xFFFBFFFFU)
#define RGX_CR_EVENT_ENABLE__ROGUEXE__TDM_CONTEXT_STORE_FINISHED_EN \
	(0x00040000U)
#define RGX_CR_EVENT_ENABLE__SIGNALS__TDM_CONTEXT_STORE_FINISHED_SHIFT (18U)
#define RGX_CR_EVENT_ENABLE__SIGNALS__TDM_CONTEXT_STORE_FINISHED_CLRMSK \
	(0xFFFBFFFFU)
#define RGX_CR_EVENT_ENABLE__SIGNALS__TDM_CONTEXT_STORE_FINISHED_EN \
	(0x00040000U)
#define RGX_CR_EVENT_ENABLE_SHG_FINISHED_SHIFT (17U)
#define RGX_CR_EVENT_ENABLE_SHG_FINISHED_CLRMSK (0xFFFDFFFFU)
#define RGX_CR_EVENT_ENABLE_SHG_FINISHED_EN (0x00020000U)
#define RGX_CR_EVENT_ENABLE__SIGNALS__SPFILTER_SIGNAL_UPDATE_SHIFT (17U)
#define RGX_CR_EVENT_ENABLE__SIGNALS__SPFILTER_SIGNAL_UPDATE_CLRMSK \
	(0xFFFDFFFFU)
#define RGX_CR_EVENT_ENABLE__SIGNALS__SPFILTER_SIGNAL_UPDATE_EN (0x00020000U)
#define RGX_CR_EVENT_ENABLE_COMPUTE_BUFFER_STALL_SHIFT (16U)
#define RGX_CR_EVENT_ENABLE_COMPUTE_BUFFER_STALL_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_EVENT_ENABLE_COMPUTE_BUFFER_STALL_EN (0x00010000U)
#define RGX_CR_EVENT_ENABLE_USC_TRIGGER_SHIFT (15U)
#define RGX_CR_EVENT_ENABLE_USC_TRIGGER_CLRMSK (0xFFFF7FFFU)
#define RGX_CR_EVENT_ENABLE_USC_TRIGGER_EN (0x00008000U)
#define RGX_CR_EVENT_ENABLE_ZLS_FINISHED_SHIFT (14U)
#define RGX_CR_EVENT_ENABLE_ZLS_FINISHED_CLRMSK (0xFFFFBFFFU)
#define RGX_CR_EVENT_ENABLE_ZLS_FINISHED_EN (0x00004000U)
#define RGX_CR_EVENT_ENABLE_GPIO_ACK_SHIFT (13U)
#define RGX_CR_EVENT_ENABLE_GPIO_ACK_CLRMSK (0xFFFFDFFFU)
#define RGX_CR_EVENT_ENABLE_GPIO_ACK_EN (0x00002000U)
#define RGX_CR_EVENT_ENABLE_GPIO_REQ_SHIFT (12U)
#define RGX_CR_EVENT_ENABLE_GPIO_REQ_CLRMSK (0xFFFFEFFFU)
#define RGX_CR_EVENT_ENABLE_GPIO_REQ_EN (0x00001000U)
#define RGX_CR_EVENT_ENABLE_POWER_ABORT_SHIFT (11U)
#define RGX_CR_EVENT_ENABLE_POWER_ABORT_CLRMSK (0xFFFFF7FFU)
#define RGX_CR_EVENT_ENABLE_POWER_ABORT_EN (0x00000800U)
#define RGX_CR_EVENT_ENABLE_POWER_COMPLETE_SHIFT (10U)
#define RGX_CR_EVENT_ENABLE_POWER_COMPLETE_CLRMSK (0xFFFFFBFFU)
#define RGX_CR_EVENT_ENABLE_POWER_COMPLETE_EN (0x00000400U)
#define RGX_CR_EVENT_ENABLE_MMU_PAGE_FAULT_SHIFT (9U)
#define RGX_CR_EVENT_ENABLE_MMU_PAGE_FAULT_CLRMSK (0xFFFFFDFFU)
#define RGX_CR_EVENT_ENABLE_MMU_PAGE_FAULT_EN (0x00000200U)
#define RGX_CR_EVENT_ENABLE_PM_3D_MEM_FREE_SHIFT (8U)
#define RGX_CR_EVENT_ENABLE_PM_3D_MEM_FREE_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_EVENT_ENABLE_PM_3D_MEM_FREE_EN (0x00000100U)
#define RGX_CR_EVENT_ENABLE_PM_OUT_OF_MEMORY_SHIFT (7U)
#define RGX_CR_EVENT_ENABLE_PM_OUT_OF_MEMORY_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_EVENT_ENABLE_PM_OUT_OF_MEMORY_EN (0x00000080U)
#define RGX_CR_EVENT_ENABLE_TA_TERMINATE_SHIFT (6U)
#define RGX_CR_EVENT_ENABLE_TA_TERMINATE_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_EVENT_ENABLE_TA_TERMINATE_EN (0x00000040U)
#define RGX_CR_EVENT_ENABLE_TA_FINISHED_SHIFT (5U)
#define RGX_CR_EVENT_ENABLE_TA_FINISHED_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_EVENT_ENABLE_TA_FINISHED_EN (0x00000020U)
#define RGX_CR_EVENT_ENABLE_ISP_END_MACROTILE_SHIFT (4U)
#define RGX_CR_EVENT_ENABLE_ISP_END_MACROTILE_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_EVENT_ENABLE_ISP_END_MACROTILE_EN (0x00000010U)
#define RGX_CR_EVENT_ENABLE_PIXELBE_END_RENDER_SHIFT (3U)
#define RGX_CR_EVENT_ENABLE_PIXELBE_END_RENDER_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_EVENT_ENABLE_PIXELBE_END_RENDER_EN (0x00000008U)
#define RGX_CR_EVENT_ENABLE_COMPUTE_FINISHED_SHIFT (2U)
#define RGX_CR_EVENT_ENABLE_COMPUTE_FINISHED_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_EVENT_ENABLE_COMPUTE_FINISHED_EN (0x00000004U)
#define RGX_CR_EVENT_ENABLE_KERNEL_FINISHED_SHIFT (1U)
#define RGX_CR_EVENT_ENABLE_KERNEL_FINISHED_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_EVENT_ENABLE_KERNEL_FINISHED_EN (0x00000002U)
#define RGX_CR_EVENT_ENABLE_TLA_COMPLETE_SHIFT (0U)
#define RGX_CR_EVENT_ENABLE_TLA_COMPLETE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_EVENT_ENABLE_TLA_COMPLETE_EN (0x00000001U)

/*
    Register RGX_CR_EVENT_STATUS
*/
#define RGX_CR_EVENT_STATUS (0x0130U)
#define RGX_CR_EVENT_STATUS__ROGUEXE__MASKFULL \
	(IMG_UINT64_C(0x00000000E01DFFFF))
#define RGX_CR_EVENT_STATUS__SIGNALS__MASKFULL \
	(IMG_UINT64_C(0x00000000E007FFFF))
#define RGX_CR_EVENT_STATUS_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_EVENT_STATUS_TDM_FENCE_FINISHED_SHIFT (31U)
#define RGX_CR_EVENT_STATUS_TDM_FENCE_FINISHED_CLRMSK (0x7FFFFFFFU)
#define RGX_CR_EVENT_STATUS_TDM_FENCE_FINISHED_EN (0x80000000U)
#define RGX_CR_EVENT_STATUS_TDM_BUFFER_STALL_SHIFT (30U)
#define RGX_CR_EVENT_STATUS_TDM_BUFFER_STALL_CLRMSK (0xBFFFFFFFU)
#define RGX_CR_EVENT_STATUS_TDM_BUFFER_STALL_EN (0x40000000U)
#define RGX_CR_EVENT_STATUS_COMPUTE_SIGNAL_FAILURE_SHIFT (29U)
#define RGX_CR_EVENT_STATUS_COMPUTE_SIGNAL_FAILURE_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_EVENT_STATUS_COMPUTE_SIGNAL_FAILURE_EN (0x20000000U)
#define RGX_CR_EVENT_STATUS_DPX_OUT_OF_MEMORY_SHIFT (28U)
#define RGX_CR_EVENT_STATUS_DPX_OUT_OF_MEMORY_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_EVENT_STATUS_DPX_OUT_OF_MEMORY_EN (0x10000000U)
#define RGX_CR_EVENT_STATUS_DPX_MMU_PAGE_FAULT_SHIFT (27U)
#define RGX_CR_EVENT_STATUS_DPX_MMU_PAGE_FAULT_CLRMSK (0xF7FFFFFFU)
#define RGX_CR_EVENT_STATUS_DPX_MMU_PAGE_FAULT_EN (0x08000000U)
#define RGX_CR_EVENT_STATUS_RPM_OUT_OF_MEMORY_SHIFT (26U)
#define RGX_CR_EVENT_STATUS_RPM_OUT_OF_MEMORY_CLRMSK (0xFBFFFFFFU)
#define RGX_CR_EVENT_STATUS_RPM_OUT_OF_MEMORY_EN (0x04000000U)
#define RGX_CR_EVENT_STATUS_FBA_FC3_FINISHED_SHIFT (25U)
#define RGX_CR_EVENT_STATUS_FBA_FC3_FINISHED_CLRMSK (0xFDFFFFFFU)
#define RGX_CR_EVENT_STATUS_FBA_FC3_FINISHED_EN (0x02000000U)
#define RGX_CR_EVENT_STATUS_FBA_FC2_FINISHED_SHIFT (24U)
#define RGX_CR_EVENT_STATUS_FBA_FC2_FINISHED_CLRMSK (0xFEFFFFFFU)
#define RGX_CR_EVENT_STATUS_FBA_FC2_FINISHED_EN (0x01000000U)
#define RGX_CR_EVENT_STATUS_FBA_FC1_FINISHED_SHIFT (23U)
#define RGX_CR_EVENT_STATUS_FBA_FC1_FINISHED_CLRMSK (0xFF7FFFFFU)
#define RGX_CR_EVENT_STATUS_FBA_FC1_FINISHED_EN (0x00800000U)
#define RGX_CR_EVENT_STATUS_FBA_FC0_FINISHED_SHIFT (22U)
#define RGX_CR_EVENT_STATUS_FBA_FC0_FINISHED_CLRMSK (0xFFBFFFFFU)
#define RGX_CR_EVENT_STATUS_FBA_FC0_FINISHED_EN (0x00400000U)
#define RGX_CR_EVENT_STATUS_RDM_FC3_FINISHED_SHIFT (21U)
#define RGX_CR_EVENT_STATUS_RDM_FC3_FINISHED_CLRMSK (0xFFDFFFFFU)
#define RGX_CR_EVENT_STATUS_RDM_FC3_FINISHED_EN (0x00200000U)
#define RGX_CR_EVENT_STATUS_RDM_FC2_FINISHED_SHIFT (20U)
#define RGX_CR_EVENT_STATUS_RDM_FC2_FINISHED_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_EVENT_STATUS_RDM_FC2_FINISHED_EN (0x00100000U)
#define RGX_CR_EVENT_STATUS__ROGUEXE__SAFETY_SHIFT (20U)
#define RGX_CR_EVENT_STATUS__ROGUEXE__SAFETY_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_EVENT_STATUS__ROGUEXE__SAFETY_EN (0x00100000U)
#define RGX_CR_EVENT_STATUS_RDM_FC1_FINISHED_SHIFT (19U)
#define RGX_CR_EVENT_STATUS_RDM_FC1_FINISHED_CLRMSK (0xFFF7FFFFU)
#define RGX_CR_EVENT_STATUS_RDM_FC1_FINISHED_EN (0x00080000U)
#define RGX_CR_EVENT_STATUS__ROGUEXE__SLAVE_REQ_SHIFT (19U)
#define RGX_CR_EVENT_STATUS__ROGUEXE__SLAVE_REQ_CLRMSK (0xFFF7FFFFU)
#define RGX_CR_EVENT_STATUS__ROGUEXE__SLAVE_REQ_EN (0x00080000U)
#define RGX_CR_EVENT_STATUS_RDM_FC0_FINISHED_SHIFT (18U)
#define RGX_CR_EVENT_STATUS_RDM_FC0_FINISHED_CLRMSK (0xFFFBFFFFU)
#define RGX_CR_EVENT_STATUS_RDM_FC0_FINISHED_EN (0x00040000U)
#define RGX_CR_EVENT_STATUS__ROGUEXE__TDM_CONTEXT_STORE_FINISHED_SHIFT (18U)
#define RGX_CR_EVENT_STATUS__ROGUEXE__TDM_CONTEXT_STORE_FINISHED_CLRMSK \
	(0xFFFBFFFFU)
#define RGX_CR_EVENT_STATUS__ROGUEXE__TDM_CONTEXT_STORE_FINISHED_EN \
	(0x00040000U)
#define RGX_CR_EVENT_STATUS__SIGNALS__TDM_CONTEXT_STORE_FINISHED_SHIFT (18U)
#define RGX_CR_EVENT_STATUS__SIGNALS__TDM_CONTEXT_STORE_FINISHED_CLRMSK \
	(0xFFFBFFFFU)
#define RGX_CR_EVENT_STATUS__SIGNALS__TDM_CONTEXT_STORE_FINISHED_EN \
	(0x00040000U)
#define RGX_CR_EVENT_STATUS_SHG_FINISHED_SHIFT (17U)
#define RGX_CR_EVENT_STATUS_SHG_FINISHED_CLRMSK (0xFFFDFFFFU)
#define RGX_CR_EVENT_STATUS_SHG_FINISHED_EN (0x00020000U)
#define RGX_CR_EVENT_STATUS__SIGNALS__SPFILTER_SIGNAL_UPDATE_SHIFT (17U)
#define RGX_CR_EVENT_STATUS__SIGNALS__SPFILTER_SIGNAL_UPDATE_CLRMSK \
	(0xFFFDFFFFU)
#define RGX_CR_EVENT_STATUS__SIGNALS__SPFILTER_SIGNAL_UPDATE_EN (0x00020000U)
#define RGX_CR_EVENT_STATUS_COMPUTE_BUFFER_STALL_SHIFT (16U)
#define RGX_CR_EVENT_STATUS_COMPUTE_BUFFER_STALL_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_EVENT_STATUS_COMPUTE_BUFFER_STALL_EN (0x00010000U)
#define RGX_CR_EVENT_STATUS_USC_TRIGGER_SHIFT (15U)
#define RGX_CR_EVENT_STATUS_USC_TRIGGER_CLRMSK (0xFFFF7FFFU)
#define RGX_CR_EVENT_STATUS_USC_TRIGGER_EN (0x00008000U)
#define RGX_CR_EVENT_STATUS_ZLS_FINISHED_SHIFT (14U)
#define RGX_CR_EVENT_STATUS_ZLS_FINISHED_CLRMSK (0xFFFFBFFFU)
#define RGX_CR_EVENT_STATUS_ZLS_FINISHED_EN (0x00004000U)
#define RGX_CR_EVENT_STATUS_GPIO_ACK_SHIFT (13U)
#define RGX_CR_EVENT_STATUS_GPIO_ACK_CLRMSK (0xFFFFDFFFU)
#define RGX_CR_EVENT_STATUS_GPIO_ACK_EN (0x00002000U)
#define RGX_CR_EVENT_STATUS_GPIO_REQ_SHIFT (12U)
#define RGX_CR_EVENT_STATUS_GPIO_REQ_CLRMSK (0xFFFFEFFFU)
#define RGX_CR_EVENT_STATUS_GPIO_REQ_EN (0x00001000U)
#define RGX_CR_EVENT_STATUS_POWER_ABORT_SHIFT (11U)
#define RGX_CR_EVENT_STATUS_POWER_ABORT_CLRMSK (0xFFFFF7FFU)
#define RGX_CR_EVENT_STATUS_POWER_ABORT_EN (0x00000800U)
#define RGX_CR_EVENT_STATUS_POWER_COMPLETE_SHIFT (10U)
#define RGX_CR_EVENT_STATUS_POWER_COMPLETE_CLRMSK (0xFFFFFBFFU)
#define RGX_CR_EVENT_STATUS_POWER_COMPLETE_EN (0x00000400U)
#define RGX_CR_EVENT_STATUS_MMU_PAGE_FAULT_SHIFT (9U)
#define RGX_CR_EVENT_STATUS_MMU_PAGE_FAULT_CLRMSK (0xFFFFFDFFU)
#define RGX_CR_EVENT_STATUS_MMU_PAGE_FAULT_EN (0x00000200U)
#define RGX_CR_EVENT_STATUS_PM_3D_MEM_FREE_SHIFT (8U)
#define RGX_CR_EVENT_STATUS_PM_3D_MEM_FREE_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_EVENT_STATUS_PM_3D_MEM_FREE_EN (0x00000100U)
#define RGX_CR_EVENT_STATUS_PM_OUT_OF_MEMORY_SHIFT (7U)
#define RGX_CR_EVENT_STATUS_PM_OUT_OF_MEMORY_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_EVENT_STATUS_PM_OUT_OF_MEMORY_EN (0x00000080U)
#define RGX_CR_EVENT_STATUS_TA_TERMINATE_SHIFT (6U)
#define RGX_CR_EVENT_STATUS_TA_TERMINATE_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_EVENT_STATUS_TA_TERMINATE_EN (0x00000040U)
#define RGX_CR_EVENT_STATUS_TA_FINISHED_SHIFT (5U)
#define RGX_CR_EVENT_STATUS_TA_FINISHED_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_EVENT_STATUS_TA_FINISHED_EN (0x00000020U)
#define RGX_CR_EVENT_STATUS_ISP_END_MACROTILE_SHIFT (4U)
#define RGX_CR_EVENT_STATUS_ISP_END_MACROTILE_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_EVENT_STATUS_ISP_END_MACROTILE_EN (0x00000010U)
#define RGX_CR_EVENT_STATUS_PIXELBE_END_RENDER_SHIFT (3U)
#define RGX_CR_EVENT_STATUS_PIXELBE_END_RENDER_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_EVENT_STATUS_PIXELBE_END_RENDER_EN (0x00000008U)
#define RGX_CR_EVENT_STATUS_COMPUTE_FINISHED_SHIFT (2U)
#define RGX_CR_EVENT_STATUS_COMPUTE_FINISHED_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_EVENT_STATUS_COMPUTE_FINISHED_EN (0x00000004U)
#define RGX_CR_EVENT_STATUS_KERNEL_FINISHED_SHIFT (1U)
#define RGX_CR_EVENT_STATUS_KERNEL_FINISHED_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_EVENT_STATUS_KERNEL_FINISHED_EN (0x00000002U)
#define RGX_CR_EVENT_STATUS_TLA_COMPLETE_SHIFT (0U)
#define RGX_CR_EVENT_STATUS_TLA_COMPLETE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_EVENT_STATUS_TLA_COMPLETE_EN (0x00000001U)

/*
    Register RGX_CR_EVENT_CLEAR
*/
#define RGX_CR_EVENT_CLEAR (0x0138U)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__MASKFULL (IMG_UINT64_C(0x00000000E01DFFFF))
#define RGX_CR_EVENT_CLEAR__SIGNALS__MASKFULL (IMG_UINT64_C(0x00000000E007FFFF))
#define RGX_CR_EVENT_CLEAR_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_EVENT_CLEAR_TDM_FENCE_FINISHED_SHIFT (31U)
#define RGX_CR_EVENT_CLEAR_TDM_FENCE_FINISHED_CLRMSK (0x7FFFFFFFU)
#define RGX_CR_EVENT_CLEAR_TDM_FENCE_FINISHED_EN (0x80000000U)
#define RGX_CR_EVENT_CLEAR_TDM_BUFFER_STALL_SHIFT (30U)
#define RGX_CR_EVENT_CLEAR_TDM_BUFFER_STALL_CLRMSK (0xBFFFFFFFU)
#define RGX_CR_EVENT_CLEAR_TDM_BUFFER_STALL_EN (0x40000000U)
#define RGX_CR_EVENT_CLEAR_COMPUTE_SIGNAL_FAILURE_SHIFT (29U)
#define RGX_CR_EVENT_CLEAR_COMPUTE_SIGNAL_FAILURE_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_EVENT_CLEAR_COMPUTE_SIGNAL_FAILURE_EN (0x20000000U)
#define RGX_CR_EVENT_CLEAR_DPX_OUT_OF_MEMORY_SHIFT (28U)
#define RGX_CR_EVENT_CLEAR_DPX_OUT_OF_MEMORY_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_EVENT_CLEAR_DPX_OUT_OF_MEMORY_EN (0x10000000U)
#define RGX_CR_EVENT_CLEAR_DPX_MMU_PAGE_FAULT_SHIFT (27U)
#define RGX_CR_EVENT_CLEAR_DPX_MMU_PAGE_FAULT_CLRMSK (0xF7FFFFFFU)
#define RGX_CR_EVENT_CLEAR_DPX_MMU_PAGE_FAULT_EN (0x08000000U)
#define RGX_CR_EVENT_CLEAR_RPM_OUT_OF_MEMORY_SHIFT (26U)
#define RGX_CR_EVENT_CLEAR_RPM_OUT_OF_MEMORY_CLRMSK (0xFBFFFFFFU)
#define RGX_CR_EVENT_CLEAR_RPM_OUT_OF_MEMORY_EN (0x04000000U)
#define RGX_CR_EVENT_CLEAR_FBA_FC3_FINISHED_SHIFT (25U)
#define RGX_CR_EVENT_CLEAR_FBA_FC3_FINISHED_CLRMSK (0xFDFFFFFFU)
#define RGX_CR_EVENT_CLEAR_FBA_FC3_FINISHED_EN (0x02000000U)
#define RGX_CR_EVENT_CLEAR_FBA_FC2_FINISHED_SHIFT (24U)
#define RGX_CR_EVENT_CLEAR_FBA_FC2_FINISHED_CLRMSK (0xFEFFFFFFU)
#define RGX_CR_EVENT_CLEAR_FBA_FC2_FINISHED_EN (0x01000000U)
#define RGX_CR_EVENT_CLEAR_FBA_FC1_FINISHED_SHIFT (23U)
#define RGX_CR_EVENT_CLEAR_FBA_FC1_FINISHED_CLRMSK (0xFF7FFFFFU)
#define RGX_CR_EVENT_CLEAR_FBA_FC1_FINISHED_EN (0x00800000U)
#define RGX_CR_EVENT_CLEAR_FBA_FC0_FINISHED_SHIFT (22U)
#define RGX_CR_EVENT_CLEAR_FBA_FC0_FINISHED_CLRMSK (0xFFBFFFFFU)
#define RGX_CR_EVENT_CLEAR_FBA_FC0_FINISHED_EN (0x00400000U)
#define RGX_CR_EVENT_CLEAR_RDM_FC3_FINISHED_SHIFT (21U)
#define RGX_CR_EVENT_CLEAR_RDM_FC3_FINISHED_CLRMSK (0xFFDFFFFFU)
#define RGX_CR_EVENT_CLEAR_RDM_FC3_FINISHED_EN (0x00200000U)
#define RGX_CR_EVENT_CLEAR_RDM_FC2_FINISHED_SHIFT (20U)
#define RGX_CR_EVENT_CLEAR_RDM_FC2_FINISHED_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_EVENT_CLEAR_RDM_FC2_FINISHED_EN (0x00100000U)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__SAFETY_SHIFT (20U)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__SAFETY_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__SAFETY_EN (0x00100000U)
#define RGX_CR_EVENT_CLEAR_RDM_FC1_FINISHED_SHIFT (19U)
#define RGX_CR_EVENT_CLEAR_RDM_FC1_FINISHED_CLRMSK (0xFFF7FFFFU)
#define RGX_CR_EVENT_CLEAR_RDM_FC1_FINISHED_EN (0x00080000U)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__SLAVE_REQ_SHIFT (19U)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__SLAVE_REQ_CLRMSK (0xFFF7FFFFU)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__SLAVE_REQ_EN (0x00080000U)
#define RGX_CR_EVENT_CLEAR_RDM_FC0_FINISHED_SHIFT (18U)
#define RGX_CR_EVENT_CLEAR_RDM_FC0_FINISHED_CLRMSK (0xFFFBFFFFU)
#define RGX_CR_EVENT_CLEAR_RDM_FC0_FINISHED_EN (0x00040000U)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__TDM_CONTEXT_STORE_FINISHED_SHIFT (18U)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__TDM_CONTEXT_STORE_FINISHED_CLRMSK \
	(0xFFFBFFFFU)
#define RGX_CR_EVENT_CLEAR__ROGUEXE__TDM_CONTEXT_STORE_FINISHED_EN (0x00040000U)
#define RGX_CR_EVENT_CLEAR__SIGNALS__TDM_CONTEXT_STORE_FINISHED_SHIFT (18U)
#define RGX_CR_EVENT_CLEAR__SIGNALS__TDM_CONTEXT_STORE_FINISHED_CLRMSK \
	(0xFFFBFFFFU)
#define RGX_CR_EVENT_CLEAR__SIGNALS__TDM_CONTEXT_STORE_FINISHED_EN (0x00040000U)
#define RGX_CR_EVENT_CLEAR_SHG_FINISHED_SHIFT (17U)
#define RGX_CR_EVENT_CLEAR_SHG_FINISHED_CLRMSK (0xFFFDFFFFU)
#define RGX_CR_EVENT_CLEAR_SHG_FINISHED_EN (0x00020000U)
#define RGX_CR_EVENT_CLEAR__SIGNALS__SPFILTER_SIGNAL_UPDATE_SHIFT (17U)
#define RGX_CR_EVENT_CLEAR__SIGNALS__SPFILTER_SIGNAL_UPDATE_CLRMSK (0xFFFDFFFFU)
#define RGX_CR_EVENT_CLEAR__SIGNALS__SPFILTER_SIGNAL_UPDATE_EN (0x00020000U)
#define RGX_CR_EVENT_CLEAR_COMPUTE_BUFFER_STALL_SHIFT (16U)
#define RGX_CR_EVENT_CLEAR_COMPUTE_BUFFER_STALL_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_EVENT_CLEAR_COMPUTE_BUFFER_STALL_EN (0x00010000U)
#define RGX_CR_EVENT_CLEAR_USC_TRIGGER_SHIFT (15U)
#define RGX_CR_EVENT_CLEAR_USC_TRIGGER_CLRMSK (0xFFFF7FFFU)
#define RGX_CR_EVENT_CLEAR_USC_TRIGGER_EN (0x00008000U)
#define RGX_CR_EVENT_CLEAR_ZLS_FINISHED_SHIFT (14U)
#define RGX_CR_EVENT_CLEAR_ZLS_FINISHED_CLRMSK (0xFFFFBFFFU)
#define RGX_CR_EVENT_CLEAR_ZLS_FINISHED_EN (0x00004000U)
#define RGX_CR_EVENT_CLEAR_GPIO_ACK_SHIFT (13U)
#define RGX_CR_EVENT_CLEAR_GPIO_ACK_CLRMSK (0xFFFFDFFFU)
#define RGX_CR_EVENT_CLEAR_GPIO_ACK_EN (0x00002000U)
#define RGX_CR_EVENT_CLEAR_GPIO_REQ_SHIFT (12U)
#define RGX_CR_EVENT_CLEAR_GPIO_REQ_CLRMSK (0xFFFFEFFFU)
#define RGX_CR_EVENT_CLEAR_GPIO_REQ_EN (0x00001000U)
#define RGX_CR_EVENT_CLEAR_POWER_ABORT_SHIFT (11U)
#define RGX_CR_EVENT_CLEAR_POWER_ABORT_CLRMSK (0xFFFFF7FFU)
#define RGX_CR_EVENT_CLEAR_POWER_ABORT_EN (0x00000800U)
#define RGX_CR_EVENT_CLEAR_POWER_COMPLETE_SHIFT (10U)
#define RGX_CR_EVENT_CLEAR_POWER_COMPLETE_CLRMSK (0xFFFFFBFFU)
#define RGX_CR_EVENT_CLEAR_POWER_COMPLETE_EN (0x00000400U)
#define RGX_CR_EVENT_CLEAR_MMU_PAGE_FAULT_SHIFT (9U)
#define RGX_CR_EVENT_CLEAR_MMU_PAGE_FAULT_CLRMSK (0xFFFFFDFFU)
#define RGX_CR_EVENT_CLEAR_MMU_PAGE_FAULT_EN (0x00000200U)
#define RGX_CR_EVENT_CLEAR_PM_3D_MEM_FREE_SHIFT (8U)
#define RGX_CR_EVENT_CLEAR_PM_3D_MEM_FREE_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_EVENT_CLEAR_PM_3D_MEM_FREE_EN (0x00000100U)
#define RGX_CR_EVENT_CLEAR_PM_OUT_OF_MEMORY_SHIFT (7U)
#define RGX_CR_EVENT_CLEAR_PM_OUT_OF_MEMORY_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_EVENT_CLEAR_PM_OUT_OF_MEMORY_EN (0x00000080U)
#define RGX_CR_EVENT_CLEAR_TA_TERMINATE_SHIFT (6U)
#define RGX_CR_EVENT_CLEAR_TA_TERMINATE_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_EVENT_CLEAR_TA_TERMINATE_EN (0x00000040U)
#define RGX_CR_EVENT_CLEAR_TA_FINISHED_SHIFT (5U)
#define RGX_CR_EVENT_CLEAR_TA_FINISHED_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_EVENT_CLEAR_TA_FINISHED_EN (0x00000020U)
#define RGX_CR_EVENT_CLEAR_ISP_END_MACROTILE_SHIFT (4U)
#define RGX_CR_EVENT_CLEAR_ISP_END_MACROTILE_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_EVENT_CLEAR_ISP_END_MACROTILE_EN (0x00000010U)
#define RGX_CR_EVENT_CLEAR_PIXELBE_END_RENDER_SHIFT (3U)
#define RGX_CR_EVENT_CLEAR_PIXELBE_END_RENDER_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_EVENT_CLEAR_PIXELBE_END_RENDER_EN (0x00000008U)
#define RGX_CR_EVENT_CLEAR_COMPUTE_FINISHED_SHIFT (2U)
#define RGX_CR_EVENT_CLEAR_COMPUTE_FINISHED_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_EVENT_CLEAR_COMPUTE_FINISHED_EN (0x00000004U)
#define RGX_CR_EVENT_CLEAR_KERNEL_FINISHED_SHIFT (1U)
#define RGX_CR_EVENT_CLEAR_KERNEL_FINISHED_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_EVENT_CLEAR_KERNEL_FINISHED_EN (0x00000002U)
#define RGX_CR_EVENT_CLEAR_TLA_COMPLETE_SHIFT (0U)
#define RGX_CR_EVENT_CLEAR_TLA_COMPLETE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_EVENT_CLEAR_TLA_COMPLETE_EN (0x00000001U)

/*
    Register RGX_CR_TIMER
*/
#define RGX_CR_TIMER (0x0160U)
#define RGX_CR_TIMER_MASKFULL (IMG_UINT64_C(0x8000FFFFFFFFFFFF))
#define RGX_CR_TIMER_BIT31_SHIFT (63U)
#define RGX_CR_TIMER_BIT31_CLRMSK (IMG_UINT64_C(0x7FFFFFFFFFFFFFFF))
#define RGX_CR_TIMER_BIT31_EN (IMG_UINT64_C(0x8000000000000000))
#define RGX_CR_TIMER_VALUE_SHIFT (0U)
#define RGX_CR_TIMER_VALUE_CLRMSK (IMG_UINT64_C(0xFFFF000000000000))

/*
    Register RGX_CR_TLA_STATUS
*/
#define RGX_CR_TLA_STATUS (0x0178U)
#define RGX_CR_TLA_STATUS_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_TLA_STATUS_BLIT_COUNT_SHIFT (39U)
#define RGX_CR_TLA_STATUS_BLIT_COUNT_CLRMSK (IMG_UINT64_C(0x0000007FFFFFFFFF))
#define RGX_CR_TLA_STATUS_REQUEST_SHIFT (7U)
#define RGX_CR_TLA_STATUS_REQUEST_CLRMSK (IMG_UINT64_C(0xFFFFFF800000007F))
#define RGX_CR_TLA_STATUS_FIFO_FULLNESS_SHIFT (1U)
#define RGX_CR_TLA_STATUS_FIFO_FULLNESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFF81))
#define RGX_CR_TLA_STATUS_BUSY_SHIFT (0U)
#define RGX_CR_TLA_STATUS_BUSY_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_TLA_STATUS_BUSY_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_PM_PARTIAL_RENDER_ENABLE
*/
#define RGX_CR_PM_PARTIAL_RENDER_ENABLE (0x0338U)
#define RGX_CR_PM_PARTIAL_RENDER_ENABLE_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_PM_PARTIAL_RENDER_ENABLE_OP_SHIFT (0U)
#define RGX_CR_PM_PARTIAL_RENDER_ENABLE_OP_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_PM_PARTIAL_RENDER_ENABLE_OP_EN (0x00000001U)

/*
    Register RGX_CR_SIDEKICK_IDLE
*/
#define RGX_CR_SIDEKICK_IDLE (0x03C8U)
#define RGX_CR_SIDEKICK_IDLE_MASKFULL (IMG_UINT64_C(0x000000000000007F))
#define RGX_CR_SIDEKICK_IDLE_FB_CDC_SHIFT (6U)
#define RGX_CR_SIDEKICK_IDLE_FB_CDC_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_SIDEKICK_IDLE_FB_CDC_EN (0x00000040U)
#define RGX_CR_SIDEKICK_IDLE_MMU_SHIFT (5U)
#define RGX_CR_SIDEKICK_IDLE_MMU_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_SIDEKICK_IDLE_MMU_EN (0x00000020U)
#define RGX_CR_SIDEKICK_IDLE_BIF128_SHIFT (4U)
#define RGX_CR_SIDEKICK_IDLE_BIF128_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_SIDEKICK_IDLE_BIF128_EN (0x00000010U)
#define RGX_CR_SIDEKICK_IDLE_TLA_SHIFT (3U)
#define RGX_CR_SIDEKICK_IDLE_TLA_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_SIDEKICK_IDLE_TLA_EN (0x00000008U)
#define RGX_CR_SIDEKICK_IDLE_GARTEN_SHIFT (2U)
#define RGX_CR_SIDEKICK_IDLE_GARTEN_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_SIDEKICK_IDLE_GARTEN_EN (0x00000004U)
#define RGX_CR_SIDEKICK_IDLE_HOSTIF_SHIFT (1U)
#define RGX_CR_SIDEKICK_IDLE_HOSTIF_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SIDEKICK_IDLE_HOSTIF_EN (0x00000002U)
#define RGX_CR_SIDEKICK_IDLE_SOCIF_SHIFT (0U)
#define RGX_CR_SIDEKICK_IDLE_SOCIF_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SIDEKICK_IDLE_SOCIF_EN (0x00000001U)

/*
    Register RGX_CR_MARS_IDLE
*/
#define RGX_CR_MARS_IDLE (0x08F8U)
#define RGX_CR_MARS_IDLE_MASKFULL (IMG_UINT64_C(0x0000000000000007))
#define RGX_CR_MARS_IDLE_MH_SYSARB0_SHIFT (2U)
#define RGX_CR_MARS_IDLE_MH_SYSARB0_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_MARS_IDLE_MH_SYSARB0_EN (0x00000004U)
#define RGX_CR_MARS_IDLE_CPU_SHIFT (1U)
#define RGX_CR_MARS_IDLE_CPU_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_MARS_IDLE_CPU_EN (0x00000002U)
#define RGX_CR_MARS_IDLE_SOCIF_SHIFT (0U)
#define RGX_CR_MARS_IDLE_SOCIF_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MARS_IDLE_SOCIF_EN (0x00000001U)

/*
    Register RGX_CR_VDM_CONTEXT_STORE_STATUS
*/
#define RGX_CR_VDM_CONTEXT_STORE_STATUS (0x0430U)
#define RGX_CR_VDM_CONTEXT_STORE_STATUS_MASKFULL \
	(IMG_UINT64_C(0x00000000000000F3))
#define RGX_CR_VDM_CONTEXT_STORE_STATUS_LAST_PIPE_SHIFT (4U)
#define RGX_CR_VDM_CONTEXT_STORE_STATUS_LAST_PIPE_CLRMSK (0xFFFFFF0FU)
#define RGX_CR_VDM_CONTEXT_STORE_STATUS_NEED_RESUME_SHIFT (1U)
#define RGX_CR_VDM_CONTEXT_STORE_STATUS_NEED_RESUME_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_VDM_CONTEXT_STORE_STATUS_NEED_RESUME_EN (0x00000002U)
#define RGX_CR_VDM_CONTEXT_STORE_STATUS_COMPLETE_SHIFT (0U)
#define RGX_CR_VDM_CONTEXT_STORE_STATUS_COMPLETE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_VDM_CONTEXT_STORE_STATUS_COMPLETE_EN (0x00000001U)

/*
    Register RGX_CR_VDM_CONTEXT_STORE_TASK0
*/
#define RGX_CR_VDM_CONTEXT_STORE_TASK0 (0x0438U)
#define RGX_CR_VDM_CONTEXT_STORE_TASK0_MASKFULL \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_VDM_CONTEXT_STORE_TASK0_PDS_STATE1_SHIFT (32U)
#define RGX_CR_VDM_CONTEXT_STORE_TASK0_PDS_STATE1_CLRMSK \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_VDM_CONTEXT_STORE_TASK0_PDS_STATE0_SHIFT (0U)
#define RGX_CR_VDM_CONTEXT_STORE_TASK0_PDS_STATE0_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000000))

/*
    Register RGX_CR_VDM_CONTEXT_STORE_TASK1
*/
#define RGX_CR_VDM_CONTEXT_STORE_TASK1 (0x0440U)
#define RGX_CR_VDM_CONTEXT_STORE_TASK1_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_VDM_CONTEXT_STORE_TASK1_PDS_STATE2_SHIFT (0U)
#define RGX_CR_VDM_CONTEXT_STORE_TASK1_PDS_STATE2_CLRMSK (0x00000000U)

/*
    Register RGX_CR_VDM_CONTEXT_STORE_TASK2
*/
#define RGX_CR_VDM_CONTEXT_STORE_TASK2 (0x0448U)
#define RGX_CR_VDM_CONTEXT_STORE_TASK2_MASKFULL \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_VDM_CONTEXT_STORE_TASK2_STREAM_OUT2_SHIFT (32U)
#define RGX_CR_VDM_CONTEXT_STORE_TASK2_STREAM_OUT2_CLRMSK \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_VDM_CONTEXT_STORE_TASK2_STREAM_OUT1_SHIFT (0U)
#define RGX_CR_VDM_CONTEXT_STORE_TASK2_STREAM_OUT1_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000000))

/*
    Register RGX_CR_VDM_CONTEXT_RESUME_TASK0
*/
#define RGX_CR_VDM_CONTEXT_RESUME_TASK0 (0x0450U)
#define RGX_CR_VDM_CONTEXT_RESUME_TASK0_MASKFULL \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_VDM_CONTEXT_RESUME_TASK0_PDS_STATE1_SHIFT (32U)
#define RGX_CR_VDM_CONTEXT_RESUME_TASK0_PDS_STATE1_CLRMSK \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_VDM_CONTEXT_RESUME_TASK0_PDS_STATE0_SHIFT (0U)
#define RGX_CR_VDM_CONTEXT_RESUME_TASK0_PDS_STATE0_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000000))

/*
    Register RGX_CR_VDM_CONTEXT_RESUME_TASK1
*/
#define RGX_CR_VDM_CONTEXT_RESUME_TASK1 (0x0458U)
#define RGX_CR_VDM_CONTEXT_RESUME_TASK1_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_VDM_CONTEXT_RESUME_TASK1_PDS_STATE2_SHIFT (0U)
#define RGX_CR_VDM_CONTEXT_RESUME_TASK1_PDS_STATE2_CLRMSK (0x00000000U)

/*
    Register RGX_CR_VDM_CONTEXT_RESUME_TASK2
*/
#define RGX_CR_VDM_CONTEXT_RESUME_TASK2 (0x0460U)
#define RGX_CR_VDM_CONTEXT_RESUME_TASK2_MASKFULL \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_VDM_CONTEXT_RESUME_TASK2_STREAM_OUT2_SHIFT (32U)
#define RGX_CR_VDM_CONTEXT_RESUME_TASK2_STREAM_OUT2_CLRMSK \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_VDM_CONTEXT_RESUME_TASK2_STREAM_OUT1_SHIFT (0U)
#define RGX_CR_VDM_CONTEXT_RESUME_TASK2_STREAM_OUT1_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000000))

/*
    Register RGX_CR_CDM_CONTEXT_STORE_STATUS
*/
#define RGX_CR_CDM_CONTEXT_STORE_STATUS (0x04A0U)
#define RGX_CR_CDM_CONTEXT_STORE_STATUS_MASKFULL \
	(IMG_UINT64_C(0x0000000000000003))
#define RGX_CR_CDM_CONTEXT_STORE_STATUS_NEED_RESUME_SHIFT (1U)
#define RGX_CR_CDM_CONTEXT_STORE_STATUS_NEED_RESUME_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_CDM_CONTEXT_STORE_STATUS_NEED_RESUME_EN (0x00000002U)
#define RGX_CR_CDM_CONTEXT_STORE_STATUS_COMPLETE_SHIFT (0U)
#define RGX_CR_CDM_CONTEXT_STORE_STATUS_COMPLETE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_CDM_CONTEXT_STORE_STATUS_COMPLETE_EN (0x00000001U)

/*
    Register RGX_CR_CDM_CONTEXT_PDS0
*/
#define RGX_CR_CDM_CONTEXT_PDS0 (0x04A8U)
#define RGX_CR_CDM_CONTEXT_PDS0_MASKFULL (IMG_UINT64_C(0xFFFFFFF0FFFFFFF0))
#define RGX_CR_CDM_CONTEXT_PDS0_DATA_ADDR_SHIFT (36U)
#define RGX_CR_CDM_CONTEXT_PDS0_DATA_ADDR_CLRMSK \
	(IMG_UINT64_C(0x0000000FFFFFFFFF))
#define RGX_CR_CDM_CONTEXT_PDS0_DATA_ADDR_ALIGNSHIFT (4U)
#define RGX_CR_CDM_CONTEXT_PDS0_DATA_ADDR_ALIGNSIZE (16U)
#define RGX_CR_CDM_CONTEXT_PDS0_CODE_ADDR_SHIFT (4U)
#define RGX_CR_CDM_CONTEXT_PDS0_CODE_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF0000000F))
#define RGX_CR_CDM_CONTEXT_PDS0_CODE_ADDR_ALIGNSHIFT (4U)
#define RGX_CR_CDM_CONTEXT_PDS0_CODE_ADDR_ALIGNSIZE (16U)

/*
    Register RGX_CR_CDM_CONTEXT_PDS1
*/
#define RGX_CR_CDM_CONTEXT_PDS1 (0x04B0U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__MASKFULL \
	(IMG_UINT64_C(0x000000007FFFFFFF))
#define RGX_CR_CDM_CONTEXT_PDS1_MASKFULL (IMG_UINT64_C(0x000000003FFFFFFF))
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__PDS_SEQ_DEP_SHIFT (30U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__PDS_SEQ_DEP_CLRMSK (0xBFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__PDS_SEQ_DEP_EN (0x40000000U)
#define RGX_CR_CDM_CONTEXT_PDS1_PDS_SEQ_DEP_SHIFT (29U)
#define RGX_CR_CDM_CONTEXT_PDS1_PDS_SEQ_DEP_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1_PDS_SEQ_DEP_EN (0x20000000U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__USC_SEQ_DEP_SHIFT (29U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__USC_SEQ_DEP_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__USC_SEQ_DEP_EN (0x20000000U)
#define RGX_CR_CDM_CONTEXT_PDS1_USC_SEQ_DEP_SHIFT (28U)
#define RGX_CR_CDM_CONTEXT_PDS1_USC_SEQ_DEP_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1_USC_SEQ_DEP_EN (0x10000000U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__TARGET_SHIFT (28U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__TARGET_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__TARGET_EN (0x10000000U)
#define RGX_CR_CDM_CONTEXT_PDS1_TARGET_SHIFT (27U)
#define RGX_CR_CDM_CONTEXT_PDS1_TARGET_CLRMSK (0xF7FFFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1_TARGET_EN (0x08000000U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__UNIFIED_SIZE_SHIFT (22U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__UNIFIED_SIZE_CLRMSK (0xF03FFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1_UNIFIED_SIZE_SHIFT (21U)
#define RGX_CR_CDM_CONTEXT_PDS1_UNIFIED_SIZE_CLRMSK (0xF81FFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__COMMON_SHARED_SHIFT (21U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__COMMON_SHARED_CLRMSK (0xFFDFFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__COMMON_SHARED_EN (0x00200000U)
#define RGX_CR_CDM_CONTEXT_PDS1_COMMON_SHARED_SHIFT (20U)
#define RGX_CR_CDM_CONTEXT_PDS1_COMMON_SHARED_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_CDM_CONTEXT_PDS1_COMMON_SHARED_EN (0x00100000U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__COMMON_SIZE_SHIFT (12U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__COMMON_SIZE_CLRMSK (0xFFE00FFFU)
#define RGX_CR_CDM_CONTEXT_PDS1_COMMON_SIZE_SHIFT (11U)
#define RGX_CR_CDM_CONTEXT_PDS1_COMMON_SIZE_CLRMSK (0xFFF007FFU)
#define RGX_CR_CDM_CONTEXT_PDS1_TEMP_SIZE_SHIFT (7U)
#define RGX_CR_CDM_CONTEXT_PDS1_TEMP_SIZE_CLRMSK (0xFFFFF87FU)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__TEMP_SIZE_SHIFT (7U)
#define RGX_CR_CDM_CONTEXT_PDS1__TEMPSIZE8__TEMP_SIZE_CLRMSK (0xFFFFF07FU)
#define RGX_CR_CDM_CONTEXT_PDS1_DATA_SIZE_SHIFT (1U)
#define RGX_CR_CDM_CONTEXT_PDS1_DATA_SIZE_CLRMSK (0xFFFFFF81U)
#define RGX_CR_CDM_CONTEXT_PDS1_FENCE_SHIFT (0U)
#define RGX_CR_CDM_CONTEXT_PDS1_FENCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_CDM_CONTEXT_PDS1_FENCE_EN (0x00000001U)

/*
    Register RGX_CR_CDM_TERMINATE_PDS
*/
#define RGX_CR_CDM_TERMINATE_PDS (0x04B8U)
#define RGX_CR_CDM_TERMINATE_PDS_MASKFULL (IMG_UINT64_C(0xFFFFFFF0FFFFFFF0))
#define RGX_CR_CDM_TERMINATE_PDS_DATA_ADDR_SHIFT (36U)
#define RGX_CR_CDM_TERMINATE_PDS_DATA_ADDR_CLRMSK \
	(IMG_UINT64_C(0x0000000FFFFFFFFF))
#define RGX_CR_CDM_TERMINATE_PDS_DATA_ADDR_ALIGNSHIFT (4U)
#define RGX_CR_CDM_TERMINATE_PDS_DATA_ADDR_ALIGNSIZE (16U)
#define RGX_CR_CDM_TERMINATE_PDS_CODE_ADDR_SHIFT (4U)
#define RGX_CR_CDM_TERMINATE_PDS_CODE_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF0000000F))
#define RGX_CR_CDM_TERMINATE_PDS_CODE_ADDR_ALIGNSHIFT (4U)
#define RGX_CR_CDM_TERMINATE_PDS_CODE_ADDR_ALIGNSIZE (16U)

/*
    Register RGX_CR_CDM_TERMINATE_PDS1
*/
#define RGX_CR_CDM_TERMINATE_PDS1 (0x04C0U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__MASKFULL \
	(IMG_UINT64_C(0x000000007FFFFFFF))
#define RGX_CR_CDM_TERMINATE_PDS1_MASKFULL (IMG_UINT64_C(0x000000003FFFFFFF))
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__PDS_SEQ_DEP_SHIFT (30U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__PDS_SEQ_DEP_CLRMSK (0xBFFFFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__PDS_SEQ_DEP_EN (0x40000000U)
#define RGX_CR_CDM_TERMINATE_PDS1_PDS_SEQ_DEP_SHIFT (29U)
#define RGX_CR_CDM_TERMINATE_PDS1_PDS_SEQ_DEP_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1_PDS_SEQ_DEP_EN (0x20000000U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__USC_SEQ_DEP_SHIFT (29U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__USC_SEQ_DEP_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__USC_SEQ_DEP_EN (0x20000000U)
#define RGX_CR_CDM_TERMINATE_PDS1_USC_SEQ_DEP_SHIFT (28U)
#define RGX_CR_CDM_TERMINATE_PDS1_USC_SEQ_DEP_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1_USC_SEQ_DEP_EN (0x10000000U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__TARGET_SHIFT (28U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__TARGET_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__TARGET_EN (0x10000000U)
#define RGX_CR_CDM_TERMINATE_PDS1_TARGET_SHIFT (27U)
#define RGX_CR_CDM_TERMINATE_PDS1_TARGET_CLRMSK (0xF7FFFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1_TARGET_EN (0x08000000U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__UNIFIED_SIZE_SHIFT (22U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__UNIFIED_SIZE_CLRMSK (0xF03FFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1_UNIFIED_SIZE_SHIFT (21U)
#define RGX_CR_CDM_TERMINATE_PDS1_UNIFIED_SIZE_CLRMSK (0xF81FFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__COMMON_SHARED_SHIFT (21U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__COMMON_SHARED_CLRMSK (0xFFDFFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__COMMON_SHARED_EN (0x00200000U)
#define RGX_CR_CDM_TERMINATE_PDS1_COMMON_SHARED_SHIFT (20U)
#define RGX_CR_CDM_TERMINATE_PDS1_COMMON_SHARED_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_CDM_TERMINATE_PDS1_COMMON_SHARED_EN (0x00100000U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__COMMON_SIZE_SHIFT (12U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__COMMON_SIZE_CLRMSK (0xFFE00FFFU)
#define RGX_CR_CDM_TERMINATE_PDS1_COMMON_SIZE_SHIFT (11U)
#define RGX_CR_CDM_TERMINATE_PDS1_COMMON_SIZE_CLRMSK (0xFFF007FFU)
#define RGX_CR_CDM_TERMINATE_PDS1_TEMP_SIZE_SHIFT (7U)
#define RGX_CR_CDM_TERMINATE_PDS1_TEMP_SIZE_CLRMSK (0xFFFFF87FU)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__TEMP_SIZE_SHIFT (7U)
#define RGX_CR_CDM_TERMINATE_PDS1__TEMPSIZE8__TEMP_SIZE_CLRMSK (0xFFFFF07FU)
#define RGX_CR_CDM_TERMINATE_PDS1_DATA_SIZE_SHIFT (1U)
#define RGX_CR_CDM_TERMINATE_PDS1_DATA_SIZE_CLRMSK (0xFFFFFF81U)
#define RGX_CR_CDM_TERMINATE_PDS1_FENCE_SHIFT (0U)
#define RGX_CR_CDM_TERMINATE_PDS1_FENCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_CDM_TERMINATE_PDS1_FENCE_EN (0x00000001U)

/*
    Register RGX_CR_CDM_CONTEXT_LOAD_PDS0
*/
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0 (0x04D8U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0_MASKFULL (IMG_UINT64_C(0xFFFFFFF0FFFFFFF0))
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0_DATA_ADDR_SHIFT (36U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0_DATA_ADDR_CLRMSK \
	(IMG_UINT64_C(0x0000000FFFFFFFFF))
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0_DATA_ADDR_ALIGNSHIFT (4U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0_DATA_ADDR_ALIGNSIZE (16U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0_CODE_ADDR_SHIFT (4U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0_CODE_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF0000000F))
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0_CODE_ADDR_ALIGNSHIFT (4U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS0_CODE_ADDR_ALIGNSIZE (16U)

/*
    Register RGX_CR_CDM_CONTEXT_LOAD_PDS1
*/
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1 (0x04E0U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__MASKFULL \
	(IMG_UINT64_C(0x000000007FFFFFFF))
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_MASKFULL (IMG_UINT64_C(0x000000003FFFFFFF))
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__PDS_SEQ_DEP_SHIFT (30U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__PDS_SEQ_DEP_CLRMSK \
	(0xBFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__PDS_SEQ_DEP_EN (0x40000000U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_PDS_SEQ_DEP_SHIFT (29U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_PDS_SEQ_DEP_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_PDS_SEQ_DEP_EN (0x20000000U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__USC_SEQ_DEP_SHIFT (29U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__USC_SEQ_DEP_CLRMSK \
	(0xDFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__USC_SEQ_DEP_EN (0x20000000U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_USC_SEQ_DEP_SHIFT (28U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_USC_SEQ_DEP_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_USC_SEQ_DEP_EN (0x10000000U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__TARGET_SHIFT (28U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__TARGET_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__TARGET_EN (0x10000000U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_TARGET_SHIFT (27U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_TARGET_CLRMSK (0xF7FFFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_TARGET_EN (0x08000000U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__UNIFIED_SIZE_SHIFT (22U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__UNIFIED_SIZE_CLRMSK \
	(0xF03FFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_UNIFIED_SIZE_SHIFT (21U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_UNIFIED_SIZE_CLRMSK (0xF81FFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__COMMON_SHARED_SHIFT (21U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__COMMON_SHARED_CLRMSK \
	(0xFFDFFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__COMMON_SHARED_EN (0x00200000U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_COMMON_SHARED_SHIFT (20U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_COMMON_SHARED_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_COMMON_SHARED_EN (0x00100000U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__COMMON_SIZE_SHIFT (12U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__COMMON_SIZE_CLRMSK \
	(0xFFE00FFFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_COMMON_SIZE_SHIFT (11U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_COMMON_SIZE_CLRMSK (0xFFF007FFU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_TEMP_SIZE_SHIFT (7U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_TEMP_SIZE_CLRMSK (0xFFFFF87FU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__TEMP_SIZE_SHIFT (7U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1__TEMPSIZE8__TEMP_SIZE_CLRMSK (0xFFFFF07FU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_DATA_SIZE_SHIFT (1U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_DATA_SIZE_CLRMSK (0xFFFFFF81U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_FENCE_SHIFT (0U)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_FENCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_CDM_CONTEXT_LOAD_PDS1_FENCE_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_WRAPPER_CONFIG
*/
#define RGX_CR_MIPS_WRAPPER_CONFIG (0x0810U)
#define RGX_CR_MIPS_WRAPPER_CONFIG_MASKFULL (IMG_UINT64_C(0x000001030F01FFFF))
#define RGX_CR_MIPS_WRAPPER_CONFIG_FW_IDLE_ENABLE_SHIFT (40U)
#define RGX_CR_MIPS_WRAPPER_CONFIG_FW_IDLE_ENABLE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFEFFFFFFFFFF))
#define RGX_CR_MIPS_WRAPPER_CONFIG_FW_IDLE_ENABLE_EN \
	(IMG_UINT64_C(0x0000010000000000))
#define RGX_CR_MIPS_WRAPPER_CONFIG_DISABLE_BOOT_SHIFT (33U)
#define RGX_CR_MIPS_WRAPPER_CONFIG_DISABLE_BOOT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFDFFFFFFFF))
#define RGX_CR_MIPS_WRAPPER_CONFIG_DISABLE_BOOT_EN \
	(IMG_UINT64_C(0x0000000200000000))
#define RGX_CR_MIPS_WRAPPER_CONFIG_L2_CACHE_OFF_SHIFT (32U)
#define RGX_CR_MIPS_WRAPPER_CONFIG_L2_CACHE_OFF_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFEFFFFFFFF))
#define RGX_CR_MIPS_WRAPPER_CONFIG_L2_CACHE_OFF_EN \
	(IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_MIPS_WRAPPER_CONFIG_OS_ID_SHIFT (25U)
#define RGX_CR_MIPS_WRAPPER_CONFIG_OS_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF1FFFFFF))
#define RGX_CR_MIPS_WRAPPER_CONFIG_TRUSTED_SHIFT (24U)
#define RGX_CR_MIPS_WRAPPER_CONFIG_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFEFFFFFF))
#define RGX_CR_MIPS_WRAPPER_CONFIG_TRUSTED_EN (IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_MIPS_WRAPPER_CONFIG_BOOT_ISA_MODE_SHIFT (16U)
#define RGX_CR_MIPS_WRAPPER_CONFIG_BOOT_ISA_MODE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFEFFFF))
#define RGX_CR_MIPS_WRAPPER_CONFIG_BOOT_ISA_MODE_MIPS32 \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_MIPS_WRAPPER_CONFIG_BOOT_ISA_MODE_MICROMIPS \
	(IMG_UINT64_C(0x0000000000010000))
#define RGX_CR_MIPS_WRAPPER_CONFIG_REGBANK_BASE_ADDR_SHIFT (0U)
#define RGX_CR_MIPS_WRAPPER_CONFIG_REGBANK_BASE_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_MIPS_ADDR_REMAP1_CONFIG1
*/
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG1 (0x0818U)
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG1_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFF001))
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG1_BASE_ADDR_IN_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG1_BASE_ADDR_IN_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000FFF))
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG1_MODE_ENABLE_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG1_MODE_ENABLE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG1_MODE_ENABLE_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_MIPS_ADDR_REMAP1_CONFIG2
*/
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2 (0x0820U)
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFF1FF))
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_ADDR_OUT_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_ADDR_OUT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_OS_ID_SHIFT (6U)
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_OS_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFE3F))
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_TRUSTED_SHIFT (5U)
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_TRUSTED_EN \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_REGION_SIZE_POW2_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP1_CONFIG2_REGION_SIZE_POW2_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFE0))

/*
    Register RGX_CR_MIPS_ADDR_REMAP2_CONFIG1
*/
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG1 (0x0828U)
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG1_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFF001))
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG1_BASE_ADDR_IN_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG1_BASE_ADDR_IN_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000FFF))
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG1_MODE_ENABLE_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG1_MODE_ENABLE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG1_MODE_ENABLE_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_MIPS_ADDR_REMAP2_CONFIG2
*/
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2 (0x0830U)
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFF1FF))
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_ADDR_OUT_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_ADDR_OUT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_OS_ID_SHIFT (6U)
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_OS_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFE3F))
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_TRUSTED_SHIFT (5U)
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_TRUSTED_EN \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_REGION_SIZE_POW2_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP2_CONFIG2_REGION_SIZE_POW2_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFE0))

/*
    Register RGX_CR_MIPS_ADDR_REMAP3_CONFIG1
*/
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG1 (0x0838U)
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG1_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFF001))
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG1_BASE_ADDR_IN_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG1_BASE_ADDR_IN_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000FFF))
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG1_MODE_ENABLE_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG1_MODE_ENABLE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG1_MODE_ENABLE_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_MIPS_ADDR_REMAP3_CONFIG2
*/
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2 (0x0840U)
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFF1FF))
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_ADDR_OUT_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_ADDR_OUT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_OS_ID_SHIFT (6U)
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_OS_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFE3F))
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_TRUSTED_SHIFT (5U)
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_TRUSTED_EN \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_REGION_SIZE_POW2_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP3_CONFIG2_REGION_SIZE_POW2_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFE0))

/*
    Register RGX_CR_MIPS_ADDR_REMAP4_CONFIG1
*/
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG1 (0x0848U)
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG1_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFF001))
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG1_BASE_ADDR_IN_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG1_BASE_ADDR_IN_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000FFF))
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG1_MODE_ENABLE_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG1_MODE_ENABLE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG1_MODE_ENABLE_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_MIPS_ADDR_REMAP4_CONFIG2
*/
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2 (0x0850U)
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFF1FF))
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_ADDR_OUT_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_ADDR_OUT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_OS_ID_SHIFT (6U)
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_OS_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFE3F))
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_TRUSTED_SHIFT (5U)
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_TRUSTED_EN \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_REGION_SIZE_POW2_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP4_CONFIG2_REGION_SIZE_POW2_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFE0))

/*
    Register RGX_CR_MIPS_ADDR_REMAP5_CONFIG1
*/
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG1 (0x0858U)
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG1_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFF001))
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG1_BASE_ADDR_IN_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG1_BASE_ADDR_IN_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000FFF))
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG1_MODE_ENABLE_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG1_MODE_ENABLE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG1_MODE_ENABLE_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_MIPS_ADDR_REMAP5_CONFIG2
*/
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2 (0x0860U)
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFF1FF))
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_ADDR_OUT_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_ADDR_OUT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_OS_ID_SHIFT (6U)
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_OS_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFE3F))
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_TRUSTED_SHIFT (5U)
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_TRUSTED_EN \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_REGION_SIZE_POW2_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP5_CONFIG2_REGION_SIZE_POW2_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFE0))

/*
    Register RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_STATUS
*/
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_STATUS (0x0868U)
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_STATUS_MASKFULL \
	(IMG_UINT64_C(0x00000001FFFFFFFF))
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_STATUS_EVENT_SHIFT (32U)
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_STATUS_EVENT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFEFFFFFFFF))
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_STATUS_EVENT_EN \
	(IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_STATUS_ADDRESS_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_STATUS_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000000))

/*
    Register RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_CLEAR
*/
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_CLEAR (0x0870U)
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_CLEAR_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_CLEAR_EVENT_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_CLEAR_EVENT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MIPS_ADDR_REMAP_UNMAPPED_CLEAR_EVENT_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG
*/
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG (0x0878U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_MASKFULL \
	(IMG_UINT64_C(0xFFFFFFF7FFFFFFBF))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_ADDR_OUT_SHIFT (36U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_ADDR_OUT_CLRMSK \
	(IMG_UINT64_C(0x0000000FFFFFFFFF))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_OS_ID_SHIFT (32U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_OS_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFF8FFFFFFFF))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_BASE_ADDR_IN_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_BASE_ADDR_IN_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000FFF))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_TRUSTED_SHIFT (11U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF7FF))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_TRUSTED_EN \
	(IMG_UINT64_C(0x0000000000000800))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_SHIFT (7U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF87F))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_4KB \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_16KB \
	(IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_64KB \
	(IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_256KB \
	(IMG_UINT64_C(0x0000000000000180))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_1MB \
	(IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_4MB \
	(IMG_UINT64_C(0x0000000000000280))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_16MB \
	(IMG_UINT64_C(0x0000000000000300))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_64MB \
	(IMG_UINT64_C(0x0000000000000380))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_REGION_SIZE_256MB \
	(IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_ENTRY_SHIFT (1U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_ENTRY_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFC1))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_MODE_ENABLE_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_MODE_ENABLE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_CONFIG_MODE_ENABLE_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_MIPS_ADDR_REMAP_RANGE_READ
*/
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_READ (0x0880U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_READ_MASKFULL \
	(IMG_UINT64_C(0x000000000000003F))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_READ_ENTRY_SHIFT (1U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_READ_ENTRY_CLRMSK (0xFFFFFFC1U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_READ_REQUEST_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_READ_REQUEST_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_READ_REQUEST_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA
*/
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA (0x0888U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_MASKFULL \
	(IMG_UINT64_C(0xFFFFFFF7FFFFFF81))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_ADDR_OUT_SHIFT (36U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_ADDR_OUT_CLRMSK \
	(IMG_UINT64_C(0x0000000FFFFFFFFF))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_OS_ID_SHIFT (32U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_OS_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFF8FFFFFFFF))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_BASE_ADDR_IN_SHIFT (12U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_BASE_ADDR_IN_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000FFF))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_TRUSTED_SHIFT (11U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF7FF))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_TRUSTED_EN \
	(IMG_UINT64_C(0x0000000000000800))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_REGION_SIZE_SHIFT (7U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_REGION_SIZE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF87F))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_MODE_ENABLE_SHIFT (0U)
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_MODE_ENABLE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MIPS_ADDR_REMAP_RANGE_DATA_MODE_ENABLE_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_MIPS_WRAPPER_IRQ_ENABLE
*/
#define RGX_CR_MIPS_WRAPPER_IRQ_ENABLE (0x08A0U)
#define RGX_CR_MIPS_WRAPPER_IRQ_ENABLE_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_MIPS_WRAPPER_IRQ_ENABLE_EVENT_SHIFT (0U)
#define RGX_CR_MIPS_WRAPPER_IRQ_ENABLE_EVENT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MIPS_WRAPPER_IRQ_ENABLE_EVENT_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_WRAPPER_IRQ_STATUS
*/
#define RGX_CR_MIPS_WRAPPER_IRQ_STATUS (0x08A8U)
#define RGX_CR_MIPS_WRAPPER_IRQ_STATUS_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_MIPS_WRAPPER_IRQ_STATUS_EVENT_SHIFT (0U)
#define RGX_CR_MIPS_WRAPPER_IRQ_STATUS_EVENT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MIPS_WRAPPER_IRQ_STATUS_EVENT_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_WRAPPER_IRQ_CLEAR
*/
#define RGX_CR_MIPS_WRAPPER_IRQ_CLEAR (0x08B0U)
#define RGX_CR_MIPS_WRAPPER_IRQ_CLEAR_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_MIPS_WRAPPER_IRQ_CLEAR_EVENT_SHIFT (0U)
#define RGX_CR_MIPS_WRAPPER_IRQ_CLEAR_EVENT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MIPS_WRAPPER_IRQ_CLEAR_EVENT_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_WRAPPER_NMI_ENABLE
*/
#define RGX_CR_MIPS_WRAPPER_NMI_ENABLE (0x08B8U)
#define RGX_CR_MIPS_WRAPPER_NMI_ENABLE_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_MIPS_WRAPPER_NMI_ENABLE_EVENT_SHIFT (0U)
#define RGX_CR_MIPS_WRAPPER_NMI_ENABLE_EVENT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MIPS_WRAPPER_NMI_ENABLE_EVENT_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_WRAPPER_NMI_EVENT
*/
#define RGX_CR_MIPS_WRAPPER_NMI_EVENT (0x08C0U)
#define RGX_CR_MIPS_WRAPPER_NMI_EVENT_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_MIPS_WRAPPER_NMI_EVENT_TRIGGER_SHIFT (0U)
#define RGX_CR_MIPS_WRAPPER_NMI_EVENT_TRIGGER_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MIPS_WRAPPER_NMI_EVENT_TRIGGER_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_DEBUG_CONFIG
*/
#define RGX_CR_MIPS_DEBUG_CONFIG (0x08C8U)
#define RGX_CR_MIPS_DEBUG_CONFIG_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_MIPS_DEBUG_CONFIG_DISABLE_PROBE_DEBUG_SHIFT (0U)
#define RGX_CR_MIPS_DEBUG_CONFIG_DISABLE_PROBE_DEBUG_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MIPS_DEBUG_CONFIG_DISABLE_PROBE_DEBUG_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_EXCEPTION_STATUS
*/
#define RGX_CR_MIPS_EXCEPTION_STATUS (0x08D0U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_MASKFULL (IMG_UINT64_C(0x000000000000003F))
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_SLEEP_SHIFT (5U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_SLEEP_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_SLEEP_EN (0x00000020U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_NMI_TAKEN_SHIFT (4U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_NMI_TAKEN_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_NMI_TAKEN_EN (0x00000010U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_NEST_EXL_SHIFT (3U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_NEST_EXL_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_NEST_EXL_EN (0x00000008U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_NEST_ERL_SHIFT (2U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_NEST_ERL_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_NEST_ERL_EN (0x00000004U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_EXL_SHIFT (1U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_EXL_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_EXL_EN (0x00000002U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_ERL_SHIFT (0U)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_ERL_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_MIPS_EXCEPTION_STATUS_SI_ERL_EN (0x00000001U)

/*
    Register RGX_CR_MIPS_WRAPPER_STATUS
*/
#define RGX_CR_MIPS_WRAPPER_STATUS (0x08E8U)
#define RGX_CR_MIPS_WRAPPER_STATUS_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_MIPS_WRAPPER_STATUS_OUTSTANDING_REQUESTS_SHIFT (0U)
#define RGX_CR_MIPS_WRAPPER_STATUS_OUTSTANDING_REQUESTS_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_XPU_BROADCAST
*/
#define RGX_CR_XPU_BROADCAST (0x0890U)
#define RGX_CR_XPU_BROADCAST_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_XPU_BROADCAST_MASK_SHIFT (0U)
#define RGX_CR_XPU_BROADCAST_MASK_CLRMSK (0xFFFFFE00U)

/*
    Register RGX_CR_META_SP_MSLVDATAX
*/
#define RGX_CR_META_SP_MSLVDATAX (0x0A00U)
#define RGX_CR_META_SP_MSLVDATAX_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_META_SP_MSLVDATAX_MSLVDATAX_SHIFT (0U)
#define RGX_CR_META_SP_MSLVDATAX_MSLVDATAX_CLRMSK (0x00000000U)

/*
    Register RGX_CR_META_SP_MSLVDATAT
*/
#define RGX_CR_META_SP_MSLVDATAT (0x0A08U)
#define RGX_CR_META_SP_MSLVDATAT_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_META_SP_MSLVDATAT_MSLVDATAT_SHIFT (0U)
#define RGX_CR_META_SP_MSLVDATAT_MSLVDATAT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_META_SP_MSLVCTRL0
*/
#define RGX_CR_META_SP_MSLVCTRL0 (0x0A10U)
#define RGX_CR_META_SP_MSLVCTRL0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_META_SP_MSLVCTRL0_ADDR_SHIFT (2U)
#define RGX_CR_META_SP_MSLVCTRL0_ADDR_CLRMSK (0x00000003U)
#define RGX_CR_META_SP_MSLVCTRL0_AUTOINCR_SHIFT (1U)
#define RGX_CR_META_SP_MSLVCTRL0_AUTOINCR_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_META_SP_MSLVCTRL0_AUTOINCR_EN (0x00000002U)
#define RGX_CR_META_SP_MSLVCTRL0_RD_SHIFT (0U)
#define RGX_CR_META_SP_MSLVCTRL0_RD_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_META_SP_MSLVCTRL0_RD_EN (0x00000001U)

/*
    Register RGX_CR_META_SP_MSLVCTRL1
*/
#define RGX_CR_META_SP_MSLVCTRL1 (0x0A18U)
#define RGX_CR_META_SP_MSLVCTRL1_MASKFULL (IMG_UINT64_C(0x00000000F7F4003F))
#define RGX_CR_META_SP_MSLVCTRL1_DEFERRTHREAD_SHIFT (30U)
#define RGX_CR_META_SP_MSLVCTRL1_DEFERRTHREAD_CLRMSK (0x3FFFFFFFU)
#define RGX_CR_META_SP_MSLVCTRL1_LOCK2_INTERLOCK_SHIFT (29U)
#define RGX_CR_META_SP_MSLVCTRL1_LOCK2_INTERLOCK_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_META_SP_MSLVCTRL1_LOCK2_INTERLOCK_EN (0x20000000U)
#define RGX_CR_META_SP_MSLVCTRL1_ATOMIC_INTERLOCK_SHIFT (28U)
#define RGX_CR_META_SP_MSLVCTRL1_ATOMIC_INTERLOCK_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_META_SP_MSLVCTRL1_ATOMIC_INTERLOCK_EN (0x10000000U)
#define RGX_CR_META_SP_MSLVCTRL1_GBLPORT_IDLE_SHIFT (26U)
#define RGX_CR_META_SP_MSLVCTRL1_GBLPORT_IDLE_CLRMSK (0xFBFFFFFFU)
#define RGX_CR_META_SP_MSLVCTRL1_GBLPORT_IDLE_EN (0x04000000U)
#define RGX_CR_META_SP_MSLVCTRL1_COREMEM_IDLE_SHIFT (25U)
#define RGX_CR_META_SP_MSLVCTRL1_COREMEM_IDLE_CLRMSK (0xFDFFFFFFU)
#define RGX_CR_META_SP_MSLVCTRL1_COREMEM_IDLE_EN (0x02000000U)
#define RGX_CR_META_SP_MSLVCTRL1_READY_SHIFT (24U)
#define RGX_CR_META_SP_MSLVCTRL1_READY_CLRMSK (0xFEFFFFFFU)
#define RGX_CR_META_SP_MSLVCTRL1_READY_EN (0x01000000U)
#define RGX_CR_META_SP_MSLVCTRL1_DEFERRID_SHIFT (21U)
#define RGX_CR_META_SP_MSLVCTRL1_DEFERRID_CLRMSK (0xFF1FFFFFU)
#define RGX_CR_META_SP_MSLVCTRL1_DEFERR_SHIFT (20U)
#define RGX_CR_META_SP_MSLVCTRL1_DEFERR_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_META_SP_MSLVCTRL1_DEFERR_EN (0x00100000U)
#define RGX_CR_META_SP_MSLVCTRL1_WR_ACTIVE_SHIFT (18U)
#define RGX_CR_META_SP_MSLVCTRL1_WR_ACTIVE_CLRMSK (0xFFFBFFFFU)
#define RGX_CR_META_SP_MSLVCTRL1_WR_ACTIVE_EN (0x00040000U)
#define RGX_CR_META_SP_MSLVCTRL1_THREAD_SHIFT (4U)
#define RGX_CR_META_SP_MSLVCTRL1_THREAD_CLRMSK (0xFFFFFFCFU)
#define RGX_CR_META_SP_MSLVCTRL1_TRANS_SIZE_SHIFT (2U)
#define RGX_CR_META_SP_MSLVCTRL1_TRANS_SIZE_CLRMSK (0xFFFFFFF3U)
#define RGX_CR_META_SP_MSLVCTRL1_BYTE_ROUND_SHIFT (0U)
#define RGX_CR_META_SP_MSLVCTRL1_BYTE_ROUND_CLRMSK (0xFFFFFFFCU)

/*
    Register RGX_CR_META_SP_MSLVHANDSHKE
*/
#define RGX_CR_META_SP_MSLVHANDSHKE (0x0A50U)
#define RGX_CR_META_SP_MSLVHANDSHKE_MASKFULL (IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_META_SP_MSLVHANDSHKE_INPUT_SHIFT (2U)
#define RGX_CR_META_SP_MSLVHANDSHKE_INPUT_CLRMSK (0xFFFFFFF3U)
#define RGX_CR_META_SP_MSLVHANDSHKE_OUTPUT_SHIFT (0U)
#define RGX_CR_META_SP_MSLVHANDSHKE_OUTPUT_CLRMSK (0xFFFFFFFCU)

/*
    Register RGX_CR_META_SP_MSLVT0KICK
*/
#define RGX_CR_META_SP_MSLVT0KICK (0x0A80U)
#define RGX_CR_META_SP_MSLVT0KICK_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_META_SP_MSLVT0KICK_MSLVT0KICK_SHIFT (0U)
#define RGX_CR_META_SP_MSLVT0KICK_MSLVT0KICK_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_META_SP_MSLVT0KICKI
*/
#define RGX_CR_META_SP_MSLVT0KICKI (0x0A88U)
#define RGX_CR_META_SP_MSLVT0KICKI_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_META_SP_MSLVT0KICKI_MSLVT0KICKI_SHIFT (0U)
#define RGX_CR_META_SP_MSLVT0KICKI_MSLVT0KICKI_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_META_SP_MSLVT1KICK
*/
#define RGX_CR_META_SP_MSLVT1KICK (0x0A90U)
#define RGX_CR_META_SP_MSLVT1KICK_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_META_SP_MSLVT1KICK_MSLVT1KICK_SHIFT (0U)
#define RGX_CR_META_SP_MSLVT1KICK_MSLVT1KICK_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_META_SP_MSLVT1KICKI
*/
#define RGX_CR_META_SP_MSLVT1KICKI (0x0A98U)
#define RGX_CR_META_SP_MSLVT1KICKI_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_META_SP_MSLVT1KICKI_MSLVT1KICKI_SHIFT (0U)
#define RGX_CR_META_SP_MSLVT1KICKI_MSLVT1KICKI_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_META_SP_MSLVT2KICK
*/
#define RGX_CR_META_SP_MSLVT2KICK (0x0AA0U)
#define RGX_CR_META_SP_MSLVT2KICK_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_META_SP_MSLVT2KICK_MSLVT2KICK_SHIFT (0U)
#define RGX_CR_META_SP_MSLVT2KICK_MSLVT2KICK_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_META_SP_MSLVT2KICKI
*/
#define RGX_CR_META_SP_MSLVT2KICKI (0x0AA8U)
#define RGX_CR_META_SP_MSLVT2KICKI_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_META_SP_MSLVT2KICKI_MSLVT2KICKI_SHIFT (0U)
#define RGX_CR_META_SP_MSLVT2KICKI_MSLVT2KICKI_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_META_SP_MSLVT3KICK
*/
#define RGX_CR_META_SP_MSLVT3KICK (0x0AB0U)
#define RGX_CR_META_SP_MSLVT3KICK_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_META_SP_MSLVT3KICK_MSLVT3KICK_SHIFT (0U)
#define RGX_CR_META_SP_MSLVT3KICK_MSLVT3KICK_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_META_SP_MSLVT3KICKI
*/
#define RGX_CR_META_SP_MSLVT3KICKI (0x0AB8U)
#define RGX_CR_META_SP_MSLVT3KICKI_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_META_SP_MSLVT3KICKI_MSLVT3KICKI_SHIFT (0U)
#define RGX_CR_META_SP_MSLVT3KICKI_MSLVT3KICKI_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_META_SP_MSLVRST
*/
#define RGX_CR_META_SP_MSLVRST (0x0AC0U)
#define RGX_CR_META_SP_MSLVRST_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_META_SP_MSLVRST_SOFTRESET_SHIFT (0U)
#define RGX_CR_META_SP_MSLVRST_SOFTRESET_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_META_SP_MSLVRST_SOFTRESET_EN (0x00000001U)

/*
    Register RGX_CR_META_SP_MSLVIRQSTATUS
*/
#define RGX_CR_META_SP_MSLVIRQSTATUS (0x0AC8U)
#define RGX_CR_META_SP_MSLVIRQSTATUS_MASKFULL (IMG_UINT64_C(0x000000000000000C))
#define RGX_CR_META_SP_MSLVIRQSTATUS_TRIGVECT3_SHIFT (3U)
#define RGX_CR_META_SP_MSLVIRQSTATUS_TRIGVECT3_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_META_SP_MSLVIRQSTATUS_TRIGVECT3_EN (0x00000008U)
#define RGX_CR_META_SP_MSLVIRQSTATUS_TRIGVECT2_SHIFT (2U)
#define RGX_CR_META_SP_MSLVIRQSTATUS_TRIGVECT2_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_META_SP_MSLVIRQSTATUS_TRIGVECT2_EN (0x00000004U)

/*
    Register RGX_CR_META_SP_MSLVIRQENABLE
*/
#define RGX_CR_META_SP_MSLVIRQENABLE (0x0AD0U)
#define RGX_CR_META_SP_MSLVIRQENABLE_MASKFULL (IMG_UINT64_C(0x000000000000000C))
#define RGX_CR_META_SP_MSLVIRQENABLE_EVENT1_SHIFT (3U)
#define RGX_CR_META_SP_MSLVIRQENABLE_EVENT1_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_META_SP_MSLVIRQENABLE_EVENT1_EN (0x00000008U)
#define RGX_CR_META_SP_MSLVIRQENABLE_EVENT0_SHIFT (2U)
#define RGX_CR_META_SP_MSLVIRQENABLE_EVENT0_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_META_SP_MSLVIRQENABLE_EVENT0_EN (0x00000004U)

/*
    Register RGX_CR_META_SP_MSLVIRQLEVEL
*/
#define RGX_CR_META_SP_MSLVIRQLEVEL (0x0AD8U)
#define RGX_CR_META_SP_MSLVIRQLEVEL_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_META_SP_MSLVIRQLEVEL_MODE_SHIFT (0U)
#define RGX_CR_META_SP_MSLVIRQLEVEL_MODE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_META_SP_MSLVIRQLEVEL_MODE_EN (0x00000001U)

/*
    Register RGX_CR_MTS_SCHEDULE
*/
#define RGX_CR_MTS_SCHEDULE (0x0B00U)
#define RGX_CR_MTS_SCHEDULE_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_MTS_SCHEDULE_HOST_SHIFT (8U)
#define RGX_CR_MTS_SCHEDULE_HOST_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_MTS_SCHEDULE_HOST_BG_TIMER (0x00000000U)
#define RGX_CR_MTS_SCHEDULE_HOST_HOST (0x00000100U)
#define RGX_CR_MTS_SCHEDULE_PRIORITY_SHIFT (6U)
#define RGX_CR_MTS_SCHEDULE_PRIORITY_CLRMSK (0xFFFFFF3FU)
#define RGX_CR_MTS_SCHEDULE_PRIORITY_PRT0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE_PRIORITY_PRT1 (0x00000040U)
#define RGX_CR_MTS_SCHEDULE_PRIORITY_PRT2 (0x00000080U)
#define RGX_CR_MTS_SCHEDULE_PRIORITY_PRT3 (0x000000C0U)
#define RGX_CR_MTS_SCHEDULE_CONTEXT_SHIFT (5U)
#define RGX_CR_MTS_SCHEDULE_CONTEXT_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MTS_SCHEDULE_CONTEXT_BGCTX (0x00000000U)
#define RGX_CR_MTS_SCHEDULE_CONTEXT_INTCTX (0x00000020U)
#define RGX_CR_MTS_SCHEDULE_TASK_SHIFT (4U)
#define RGX_CR_MTS_SCHEDULE_TASK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MTS_SCHEDULE_TASK_NON_COUNTED (0x00000000U)
#define RGX_CR_MTS_SCHEDULE_TASK_COUNTED (0x00000010U)
#define RGX_CR_MTS_SCHEDULE_DM_SHIFT (0U)
#define RGX_CR_MTS_SCHEDULE_DM_CLRMSK (0xFFFFFFF0U)
#define RGX_CR_MTS_SCHEDULE_DM_DM0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE_DM_DM1 (0x00000001U)
#define RGX_CR_MTS_SCHEDULE_DM_DM2 (0x00000002U)
#define RGX_CR_MTS_SCHEDULE_DM_DM3 (0x00000003U)
#define RGX_CR_MTS_SCHEDULE_DM_DM4 (0x00000004U)
#define RGX_CR_MTS_SCHEDULE_DM_DM5 (0x00000005U)
#define RGX_CR_MTS_SCHEDULE_DM_DM6 (0x00000006U)
#define RGX_CR_MTS_SCHEDULE_DM_DM7 (0x00000007U)
#define RGX_CR_MTS_SCHEDULE_DM_DM_ALL (0x0000000FU)

/*
    Register RGX_CR_MTS_SCHEDULE1
*/
#define RGX_CR_MTS_SCHEDULE1 (0x10B00U)
#define RGX_CR_MTS_SCHEDULE1_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_MTS_SCHEDULE1_HOST_SHIFT (8U)
#define RGX_CR_MTS_SCHEDULE1_HOST_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_MTS_SCHEDULE1_HOST_BG_TIMER (0x00000000U)
#define RGX_CR_MTS_SCHEDULE1_HOST_HOST (0x00000100U)
#define RGX_CR_MTS_SCHEDULE1_PRIORITY_SHIFT (6U)
#define RGX_CR_MTS_SCHEDULE1_PRIORITY_CLRMSK (0xFFFFFF3FU)
#define RGX_CR_MTS_SCHEDULE1_PRIORITY_PRT0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE1_PRIORITY_PRT1 (0x00000040U)
#define RGX_CR_MTS_SCHEDULE1_PRIORITY_PRT2 (0x00000080U)
#define RGX_CR_MTS_SCHEDULE1_PRIORITY_PRT3 (0x000000C0U)
#define RGX_CR_MTS_SCHEDULE1_CONTEXT_SHIFT (5U)
#define RGX_CR_MTS_SCHEDULE1_CONTEXT_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MTS_SCHEDULE1_CONTEXT_BGCTX (0x00000000U)
#define RGX_CR_MTS_SCHEDULE1_CONTEXT_INTCTX (0x00000020U)
#define RGX_CR_MTS_SCHEDULE1_TASK_SHIFT (4U)
#define RGX_CR_MTS_SCHEDULE1_TASK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MTS_SCHEDULE1_TASK_NON_COUNTED (0x00000000U)
#define RGX_CR_MTS_SCHEDULE1_TASK_COUNTED (0x00000010U)
#define RGX_CR_MTS_SCHEDULE1_DM_SHIFT (0U)
#define RGX_CR_MTS_SCHEDULE1_DM_CLRMSK (0xFFFFFFF0U)
#define RGX_CR_MTS_SCHEDULE1_DM_DM0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE1_DM_DM1 (0x00000001U)
#define RGX_CR_MTS_SCHEDULE1_DM_DM2 (0x00000002U)
#define RGX_CR_MTS_SCHEDULE1_DM_DM3 (0x00000003U)
#define RGX_CR_MTS_SCHEDULE1_DM_DM4 (0x00000004U)
#define RGX_CR_MTS_SCHEDULE1_DM_DM5 (0x00000005U)
#define RGX_CR_MTS_SCHEDULE1_DM_DM6 (0x00000006U)
#define RGX_CR_MTS_SCHEDULE1_DM_DM7 (0x00000007U)
#define RGX_CR_MTS_SCHEDULE1_DM_DM_ALL (0x0000000FU)

/*
    Register RGX_CR_MTS_SCHEDULE2
*/
#define RGX_CR_MTS_SCHEDULE2 (0x20B00U)
#define RGX_CR_MTS_SCHEDULE2_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_MTS_SCHEDULE2_HOST_SHIFT (8U)
#define RGX_CR_MTS_SCHEDULE2_HOST_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_MTS_SCHEDULE2_HOST_BG_TIMER (0x00000000U)
#define RGX_CR_MTS_SCHEDULE2_HOST_HOST (0x00000100U)
#define RGX_CR_MTS_SCHEDULE2_PRIORITY_SHIFT (6U)
#define RGX_CR_MTS_SCHEDULE2_PRIORITY_CLRMSK (0xFFFFFF3FU)
#define RGX_CR_MTS_SCHEDULE2_PRIORITY_PRT0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE2_PRIORITY_PRT1 (0x00000040U)
#define RGX_CR_MTS_SCHEDULE2_PRIORITY_PRT2 (0x00000080U)
#define RGX_CR_MTS_SCHEDULE2_PRIORITY_PRT3 (0x000000C0U)
#define RGX_CR_MTS_SCHEDULE2_CONTEXT_SHIFT (5U)
#define RGX_CR_MTS_SCHEDULE2_CONTEXT_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MTS_SCHEDULE2_CONTEXT_BGCTX (0x00000000U)
#define RGX_CR_MTS_SCHEDULE2_CONTEXT_INTCTX (0x00000020U)
#define RGX_CR_MTS_SCHEDULE2_TASK_SHIFT (4U)
#define RGX_CR_MTS_SCHEDULE2_TASK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MTS_SCHEDULE2_TASK_NON_COUNTED (0x00000000U)
#define RGX_CR_MTS_SCHEDULE2_TASK_COUNTED (0x00000010U)
#define RGX_CR_MTS_SCHEDULE2_DM_SHIFT (0U)
#define RGX_CR_MTS_SCHEDULE2_DM_CLRMSK (0xFFFFFFF0U)
#define RGX_CR_MTS_SCHEDULE2_DM_DM0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE2_DM_DM1 (0x00000001U)
#define RGX_CR_MTS_SCHEDULE2_DM_DM2 (0x00000002U)
#define RGX_CR_MTS_SCHEDULE2_DM_DM3 (0x00000003U)
#define RGX_CR_MTS_SCHEDULE2_DM_DM4 (0x00000004U)
#define RGX_CR_MTS_SCHEDULE2_DM_DM5 (0x00000005U)
#define RGX_CR_MTS_SCHEDULE2_DM_DM6 (0x00000006U)
#define RGX_CR_MTS_SCHEDULE2_DM_DM7 (0x00000007U)
#define RGX_CR_MTS_SCHEDULE2_DM_DM_ALL (0x0000000FU)

/*
    Register RGX_CR_MTS_SCHEDULE3
*/
#define RGX_CR_MTS_SCHEDULE3 (0x30B00U)
#define RGX_CR_MTS_SCHEDULE3_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_MTS_SCHEDULE3_HOST_SHIFT (8U)
#define RGX_CR_MTS_SCHEDULE3_HOST_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_MTS_SCHEDULE3_HOST_BG_TIMER (0x00000000U)
#define RGX_CR_MTS_SCHEDULE3_HOST_HOST (0x00000100U)
#define RGX_CR_MTS_SCHEDULE3_PRIORITY_SHIFT (6U)
#define RGX_CR_MTS_SCHEDULE3_PRIORITY_CLRMSK (0xFFFFFF3FU)
#define RGX_CR_MTS_SCHEDULE3_PRIORITY_PRT0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE3_PRIORITY_PRT1 (0x00000040U)
#define RGX_CR_MTS_SCHEDULE3_PRIORITY_PRT2 (0x00000080U)
#define RGX_CR_MTS_SCHEDULE3_PRIORITY_PRT3 (0x000000C0U)
#define RGX_CR_MTS_SCHEDULE3_CONTEXT_SHIFT (5U)
#define RGX_CR_MTS_SCHEDULE3_CONTEXT_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MTS_SCHEDULE3_CONTEXT_BGCTX (0x00000000U)
#define RGX_CR_MTS_SCHEDULE3_CONTEXT_INTCTX (0x00000020U)
#define RGX_CR_MTS_SCHEDULE3_TASK_SHIFT (4U)
#define RGX_CR_MTS_SCHEDULE3_TASK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MTS_SCHEDULE3_TASK_NON_COUNTED (0x00000000U)
#define RGX_CR_MTS_SCHEDULE3_TASK_COUNTED (0x00000010U)
#define RGX_CR_MTS_SCHEDULE3_DM_SHIFT (0U)
#define RGX_CR_MTS_SCHEDULE3_DM_CLRMSK (0xFFFFFFF0U)
#define RGX_CR_MTS_SCHEDULE3_DM_DM0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE3_DM_DM1 (0x00000001U)
#define RGX_CR_MTS_SCHEDULE3_DM_DM2 (0x00000002U)
#define RGX_CR_MTS_SCHEDULE3_DM_DM3 (0x00000003U)
#define RGX_CR_MTS_SCHEDULE3_DM_DM4 (0x00000004U)
#define RGX_CR_MTS_SCHEDULE3_DM_DM5 (0x00000005U)
#define RGX_CR_MTS_SCHEDULE3_DM_DM6 (0x00000006U)
#define RGX_CR_MTS_SCHEDULE3_DM_DM7 (0x00000007U)
#define RGX_CR_MTS_SCHEDULE3_DM_DM_ALL (0x0000000FU)

/*
    Register RGX_CR_MTS_SCHEDULE4
*/
#define RGX_CR_MTS_SCHEDULE4 (0x40B00U)
#define RGX_CR_MTS_SCHEDULE4_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_MTS_SCHEDULE4_HOST_SHIFT (8U)
#define RGX_CR_MTS_SCHEDULE4_HOST_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_MTS_SCHEDULE4_HOST_BG_TIMER (0x00000000U)
#define RGX_CR_MTS_SCHEDULE4_HOST_HOST (0x00000100U)
#define RGX_CR_MTS_SCHEDULE4_PRIORITY_SHIFT (6U)
#define RGX_CR_MTS_SCHEDULE4_PRIORITY_CLRMSK (0xFFFFFF3FU)
#define RGX_CR_MTS_SCHEDULE4_PRIORITY_PRT0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE4_PRIORITY_PRT1 (0x00000040U)
#define RGX_CR_MTS_SCHEDULE4_PRIORITY_PRT2 (0x00000080U)
#define RGX_CR_MTS_SCHEDULE4_PRIORITY_PRT3 (0x000000C0U)
#define RGX_CR_MTS_SCHEDULE4_CONTEXT_SHIFT (5U)
#define RGX_CR_MTS_SCHEDULE4_CONTEXT_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MTS_SCHEDULE4_CONTEXT_BGCTX (0x00000000U)
#define RGX_CR_MTS_SCHEDULE4_CONTEXT_INTCTX (0x00000020U)
#define RGX_CR_MTS_SCHEDULE4_TASK_SHIFT (4U)
#define RGX_CR_MTS_SCHEDULE4_TASK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MTS_SCHEDULE4_TASK_NON_COUNTED (0x00000000U)
#define RGX_CR_MTS_SCHEDULE4_TASK_COUNTED (0x00000010U)
#define RGX_CR_MTS_SCHEDULE4_DM_SHIFT (0U)
#define RGX_CR_MTS_SCHEDULE4_DM_CLRMSK (0xFFFFFFF0U)
#define RGX_CR_MTS_SCHEDULE4_DM_DM0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE4_DM_DM1 (0x00000001U)
#define RGX_CR_MTS_SCHEDULE4_DM_DM2 (0x00000002U)
#define RGX_CR_MTS_SCHEDULE4_DM_DM3 (0x00000003U)
#define RGX_CR_MTS_SCHEDULE4_DM_DM4 (0x00000004U)
#define RGX_CR_MTS_SCHEDULE4_DM_DM5 (0x00000005U)
#define RGX_CR_MTS_SCHEDULE4_DM_DM6 (0x00000006U)
#define RGX_CR_MTS_SCHEDULE4_DM_DM7 (0x00000007U)
#define RGX_CR_MTS_SCHEDULE4_DM_DM_ALL (0x0000000FU)

/*
    Register RGX_CR_MTS_SCHEDULE5
*/
#define RGX_CR_MTS_SCHEDULE5 (0x50B00U)
#define RGX_CR_MTS_SCHEDULE5_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_MTS_SCHEDULE5_HOST_SHIFT (8U)
#define RGX_CR_MTS_SCHEDULE5_HOST_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_MTS_SCHEDULE5_HOST_BG_TIMER (0x00000000U)
#define RGX_CR_MTS_SCHEDULE5_HOST_HOST (0x00000100U)
#define RGX_CR_MTS_SCHEDULE5_PRIORITY_SHIFT (6U)
#define RGX_CR_MTS_SCHEDULE5_PRIORITY_CLRMSK (0xFFFFFF3FU)
#define RGX_CR_MTS_SCHEDULE5_PRIORITY_PRT0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE5_PRIORITY_PRT1 (0x00000040U)
#define RGX_CR_MTS_SCHEDULE5_PRIORITY_PRT2 (0x00000080U)
#define RGX_CR_MTS_SCHEDULE5_PRIORITY_PRT3 (0x000000C0U)
#define RGX_CR_MTS_SCHEDULE5_CONTEXT_SHIFT (5U)
#define RGX_CR_MTS_SCHEDULE5_CONTEXT_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MTS_SCHEDULE5_CONTEXT_BGCTX (0x00000000U)
#define RGX_CR_MTS_SCHEDULE5_CONTEXT_INTCTX (0x00000020U)
#define RGX_CR_MTS_SCHEDULE5_TASK_SHIFT (4U)
#define RGX_CR_MTS_SCHEDULE5_TASK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MTS_SCHEDULE5_TASK_NON_COUNTED (0x00000000U)
#define RGX_CR_MTS_SCHEDULE5_TASK_COUNTED (0x00000010U)
#define RGX_CR_MTS_SCHEDULE5_DM_SHIFT (0U)
#define RGX_CR_MTS_SCHEDULE5_DM_CLRMSK (0xFFFFFFF0U)
#define RGX_CR_MTS_SCHEDULE5_DM_DM0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE5_DM_DM1 (0x00000001U)
#define RGX_CR_MTS_SCHEDULE5_DM_DM2 (0x00000002U)
#define RGX_CR_MTS_SCHEDULE5_DM_DM3 (0x00000003U)
#define RGX_CR_MTS_SCHEDULE5_DM_DM4 (0x00000004U)
#define RGX_CR_MTS_SCHEDULE5_DM_DM5 (0x00000005U)
#define RGX_CR_MTS_SCHEDULE5_DM_DM6 (0x00000006U)
#define RGX_CR_MTS_SCHEDULE5_DM_DM7 (0x00000007U)
#define RGX_CR_MTS_SCHEDULE5_DM_DM_ALL (0x0000000FU)

/*
    Register RGX_CR_MTS_SCHEDULE6
*/
#define RGX_CR_MTS_SCHEDULE6 (0x60B00U)
#define RGX_CR_MTS_SCHEDULE6_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_MTS_SCHEDULE6_HOST_SHIFT (8U)
#define RGX_CR_MTS_SCHEDULE6_HOST_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_MTS_SCHEDULE6_HOST_BG_TIMER (0x00000000U)
#define RGX_CR_MTS_SCHEDULE6_HOST_HOST (0x00000100U)
#define RGX_CR_MTS_SCHEDULE6_PRIORITY_SHIFT (6U)
#define RGX_CR_MTS_SCHEDULE6_PRIORITY_CLRMSK (0xFFFFFF3FU)
#define RGX_CR_MTS_SCHEDULE6_PRIORITY_PRT0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE6_PRIORITY_PRT1 (0x00000040U)
#define RGX_CR_MTS_SCHEDULE6_PRIORITY_PRT2 (0x00000080U)
#define RGX_CR_MTS_SCHEDULE6_PRIORITY_PRT3 (0x000000C0U)
#define RGX_CR_MTS_SCHEDULE6_CONTEXT_SHIFT (5U)
#define RGX_CR_MTS_SCHEDULE6_CONTEXT_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MTS_SCHEDULE6_CONTEXT_BGCTX (0x00000000U)
#define RGX_CR_MTS_SCHEDULE6_CONTEXT_INTCTX (0x00000020U)
#define RGX_CR_MTS_SCHEDULE6_TASK_SHIFT (4U)
#define RGX_CR_MTS_SCHEDULE6_TASK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MTS_SCHEDULE6_TASK_NON_COUNTED (0x00000000U)
#define RGX_CR_MTS_SCHEDULE6_TASK_COUNTED (0x00000010U)
#define RGX_CR_MTS_SCHEDULE6_DM_SHIFT (0U)
#define RGX_CR_MTS_SCHEDULE6_DM_CLRMSK (0xFFFFFFF0U)
#define RGX_CR_MTS_SCHEDULE6_DM_DM0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE6_DM_DM1 (0x00000001U)
#define RGX_CR_MTS_SCHEDULE6_DM_DM2 (0x00000002U)
#define RGX_CR_MTS_SCHEDULE6_DM_DM3 (0x00000003U)
#define RGX_CR_MTS_SCHEDULE6_DM_DM4 (0x00000004U)
#define RGX_CR_MTS_SCHEDULE6_DM_DM5 (0x00000005U)
#define RGX_CR_MTS_SCHEDULE6_DM_DM6 (0x00000006U)
#define RGX_CR_MTS_SCHEDULE6_DM_DM7 (0x00000007U)
#define RGX_CR_MTS_SCHEDULE6_DM_DM_ALL (0x0000000FU)

/*
    Register RGX_CR_MTS_SCHEDULE7
*/
#define RGX_CR_MTS_SCHEDULE7 (0x70B00U)
#define RGX_CR_MTS_SCHEDULE7_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_MTS_SCHEDULE7_HOST_SHIFT (8U)
#define RGX_CR_MTS_SCHEDULE7_HOST_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_MTS_SCHEDULE7_HOST_BG_TIMER (0x00000000U)
#define RGX_CR_MTS_SCHEDULE7_HOST_HOST (0x00000100U)
#define RGX_CR_MTS_SCHEDULE7_PRIORITY_SHIFT (6U)
#define RGX_CR_MTS_SCHEDULE7_PRIORITY_CLRMSK (0xFFFFFF3FU)
#define RGX_CR_MTS_SCHEDULE7_PRIORITY_PRT0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE7_PRIORITY_PRT1 (0x00000040U)
#define RGX_CR_MTS_SCHEDULE7_PRIORITY_PRT2 (0x00000080U)
#define RGX_CR_MTS_SCHEDULE7_PRIORITY_PRT3 (0x000000C0U)
#define RGX_CR_MTS_SCHEDULE7_CONTEXT_SHIFT (5U)
#define RGX_CR_MTS_SCHEDULE7_CONTEXT_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MTS_SCHEDULE7_CONTEXT_BGCTX (0x00000000U)
#define RGX_CR_MTS_SCHEDULE7_CONTEXT_INTCTX (0x00000020U)
#define RGX_CR_MTS_SCHEDULE7_TASK_SHIFT (4U)
#define RGX_CR_MTS_SCHEDULE7_TASK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MTS_SCHEDULE7_TASK_NON_COUNTED (0x00000000U)
#define RGX_CR_MTS_SCHEDULE7_TASK_COUNTED (0x00000010U)
#define RGX_CR_MTS_SCHEDULE7_DM_SHIFT (0U)
#define RGX_CR_MTS_SCHEDULE7_DM_CLRMSK (0xFFFFFFF0U)
#define RGX_CR_MTS_SCHEDULE7_DM_DM0 (0x00000000U)
#define RGX_CR_MTS_SCHEDULE7_DM_DM1 (0x00000001U)
#define RGX_CR_MTS_SCHEDULE7_DM_DM2 (0x00000002U)
#define RGX_CR_MTS_SCHEDULE7_DM_DM3 (0x00000003U)
#define RGX_CR_MTS_SCHEDULE7_DM_DM4 (0x00000004U)
#define RGX_CR_MTS_SCHEDULE7_DM_DM5 (0x00000005U)
#define RGX_CR_MTS_SCHEDULE7_DM_DM6 (0x00000006U)
#define RGX_CR_MTS_SCHEDULE7_DM_DM7 (0x00000007U)
#define RGX_CR_MTS_SCHEDULE7_DM_DM_ALL (0x0000000FU)

/*
    Register RGX_CR_MTS_BGCTX_THREAD0_DM_ASSOC
*/
#define RGX_CR_MTS_BGCTX_THREAD0_DM_ASSOC (0x0B30U)
#define RGX_CR_MTS_BGCTX_THREAD0_DM_ASSOC_MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_MTS_BGCTX_THREAD0_DM_ASSOC_DM_ASSOC_SHIFT (0U)
#define RGX_CR_MTS_BGCTX_THREAD0_DM_ASSOC_DM_ASSOC_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_MTS_BGCTX_THREAD1_DM_ASSOC
*/
#define RGX_CR_MTS_BGCTX_THREAD1_DM_ASSOC (0x0B38U)
#define RGX_CR_MTS_BGCTX_THREAD1_DM_ASSOC_MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_MTS_BGCTX_THREAD1_DM_ASSOC_DM_ASSOC_SHIFT (0U)
#define RGX_CR_MTS_BGCTX_THREAD1_DM_ASSOC_DM_ASSOC_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_MTS_INTCTX_THREAD0_DM_ASSOC
*/
#define RGX_CR_MTS_INTCTX_THREAD0_DM_ASSOC (0x0B40U)
#define RGX_CR_MTS_INTCTX_THREAD0_DM_ASSOC_MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_MTS_INTCTX_THREAD0_DM_ASSOC_DM_ASSOC_SHIFT (0U)
#define RGX_CR_MTS_INTCTX_THREAD0_DM_ASSOC_DM_ASSOC_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_MTS_INTCTX_THREAD1_DM_ASSOC
*/
#define RGX_CR_MTS_INTCTX_THREAD1_DM_ASSOC (0x0B48U)
#define RGX_CR_MTS_INTCTX_THREAD1_DM_ASSOC_MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_MTS_INTCTX_THREAD1_DM_ASSOC_DM_ASSOC_SHIFT (0U)
#define RGX_CR_MTS_INTCTX_THREAD1_DM_ASSOC_DM_ASSOC_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_MTS_GARTEN_WRAPPER_CONFIG
*/
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG (0x0B50U)
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG__S7_INFRA__MASKFULL \
	(IMG_UINT64_C(0x000FF0FFFFFFF701))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_MASKFULL \
	(IMG_UINT64_C(0x0000FFFFFFFFF001))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_FENCE_PC_BASE_SHIFT (44U)
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_FENCE_PC_BASE_CLRMSK \
	(IMG_UINT64_C(0xFFFF0FFFFFFFFFFF))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG__S7_INFRA__FENCE_PC_BASE_SHIFT (44U)
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG__S7_INFRA__FENCE_PC_BASE_CLRMSK \
	(IMG_UINT64_C(0xFFF00FFFFFFFFFFF))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_FENCE_DM_SHIFT (40U)
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_FENCE_DM_CLRMSK \
	(IMG_UINT64_C(0xFFFFF0FFFFFFFFFF))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_FENCE_ADDR_SHIFT (12U)
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_FENCE_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG__S7_INFRA__FENCE_PERSISTENCE_SHIFT (9U)
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG__S7_INFRA__FENCE_PERSISTENCE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF9FF))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG__S7_INFRA__FENCE_SLC_COHERENT_SHIFT \
	(8U)
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG__S7_INFRA__FENCE_SLC_COHERENT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFEFF))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG__S7_INFRA__FENCE_SLC_COHERENT_EN \
	(IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_IDLE_CTRL_SHIFT (0U)
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_IDLE_CTRL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_IDLE_CTRL_META \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_IDLE_CTRL_MTS \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_IDLE_CTRL__S7_INFRA__META \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_MTS_GARTEN_WRAPPER_CONFIG_IDLE_CTRL__S7_INFRA__MTS \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_MTS_DM0_INTERRUPT_ENABLE
*/
#define RGX_CR_MTS_DM0_INTERRUPT_ENABLE (0x0B58U)
#define RGX_CR_MTS_DM0_INTERRUPT_ENABLE_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MTS_DM0_INTERRUPT_ENABLE_INT_ENABLE_SHIFT (0U)
#define RGX_CR_MTS_DM0_INTERRUPT_ENABLE_INT_ENABLE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_MTS_DM1_INTERRUPT_ENABLE
*/
#define RGX_CR_MTS_DM1_INTERRUPT_ENABLE (0x0B60U)
#define RGX_CR_MTS_DM1_INTERRUPT_ENABLE_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MTS_DM1_INTERRUPT_ENABLE_INT_ENABLE_SHIFT (0U)
#define RGX_CR_MTS_DM1_INTERRUPT_ENABLE_INT_ENABLE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_MTS_DM2_INTERRUPT_ENABLE
*/
#define RGX_CR_MTS_DM2_INTERRUPT_ENABLE (0x0B68U)
#define RGX_CR_MTS_DM2_INTERRUPT_ENABLE_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MTS_DM2_INTERRUPT_ENABLE_INT_ENABLE_SHIFT (0U)
#define RGX_CR_MTS_DM2_INTERRUPT_ENABLE_INT_ENABLE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_MTS_DM3_INTERRUPT_ENABLE
*/
#define RGX_CR_MTS_DM3_INTERRUPT_ENABLE (0x0B70U)
#define RGX_CR_MTS_DM3_INTERRUPT_ENABLE_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MTS_DM3_INTERRUPT_ENABLE_INT_ENABLE_SHIFT (0U)
#define RGX_CR_MTS_DM3_INTERRUPT_ENABLE_INT_ENABLE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_MTS_DM4_INTERRUPT_ENABLE
*/
#define RGX_CR_MTS_DM4_INTERRUPT_ENABLE (0x0B78U)
#define RGX_CR_MTS_DM4_INTERRUPT_ENABLE_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MTS_DM4_INTERRUPT_ENABLE_INT_ENABLE_SHIFT (0U)
#define RGX_CR_MTS_DM4_INTERRUPT_ENABLE_INT_ENABLE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_MTS_DM5_INTERRUPT_ENABLE
*/
#define RGX_CR_MTS_DM5_INTERRUPT_ENABLE (0x0B80U)
#define RGX_CR_MTS_DM5_INTERRUPT_ENABLE_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MTS_DM5_INTERRUPT_ENABLE_INT_ENABLE_SHIFT (0U)
#define RGX_CR_MTS_DM5_INTERRUPT_ENABLE_INT_ENABLE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_MTS_INTCTX
*/
#define RGX_CR_MTS_INTCTX (0x0B98U)
#define RGX_CR_MTS_INTCTX_MASKFULL (IMG_UINT64_C(0x000000003FFFFFFF))
#define RGX_CR_MTS_INTCTX_DM_HOST_SCHEDULE_SHIFT (22U)
#define RGX_CR_MTS_INTCTX_DM_HOST_SCHEDULE_CLRMSK (0xC03FFFFFU)
#define RGX_CR_MTS_INTCTX_DM_PTR_SHIFT (18U)
#define RGX_CR_MTS_INTCTX_DM_PTR_CLRMSK (0xFFC3FFFFU)
#define RGX_CR_MTS_INTCTX_THREAD_ACTIVE_SHIFT (16U)
#define RGX_CR_MTS_INTCTX_THREAD_ACTIVE_CLRMSK (0xFFFCFFFFU)
#define RGX_CR_MTS_INTCTX_DM_TIMER_SCHEDULE_SHIFT (8U)
#define RGX_CR_MTS_INTCTX_DM_TIMER_SCHEDULE_CLRMSK (0xFFFF00FFU)
#define RGX_CR_MTS_INTCTX_DM_INTERRUPT_SCHEDULE_SHIFT (0U)
#define RGX_CR_MTS_INTCTX_DM_INTERRUPT_SCHEDULE_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_MTS_BGCTX
*/
#define RGX_CR_MTS_BGCTX (0x0BA0U)
#define RGX_CR_MTS_BGCTX_MASKFULL (IMG_UINT64_C(0x0000000000003FFF))
#define RGX_CR_MTS_BGCTX_DM_PTR_SHIFT (10U)
#define RGX_CR_MTS_BGCTX_DM_PTR_CLRMSK (0xFFFFC3FFU)
#define RGX_CR_MTS_BGCTX_THREAD_ACTIVE_SHIFT (8U)
#define RGX_CR_MTS_BGCTX_THREAD_ACTIVE_CLRMSK (0xFFFFFCFFU)
#define RGX_CR_MTS_BGCTX_DM_NONCOUNTED_SCHEDULE_SHIFT (0U)
#define RGX_CR_MTS_BGCTX_DM_NONCOUNTED_SCHEDULE_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE
*/
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE (0x0BA8U)
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_MASKFULL \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM7_SHIFT (56U)
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM7_CLRMSK \
	(IMG_UINT64_C(0x00FFFFFFFFFFFFFF))
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM6_SHIFT (48U)
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM6_CLRMSK \
	(IMG_UINT64_C(0xFF00FFFFFFFFFFFF))
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM5_SHIFT (40U)
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM5_CLRMSK \
	(IMG_UINT64_C(0xFFFF00FFFFFFFFFF))
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM4_SHIFT (32U)
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM4_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF00FFFFFFFF))
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM3_SHIFT (24U)
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM3_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00FFFFFF))
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM2_SHIFT (16U)
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM2_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFF00FFFF))
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM1_SHIFT (8U)
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM1_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF00FF))
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM0_SHIFT (0U)
#define RGX_CR_MTS_BGCTX_COUNTED_SCHEDULE_DM0_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFF00))

/*
    Register RGX_CR_MTS_GPU_INT_STATUS
*/
#define RGX_CR_MTS_GPU_INT_STATUS (0x0BB0U)
#define RGX_CR_MTS_GPU_INT_STATUS_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MTS_GPU_INT_STATUS_STATUS_SHIFT (0U)
#define RGX_CR_MTS_GPU_INT_STATUS_STATUS_CLRMSK (0x00000000U)

/*
    Register RGX_CR_MTS_SCHEDULE_ENABLE
*/
#define RGX_CR_MTS_SCHEDULE_ENABLE (0x0BC8U)
#define RGX_CR_MTS_SCHEDULE_ENABLE_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_MTS_SCHEDULE_ENABLE_MASK_SHIFT (0U)
#define RGX_CR_MTS_SCHEDULE_ENABLE_MASK_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_IRQ_OS0_EVENT_STATUS
*/
#define RGX_CR_IRQ_OS0_EVENT_STATUS (0x0BD8U)
#define RGX_CR_IRQ_OS0_EVENT_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS0_EVENT_STATUS_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS0_EVENT_STATUS_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS0_EVENT_STATUS_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS0_EVENT_CLEAR
*/
#define RGX_CR_IRQ_OS0_EVENT_CLEAR (0x0BE8U)
#define RGX_CR_IRQ_OS0_EVENT_CLEAR_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS0_EVENT_CLEAR_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS0_EVENT_CLEAR_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS0_EVENT_CLEAR_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS1_EVENT_STATUS
*/
#define RGX_CR_IRQ_OS1_EVENT_STATUS (0x10BD8U)
#define RGX_CR_IRQ_OS1_EVENT_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS1_EVENT_STATUS_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS1_EVENT_STATUS_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS1_EVENT_STATUS_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS1_EVENT_CLEAR
*/
#define RGX_CR_IRQ_OS1_EVENT_CLEAR (0x10BE8U)
#define RGX_CR_IRQ_OS1_EVENT_CLEAR_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS1_EVENT_CLEAR_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS1_EVENT_CLEAR_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS1_EVENT_CLEAR_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS2_EVENT_STATUS
*/
#define RGX_CR_IRQ_OS2_EVENT_STATUS (0x20BD8U)
#define RGX_CR_IRQ_OS2_EVENT_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS2_EVENT_STATUS_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS2_EVENT_STATUS_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS2_EVENT_STATUS_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS2_EVENT_CLEAR
*/
#define RGX_CR_IRQ_OS2_EVENT_CLEAR (0x20BE8U)
#define RGX_CR_IRQ_OS2_EVENT_CLEAR_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS2_EVENT_CLEAR_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS2_EVENT_CLEAR_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS2_EVENT_CLEAR_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS3_EVENT_STATUS
*/
#define RGX_CR_IRQ_OS3_EVENT_STATUS (0x30BD8U)
#define RGX_CR_IRQ_OS3_EVENT_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS3_EVENT_STATUS_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS3_EVENT_STATUS_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS3_EVENT_STATUS_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS3_EVENT_CLEAR
*/
#define RGX_CR_IRQ_OS3_EVENT_CLEAR (0x30BE8U)
#define RGX_CR_IRQ_OS3_EVENT_CLEAR_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS3_EVENT_CLEAR_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS3_EVENT_CLEAR_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS3_EVENT_CLEAR_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS4_EVENT_STATUS
*/
#define RGX_CR_IRQ_OS4_EVENT_STATUS (0x40BD8U)
#define RGX_CR_IRQ_OS4_EVENT_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS4_EVENT_STATUS_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS4_EVENT_STATUS_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS4_EVENT_STATUS_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS4_EVENT_CLEAR
*/
#define RGX_CR_IRQ_OS4_EVENT_CLEAR (0x40BE8U)
#define RGX_CR_IRQ_OS4_EVENT_CLEAR_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS4_EVENT_CLEAR_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS4_EVENT_CLEAR_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS4_EVENT_CLEAR_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS5_EVENT_STATUS
*/
#define RGX_CR_IRQ_OS5_EVENT_STATUS (0x50BD8U)
#define RGX_CR_IRQ_OS5_EVENT_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS5_EVENT_STATUS_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS5_EVENT_STATUS_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS5_EVENT_STATUS_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS5_EVENT_CLEAR
*/
#define RGX_CR_IRQ_OS5_EVENT_CLEAR (0x50BE8U)
#define RGX_CR_IRQ_OS5_EVENT_CLEAR_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS5_EVENT_CLEAR_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS5_EVENT_CLEAR_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS5_EVENT_CLEAR_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS6_EVENT_STATUS
*/
#define RGX_CR_IRQ_OS6_EVENT_STATUS (0x60BD8U)
#define RGX_CR_IRQ_OS6_EVENT_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS6_EVENT_STATUS_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS6_EVENT_STATUS_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS6_EVENT_STATUS_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS6_EVENT_CLEAR
*/
#define RGX_CR_IRQ_OS6_EVENT_CLEAR (0x60BE8U)
#define RGX_CR_IRQ_OS6_EVENT_CLEAR_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS6_EVENT_CLEAR_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS6_EVENT_CLEAR_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS6_EVENT_CLEAR_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS7_EVENT_STATUS
*/
#define RGX_CR_IRQ_OS7_EVENT_STATUS (0x70BD8U)
#define RGX_CR_IRQ_OS7_EVENT_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS7_EVENT_STATUS_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS7_EVENT_STATUS_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS7_EVENT_STATUS_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_IRQ_OS7_EVENT_CLEAR
*/
#define RGX_CR_IRQ_OS7_EVENT_CLEAR (0x70BE8U)
#define RGX_CR_IRQ_OS7_EVENT_CLEAR_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_IRQ_OS7_EVENT_CLEAR_SOURCE_SHIFT (0U)
#define RGX_CR_IRQ_OS7_EVENT_CLEAR_SOURCE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_IRQ_OS7_EVENT_CLEAR_SOURCE_EN (0x00000001U)

/*
    Register RGX_CR_META_BOOT
*/
#define RGX_CR_META_BOOT (0x0BF8U)
#define RGX_CR_META_BOOT_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_META_BOOT_MODE_SHIFT (0U)
#define RGX_CR_META_BOOT_MODE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_META_BOOT_MODE_EN (0x00000001U)

/*
    Register RGX_CR_GARTEN_SLC
*/
#define RGX_CR_GARTEN_SLC (0x0BB8U)
#define RGX_CR_GARTEN_SLC_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_GARTEN_SLC_FORCE_COHERENCY_SHIFT (0U)
#define RGX_CR_GARTEN_SLC_FORCE_COHERENCY_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_GARTEN_SLC_FORCE_COHERENCY_EN (0x00000001U)

/*
    Register RGX_CR_PPP
*/
#define RGX_CR_PPP (0x0CD0U)
#define RGX_CR_PPP_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PPP_CHECKSUM_SHIFT (0U)
#define RGX_CR_PPP_CHECKSUM_CLRMSK (0x00000000U)

#define RGX_CR_ISP_RENDER_DIR_TYPE_MASK (0x00000003U)
/*
Top-left to bottom-right */
#define RGX_CR_ISP_RENDER_DIR_TYPE_TL2BR (0x00000000U)
/*
Top-right to bottom-left */
#define RGX_CR_ISP_RENDER_DIR_TYPE_TR2BL (0x00000001U)
/*
Bottom-left to top-right */
#define RGX_CR_ISP_RENDER_DIR_TYPE_BL2TR (0x00000002U)
/*
Bottom-right to top-left */
#define RGX_CR_ISP_RENDER_DIR_TYPE_BR2TL (0x00000003U)

#define RGX_CR_ISP_RENDER_MODE_TYPE_MASK (0x00000003U)
/*
Normal render     */
#define RGX_CR_ISP_RENDER_MODE_TYPE_NORM (0x00000000U)
/*
Fast 2D render    */
#define RGX_CR_ISP_RENDER_MODE_TYPE_FAST_2D (0x00000002U)
/*
Fast scale render */
#define RGX_CR_ISP_RENDER_MODE_TYPE_FAST_SCALE (0x00000003U)

/*
    Register RGX_CR_ISP_RENDER
*/
#define RGX_CR_ISP_RENDER (0x0F08U)
#define RGX_CR_ISP_RENDER_MASKFULL (IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_ISP_RENDER_FAST_RENDER_FORCE_PROTECT_SHIFT (8U)
#define RGX_CR_ISP_RENDER_FAST_RENDER_FORCE_PROTECT_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_ISP_RENDER_FAST_RENDER_FORCE_PROTECT_EN (0x00000100U)
#define RGX_CR_ISP_RENDER_PROCESS_PROTECTED_TILES_SHIFT (7U)
#define RGX_CR_ISP_RENDER_PROCESS_PROTECTED_TILES_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_ISP_RENDER_PROCESS_PROTECTED_TILES_EN (0x00000080U)
#define RGX_CR_ISP_RENDER_PROCESS_UNPROTECTED_TILES_SHIFT (6U)
#define RGX_CR_ISP_RENDER_PROCESS_UNPROTECTED_TILES_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_ISP_RENDER_PROCESS_UNPROTECTED_TILES_EN (0x00000040U)
#define RGX_CR_ISP_RENDER_DISABLE_EOMT_SHIFT (5U)
#define RGX_CR_ISP_RENDER_DISABLE_EOMT_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_ISP_RENDER_DISABLE_EOMT_EN (0x00000020U)
#define RGX_CR_ISP_RENDER_RESUME_SHIFT (4U)
#define RGX_CR_ISP_RENDER_RESUME_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_ISP_RENDER_RESUME_EN (0x00000010U)
#define RGX_CR_ISP_RENDER_DIR_SHIFT (2U)
#define RGX_CR_ISP_RENDER_DIR_CLRMSK (0xFFFFFFF3U)
#define RGX_CR_ISP_RENDER_DIR_TL2BR (0x00000000U)
#define RGX_CR_ISP_RENDER_DIR_TR2BL (0x00000004U)
#define RGX_CR_ISP_RENDER_DIR_BL2TR (0x00000008U)
#define RGX_CR_ISP_RENDER_DIR_BR2TL (0x0000000CU)
#define RGX_CR_ISP_RENDER_MODE_SHIFT (0U)
#define RGX_CR_ISP_RENDER_MODE_CLRMSK (0xFFFFFFFCU)
#define RGX_CR_ISP_RENDER_MODE_NORM (0x00000000U)
#define RGX_CR_ISP_RENDER_MODE_FAST_2D (0x00000002U)
#define RGX_CR_ISP_RENDER_MODE_FAST_SCALE (0x00000003U)

/*
    Register RGX_CR_ISP_CTL
*/
#define RGX_CR_ISP_CTL (0x0F38U)
#define RGX_CR_ISP_CTL_MASKFULL (IMG_UINT64_C(0x00000000FFFFF3FF))
#define RGX_CR_ISP_CTL_SKIP_INIT_HDRS_SHIFT (31U)
#define RGX_CR_ISP_CTL_SKIP_INIT_HDRS_CLRMSK (0x7FFFFFFFU)
#define RGX_CR_ISP_CTL_SKIP_INIT_HDRS_EN (0x80000000U)
#define RGX_CR_ISP_CTL_LINE_STYLE_SHIFT (30U)
#define RGX_CR_ISP_CTL_LINE_STYLE_CLRMSK (0xBFFFFFFFU)
#define RGX_CR_ISP_CTL_LINE_STYLE_EN (0x40000000U)
#define RGX_CR_ISP_CTL_LINE_STYLE_PIX_SHIFT (29U)
#define RGX_CR_ISP_CTL_LINE_STYLE_PIX_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_ISP_CTL_LINE_STYLE_PIX_EN (0x20000000U)
#define RGX_CR_ISP_CTL_PAIR_TILES_VERT_SHIFT (28U)
#define RGX_CR_ISP_CTL_PAIR_TILES_VERT_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_ISP_CTL_PAIR_TILES_VERT_EN (0x10000000U)
#define RGX_CR_ISP_CTL_PAIR_TILES_SHIFT (27U)
#define RGX_CR_ISP_CTL_PAIR_TILES_CLRMSK (0xF7FFFFFFU)
#define RGX_CR_ISP_CTL_PAIR_TILES_EN (0x08000000U)
#define RGX_CR_ISP_CTL_CREQ_BUF_EN_SHIFT (26U)
#define RGX_CR_ISP_CTL_CREQ_BUF_EN_CLRMSK (0xFBFFFFFFU)
#define RGX_CR_ISP_CTL_CREQ_BUF_EN_EN (0x04000000U)
#define RGX_CR_ISP_CTL_TILE_AGE_EN_SHIFT (25U)
#define RGX_CR_ISP_CTL_TILE_AGE_EN_CLRMSK (0xFDFFFFFFU)
#define RGX_CR_ISP_CTL_TILE_AGE_EN_EN (0x02000000U)
#define RGX_CR_ISP_CTL_ISP_SAMPLE_POS_MODE_SHIFT (23U)
#define RGX_CR_ISP_CTL_ISP_SAMPLE_POS_MODE_CLRMSK (0xFE7FFFFFU)
#define RGX_CR_ISP_CTL_ISP_SAMPLE_POS_MODE_DX9 (0x00000000U)
#define RGX_CR_ISP_CTL_ISP_SAMPLE_POS_MODE_DX10 (0x00800000U)
#define RGX_CR_ISP_CTL_ISP_SAMPLE_POS_MODE_OGL (0x01000000U)
#define RGX_CR_ISP_CTL_NUM_TILES_PER_USC_SHIFT (21U)
#define RGX_CR_ISP_CTL_NUM_TILES_PER_USC_CLRMSK (0xFF9FFFFFU)
#define RGX_CR_ISP_CTL_DBIAS_IS_INT_SHIFT (20U)
#define RGX_CR_ISP_CTL_DBIAS_IS_INT_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_ISP_CTL_DBIAS_IS_INT_EN (0x00100000U)
#define RGX_CR_ISP_CTL_OVERLAP_CHECK_MODE_SHIFT (19U)
#define RGX_CR_ISP_CTL_OVERLAP_CHECK_MODE_CLRMSK (0xFFF7FFFFU)
#define RGX_CR_ISP_CTL_OVERLAP_CHECK_MODE_EN (0x00080000U)
#define RGX_CR_ISP_CTL_PT_UPFRONT_DEPTH_DISABLE_SHIFT (18U)
#define RGX_CR_ISP_CTL_PT_UPFRONT_DEPTH_DISABLE_CLRMSK (0xFFFBFFFFU)
#define RGX_CR_ISP_CTL_PT_UPFRONT_DEPTH_DISABLE_EN (0x00040000U)
#define RGX_CR_ISP_CTL_PROCESS_EMPTY_TILES_SHIFT (17U)
#define RGX_CR_ISP_CTL_PROCESS_EMPTY_TILES_CLRMSK (0xFFFDFFFFU)
#define RGX_CR_ISP_CTL_PROCESS_EMPTY_TILES_EN (0x00020000U)
#define RGX_CR_ISP_CTL_SAMPLE_POS_SHIFT (16U)
#define RGX_CR_ISP_CTL_SAMPLE_POS_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_ISP_CTL_SAMPLE_POS_EN (0x00010000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_SHIFT (12U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_CLRMSK (0xFFFF0FFFU)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_ONE (0x00000000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_TWO (0x00001000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_THREE (0x00002000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_FOUR (0x00003000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_FIVE (0x00004000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_SIX (0x00005000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_SEVEN (0x00006000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_EIGHT (0x00007000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_NINE (0x00008000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_TEN (0x00009000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_ELEVEN (0x0000A000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_TWELVE (0x0000B000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_THIRTEEN (0x0000C000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_FOURTEEN (0x0000D000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_FIFTEEN (0x0000E000U)
#define RGX_CR_ISP_CTL_PIPE_ENABLE_PIPE_SIXTEEN (0x0000F000U)
#define RGX_CR_ISP_CTL_VALID_ID_SHIFT (4U)
#define RGX_CR_ISP_CTL_VALID_ID_CLRMSK (0xFFFFFC0FU)
#define RGX_CR_ISP_CTL_UPASS_START_SHIFT (0U)
#define RGX_CR_ISP_CTL_UPASS_START_CLRMSK (0xFFFFFFF0U)

/*
    Register RGX_CR_ISP_STATUS
*/
#define RGX_CR_ISP_STATUS (0x1038U)
#define RGX_CR_ISP_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000000007))
#define RGX_CR_ISP_STATUS_SPLIT_MAX_SHIFT (2U)
#define RGX_CR_ISP_STATUS_SPLIT_MAX_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_ISP_STATUS_SPLIT_MAX_EN (0x00000004U)
#define RGX_CR_ISP_STATUS_ACTIVE_SHIFT (1U)
#define RGX_CR_ISP_STATUS_ACTIVE_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_ISP_STATUS_ACTIVE_EN (0x00000002U)
#define RGX_CR_ISP_STATUS_EOR_SHIFT (0U)
#define RGX_CR_ISP_STATUS_EOR_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_ISP_STATUS_EOR_EN (0x00000001U)

/*
    Register group: RGX_CR_ISP_XTP_RESUME, with 64 repeats
*/
#define RGX_CR_ISP_XTP_RESUME_REPEATCOUNT (64U)
/*
    Register RGX_CR_ISP_XTP_RESUME0
*/
#define RGX_CR_ISP_XTP_RESUME0 (0x3A00U)
#define RGX_CR_ISP_XTP_RESUME0_MASKFULL (IMG_UINT64_C(0x00000000003FF3FF))
#define RGX_CR_ISP_XTP_RESUME0_TILE_X_SHIFT (12U)
#define RGX_CR_ISP_XTP_RESUME0_TILE_X_CLRMSK (0xFFC00FFFU)
#define RGX_CR_ISP_XTP_RESUME0_TILE_Y_SHIFT (0U)
#define RGX_CR_ISP_XTP_RESUME0_TILE_Y_CLRMSK (0xFFFFFC00U)

/*
    Register group: RGX_CR_ISP_XTP_STORE, with 32 repeats
*/
#define RGX_CR_ISP_XTP_STORE_REPEATCOUNT (32U)
/*
    Register RGX_CR_ISP_XTP_STORE0
*/
#define RGX_CR_ISP_XTP_STORE0 (0x3C00U)
#define RGX_CR_ISP_XTP_STORE0_MASKFULL (IMG_UINT64_C(0x000000007F3FF3FF))
#define RGX_CR_ISP_XTP_STORE0_ACTIVE_SHIFT (30U)
#define RGX_CR_ISP_XTP_STORE0_ACTIVE_CLRMSK (0xBFFFFFFFU)
#define RGX_CR_ISP_XTP_STORE0_ACTIVE_EN (0x40000000U)
#define RGX_CR_ISP_XTP_STORE0_EOR_SHIFT (29U)
#define RGX_CR_ISP_XTP_STORE0_EOR_CLRMSK (0xDFFFFFFFU)
#define RGX_CR_ISP_XTP_STORE0_EOR_EN (0x20000000U)
#define RGX_CR_ISP_XTP_STORE0_TILE_LAST_SHIFT (28U)
#define RGX_CR_ISP_XTP_STORE0_TILE_LAST_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_ISP_XTP_STORE0_TILE_LAST_EN (0x10000000U)
#define RGX_CR_ISP_XTP_STORE0_MT_SHIFT (24U)
#define RGX_CR_ISP_XTP_STORE0_MT_CLRMSK (0xF0FFFFFFU)
#define RGX_CR_ISP_XTP_STORE0_TILE_X_SHIFT (12U)
#define RGX_CR_ISP_XTP_STORE0_TILE_X_CLRMSK (0xFFC00FFFU)
#define RGX_CR_ISP_XTP_STORE0_TILE_Y_SHIFT (0U)
#define RGX_CR_ISP_XTP_STORE0_TILE_Y_CLRMSK (0xFFFFFC00U)

/*
    Register group: RGX_CR_BIF_CAT_BASE, with 8 repeats
*/
#define RGX_CR_BIF_CAT_BASE_REPEATCOUNT (8U)
/*
    Register RGX_CR_BIF_CAT_BASE0
*/
#define RGX_CR_BIF_CAT_BASE0 (0x1200U)
#define RGX_CR_BIF_CAT_BASE0_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_BIF_CAT_BASE0_ADDR_SHIFT (12U)
#define RGX_CR_BIF_CAT_BASE0_ADDR_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_CAT_BASE0_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_BIF_CAT_BASE0_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_CAT_BASE1
*/
#define RGX_CR_BIF_CAT_BASE1 (0x1208U)
#define RGX_CR_BIF_CAT_BASE1_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_BIF_CAT_BASE1_ADDR_SHIFT (12U)
#define RGX_CR_BIF_CAT_BASE1_ADDR_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_CAT_BASE1_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_BIF_CAT_BASE1_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_CAT_BASE2
*/
#define RGX_CR_BIF_CAT_BASE2 (0x1210U)
#define RGX_CR_BIF_CAT_BASE2_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_BIF_CAT_BASE2_ADDR_SHIFT (12U)
#define RGX_CR_BIF_CAT_BASE2_ADDR_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_CAT_BASE2_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_BIF_CAT_BASE2_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_CAT_BASE3
*/
#define RGX_CR_BIF_CAT_BASE3 (0x1218U)
#define RGX_CR_BIF_CAT_BASE3_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_BIF_CAT_BASE3_ADDR_SHIFT (12U)
#define RGX_CR_BIF_CAT_BASE3_ADDR_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_CAT_BASE3_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_BIF_CAT_BASE3_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_CAT_BASE4
*/
#define RGX_CR_BIF_CAT_BASE4 (0x1220U)
#define RGX_CR_BIF_CAT_BASE4_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_BIF_CAT_BASE4_ADDR_SHIFT (12U)
#define RGX_CR_BIF_CAT_BASE4_ADDR_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_CAT_BASE4_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_BIF_CAT_BASE4_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_CAT_BASE5
*/
#define RGX_CR_BIF_CAT_BASE5 (0x1228U)
#define RGX_CR_BIF_CAT_BASE5_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_BIF_CAT_BASE5_ADDR_SHIFT (12U)
#define RGX_CR_BIF_CAT_BASE5_ADDR_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_CAT_BASE5_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_BIF_CAT_BASE5_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_CAT_BASE6
*/
#define RGX_CR_BIF_CAT_BASE6 (0x1230U)
#define RGX_CR_BIF_CAT_BASE6_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_BIF_CAT_BASE6_ADDR_SHIFT (12U)
#define RGX_CR_BIF_CAT_BASE6_ADDR_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_CAT_BASE6_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_BIF_CAT_BASE6_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_CAT_BASE7
*/
#define RGX_CR_BIF_CAT_BASE7 (0x1238U)
#define RGX_CR_BIF_CAT_BASE7_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_BIF_CAT_BASE7_ADDR_SHIFT (12U)
#define RGX_CR_BIF_CAT_BASE7_ADDR_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_CAT_BASE7_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_BIF_CAT_BASE7_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_CAT_BASE_INDEX
*/
#define RGX_CR_BIF_CAT_BASE_INDEX (0x1240U)
#define RGX_CR_BIF_CAT_BASE_INDEX_MASKFULL (IMG_UINT64_C(0x00070707073F0707))
#define RGX_CR_BIF_CAT_BASE_INDEX_RVTX_SHIFT (48U)
#define RGX_CR_BIF_CAT_BASE_INDEX_RVTX_CLRMSK (IMG_UINT64_C(0xFFF8FFFFFFFFFFFF))
#define RGX_CR_BIF_CAT_BASE_INDEX_RAY_SHIFT (40U)
#define RGX_CR_BIF_CAT_BASE_INDEX_RAY_CLRMSK (IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_BIF_CAT_BASE_INDEX_HOST_SHIFT (32U)
#define RGX_CR_BIF_CAT_BASE_INDEX_HOST_CLRMSK (IMG_UINT64_C(0xFFFFFFF8FFFFFFFF))
#define RGX_CR_BIF_CAT_BASE_INDEX_TLA_SHIFT (24U)
#define RGX_CR_BIF_CAT_BASE_INDEX_TLA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFF8FFFFFF))
#define RGX_CR_BIF_CAT_BASE_INDEX_TDM_SHIFT (19U)
#define RGX_CR_BIF_CAT_BASE_INDEX_TDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFC7FFFF))
#define RGX_CR_BIF_CAT_BASE_INDEX_CDM_SHIFT (16U)
#define RGX_CR_BIF_CAT_BASE_INDEX_CDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFF8FFFF))
#define RGX_CR_BIF_CAT_BASE_INDEX_PIXEL_SHIFT (8U)
#define RGX_CR_BIF_CAT_BASE_INDEX_PIXEL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF8FF))
#define RGX_CR_BIF_CAT_BASE_INDEX_TA_SHIFT (0U)
#define RGX_CR_BIF_CAT_BASE_INDEX_TA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFF8))

/*
    Register RGX_CR_BIF_PM_CAT_BASE_VCE0
*/
#define RGX_CR_BIF_PM_CAT_BASE_VCE0 (0x1248U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_MASKFULL (IMG_UINT64_C(0x0FFFFFFFFFFFF003))
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_INIT_PAGE_SHIFT (40U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_INIT_PAGE_CLRMSK \
	(IMG_UINT64_C(0xF00000FFFFFFFFFF))
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_ADDR_SHIFT (12U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_WRAP_SHIFT (1U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_WRAP_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_WRAP_EN (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_VALID_SHIFT (0U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_VALID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_BIF_PM_CAT_BASE_VCE0_VALID_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_BIF_PM_CAT_BASE_TE0
*/
#define RGX_CR_BIF_PM_CAT_BASE_TE0 (0x1250U)
#define RGX_CR_BIF_PM_CAT_BASE_TE0_MASKFULL (IMG_UINT64_C(0x0FFFFFFFFFFFF003))
#define RGX_CR_BIF_PM_CAT_BASE_TE0_INIT_PAGE_SHIFT (40U)
#define RGX_CR_BIF_PM_CAT_BASE_TE0_INIT_PAGE_CLRMSK \
	(IMG_UINT64_C(0xF00000FFFFFFFFFF))
#define RGX_CR_BIF_PM_CAT_BASE_TE0_ADDR_SHIFT (12U)
#define RGX_CR_BIF_PM_CAT_BASE_TE0_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_PM_CAT_BASE_TE0_WRAP_SHIFT (1U)
#define RGX_CR_BIF_PM_CAT_BASE_TE0_WRAP_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_BIF_PM_CAT_BASE_TE0_WRAP_EN (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_BIF_PM_CAT_BASE_TE0_VALID_SHIFT (0U)
#define RGX_CR_BIF_PM_CAT_BASE_TE0_VALID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_BIF_PM_CAT_BASE_TE0_VALID_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_BIF_PM_CAT_BASE_ALIST0
*/
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0 (0x1260U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_MASKFULL \
	(IMG_UINT64_C(0x0FFFFFFFFFFFF003))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_INIT_PAGE_SHIFT (40U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_INIT_PAGE_CLRMSK \
	(IMG_UINT64_C(0xF00000FFFFFFFFFF))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_ADDR_SHIFT (12U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_WRAP_SHIFT (1U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_WRAP_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_WRAP_EN (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_VALID_SHIFT (0U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_VALID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST0_VALID_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_BIF_PM_CAT_BASE_VCE1
*/
#define RGX_CR_BIF_PM_CAT_BASE_VCE1 (0x1268U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_MASKFULL (IMG_UINT64_C(0x0FFFFFFFFFFFF003))
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_INIT_PAGE_SHIFT (40U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_INIT_PAGE_CLRMSK \
	(IMG_UINT64_C(0xF00000FFFFFFFFFF))
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_ADDR_SHIFT (12U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_WRAP_SHIFT (1U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_WRAP_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_WRAP_EN (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_VALID_SHIFT (0U)
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_VALID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_BIF_PM_CAT_BASE_VCE1_VALID_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_BIF_PM_CAT_BASE_TE1
*/
#define RGX_CR_BIF_PM_CAT_BASE_TE1 (0x1270U)
#define RGX_CR_BIF_PM_CAT_BASE_TE1_MASKFULL (IMG_UINT64_C(0x0FFFFFFFFFFFF003))
#define RGX_CR_BIF_PM_CAT_BASE_TE1_INIT_PAGE_SHIFT (40U)
#define RGX_CR_BIF_PM_CAT_BASE_TE1_INIT_PAGE_CLRMSK \
	(IMG_UINT64_C(0xF00000FFFFFFFFFF))
#define RGX_CR_BIF_PM_CAT_BASE_TE1_ADDR_SHIFT (12U)
#define RGX_CR_BIF_PM_CAT_BASE_TE1_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_PM_CAT_BASE_TE1_WRAP_SHIFT (1U)
#define RGX_CR_BIF_PM_CAT_BASE_TE1_WRAP_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_BIF_PM_CAT_BASE_TE1_WRAP_EN (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_BIF_PM_CAT_BASE_TE1_VALID_SHIFT (0U)
#define RGX_CR_BIF_PM_CAT_BASE_TE1_VALID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_BIF_PM_CAT_BASE_TE1_VALID_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_BIF_PM_CAT_BASE_ALIST1
*/
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1 (0x1280U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_MASKFULL \
	(IMG_UINT64_C(0x0FFFFFFFFFFFF003))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_INIT_PAGE_SHIFT (40U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_INIT_PAGE_CLRMSK \
	(IMG_UINT64_C(0xF00000FFFFFFFFFF))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_ADDR_SHIFT (12U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_WRAP_SHIFT (1U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_WRAP_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_WRAP_EN (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_VALID_SHIFT (0U)
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_VALID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_BIF_PM_CAT_BASE_ALIST1_VALID_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_BIF_MMU_ENTRY_STATUS
*/
#define RGX_CR_BIF_MMU_ENTRY_STATUS (0x1288U)
#define RGX_CR_BIF_MMU_ENTRY_STATUS_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF0F3))
#define RGX_CR_BIF_MMU_ENTRY_STATUS_ADDRESS_SHIFT (12U)
#define RGX_CR_BIF_MMU_ENTRY_STATUS_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_BIF_MMU_ENTRY_STATUS_CAT_BASE_SHIFT (4U)
#define RGX_CR_BIF_MMU_ENTRY_STATUS_CAT_BASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFF0F))
#define RGX_CR_BIF_MMU_ENTRY_STATUS_DATA_TYPE_SHIFT (0U)
#define RGX_CR_BIF_MMU_ENTRY_STATUS_DATA_TYPE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFC))

/*
    Register RGX_CR_BIF_MMU_ENTRY
*/
#define RGX_CR_BIF_MMU_ENTRY (0x1290U)
#define RGX_CR_BIF_MMU_ENTRY_MASKFULL (IMG_UINT64_C(0x0000000000000003))
#define RGX_CR_BIF_MMU_ENTRY_ENABLE_SHIFT (1U)
#define RGX_CR_BIF_MMU_ENTRY_ENABLE_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_BIF_MMU_ENTRY_ENABLE_EN (0x00000002U)
#define RGX_CR_BIF_MMU_ENTRY_PENDING_SHIFT (0U)
#define RGX_CR_BIF_MMU_ENTRY_PENDING_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_BIF_MMU_ENTRY_PENDING_EN (0x00000001U)

/*
    Register RGX_CR_BIF_CTRL_INVAL
*/
#define RGX_CR_BIF_CTRL_INVAL (0x12A0U)
#define RGX_CR_BIF_CTRL_INVAL_MASKFULL (IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_BIF_CTRL_INVAL_TLB1_SHIFT (3U)
#define RGX_CR_BIF_CTRL_INVAL_TLB1_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_BIF_CTRL_INVAL_TLB1_EN (0x00000008U)
#define RGX_CR_BIF_CTRL_INVAL_PC_SHIFT (2U)
#define RGX_CR_BIF_CTRL_INVAL_PC_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_BIF_CTRL_INVAL_PC_EN (0x00000004U)
#define RGX_CR_BIF_CTRL_INVAL_PD_SHIFT (1U)
#define RGX_CR_BIF_CTRL_INVAL_PD_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_BIF_CTRL_INVAL_PD_EN (0x00000002U)
#define RGX_CR_BIF_CTRL_INVAL_PT_SHIFT (0U)
#define RGX_CR_BIF_CTRL_INVAL_PT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_BIF_CTRL_INVAL_PT_EN (0x00000001U)

/*
    Register RGX_CR_BIF_CTRL
*/
#define RGX_CR_BIF_CTRL (0x12A8U)
#define RGX_CR_BIF_CTRL__XE_MEM__MASKFULL (IMG_UINT64_C(0x000000000000033F))
#define RGX_CR_BIF_CTRL_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_BIF_CTRL__XE_MEM__PAUSE_MMU_CPU_SHIFT (9U)
#define RGX_CR_BIF_CTRL__XE_MEM__PAUSE_MMU_CPU_CLRMSK (0xFFFFFDFFU)
#define RGX_CR_BIF_CTRL__XE_MEM__PAUSE_MMU_CPU_EN (0x00000200U)
#define RGX_CR_BIF_CTRL__XE_MEM__PAUSE_MMU_BIF4_SHIFT (8U)
#define RGX_CR_BIF_CTRL__XE_MEM__PAUSE_MMU_BIF4_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_BIF_CTRL__XE_MEM__PAUSE_MMU_BIF4_EN (0x00000100U)
#define RGX_CR_BIF_CTRL_ENABLE_MMU_QUEUE_BYPASS_SHIFT (7U)
#define RGX_CR_BIF_CTRL_ENABLE_MMU_QUEUE_BYPASS_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_BIF_CTRL_ENABLE_MMU_QUEUE_BYPASS_EN (0x00000080U)
#define RGX_CR_BIF_CTRL_ENABLE_MMU_AUTO_PREFETCH_SHIFT (6U)
#define RGX_CR_BIF_CTRL_ENABLE_MMU_AUTO_PREFETCH_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_BIF_CTRL_ENABLE_MMU_AUTO_PREFETCH_EN (0x00000040U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF3_SHIFT (5U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF3_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF3_EN (0x00000020U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF2_SHIFT (4U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF2_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF2_EN (0x00000010U)
#define RGX_CR_BIF_CTRL_PAUSE_BIF1_SHIFT (3U)
#define RGX_CR_BIF_CTRL_PAUSE_BIF1_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_BIF_CTRL_PAUSE_BIF1_EN (0x00000008U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_PM_SHIFT (2U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_PM_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_PM_EN (0x00000004U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF1_SHIFT (1U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF1_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF1_EN (0x00000002U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF0_SHIFT (0U)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF0_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_BIF_CTRL_PAUSE_MMU_BIF0_EN (0x00000001U)

/*
    Register RGX_CR_BIF_FAULT_BANK0_MMU_STATUS
*/
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS (0x12B0U)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_MASKFULL \
	(IMG_UINT64_C(0x000000000000F775))
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_CAT_BASE_SHIFT (12U)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_CAT_BASE_CLRMSK (0xFFFF0FFFU)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_PAGE_SIZE_SHIFT (8U)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_PAGE_SIZE_CLRMSK (0xFFFFF8FFU)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_DATA_TYPE_SHIFT (5U)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_DATA_TYPE_CLRMSK (0xFFFFFF9FU)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_FAULT_RO_SHIFT (4U)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_FAULT_RO_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_FAULT_RO_EN (0x00000010U)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_FAULT_PM_META_RO_SHIFT (2U)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_FAULT_PM_META_RO_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_FAULT_PM_META_RO_EN (0x00000004U)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_FAULT_SHIFT (0U)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_FAULT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_BIF_FAULT_BANK0_MMU_STATUS_FAULT_EN (0x00000001U)

/*
    Register RGX_CR_BIF_FAULT_BANK0_REQ_STATUS
*/
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS (0x12B8U)
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS__XE_MEM__MASKFULL \
	(IMG_UINT64_C(0x001FFFFFFFFFFFF0))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_MASKFULL \
	(IMG_UINT64_C(0x0007FFFFFFFFFFF0))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS__XE_MEM__RNW_SHIFT (52U)
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS__XE_MEM__RNW_CLRMSK \
	(IMG_UINT64_C(0xFFEFFFFFFFFFFFFF))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS__XE_MEM__RNW_EN \
	(IMG_UINT64_C(0x0010000000000000))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_RNW_SHIFT (50U)
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_RNW_CLRMSK \
	(IMG_UINT64_C(0xFFFBFFFFFFFFFFFF))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_RNW_EN \
	(IMG_UINT64_C(0x0004000000000000))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS__XE_MEM__TAG_SB_SHIFT (46U)
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS__XE_MEM__TAG_SB_CLRMSK \
	(IMG_UINT64_C(0xFFF03FFFFFFFFFFF))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_TAG_SB_SHIFT (44U)
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_TAG_SB_CLRMSK \
	(IMG_UINT64_C(0xFFFC0FFFFFFFFFFF))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_TAG_ID_SHIFT (40U)
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_TAG_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFF0FFFFFFFFFF))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS__XE_MEM__TAG_ID_SHIFT (40U)
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS__XE_MEM__TAG_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFC0FFFFFFFFFF))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_ADDRESS_SHIFT (4U)
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF000000000F))
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_ADDRESS_ALIGNSHIFT (4U)
#define RGX_CR_BIF_FAULT_BANK0_REQ_STATUS_ADDRESS_ALIGNSIZE (16U)

/*
    Register RGX_CR_BIF_FAULT_BANK1_MMU_STATUS
*/
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS (0x12C0U)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_MASKFULL \
	(IMG_UINT64_C(0x000000000000F775))
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_CAT_BASE_SHIFT (12U)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_CAT_BASE_CLRMSK (0xFFFF0FFFU)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_PAGE_SIZE_SHIFT (8U)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_PAGE_SIZE_CLRMSK (0xFFFFF8FFU)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_DATA_TYPE_SHIFT (5U)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_DATA_TYPE_CLRMSK (0xFFFFFF9FU)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_FAULT_RO_SHIFT (4U)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_FAULT_RO_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_FAULT_RO_EN (0x00000010U)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_FAULT_PM_META_RO_SHIFT (2U)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_FAULT_PM_META_RO_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_FAULT_PM_META_RO_EN (0x00000004U)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_FAULT_SHIFT (0U)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_FAULT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_BIF_FAULT_BANK1_MMU_STATUS_FAULT_EN (0x00000001U)

/*
    Register RGX_CR_BIF_FAULT_BANK1_REQ_STATUS
*/
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS (0x12C8U)
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_MASKFULL \
	(IMG_UINT64_C(0x0007FFFFFFFFFFF0))
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_RNW_SHIFT (50U)
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_RNW_CLRMSK \
	(IMG_UINT64_C(0xFFFBFFFFFFFFFFFF))
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_RNW_EN \
	(IMG_UINT64_C(0x0004000000000000))
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_TAG_SB_SHIFT (44U)
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_TAG_SB_CLRMSK \
	(IMG_UINT64_C(0xFFFC0FFFFFFFFFFF))
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_TAG_ID_SHIFT (40U)
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_TAG_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFF0FFFFFFFFFF))
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_ADDRESS_SHIFT (4U)
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF000000000F))
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_ADDRESS_ALIGNSHIFT (4U)
#define RGX_CR_BIF_FAULT_BANK1_REQ_STATUS_ADDRESS_ALIGNSIZE (16U)

/*
    Register RGX_CR_BIF_MMU_STATUS
*/
#define RGX_CR_BIF_MMU_STATUS (0x12D0U)
#define RGX_CR_BIF_MMU_STATUS__XE_MEM__MASKFULL \
	(IMG_UINT64_C(0x000000001FFFFFF7))
#define RGX_CR_BIF_MMU_STATUS_MASKFULL (IMG_UINT64_C(0x000000001FFFFFF7))
#define RGX_CR_BIF_MMU_STATUS_PM_FAULT_SHIFT (28U)
#define RGX_CR_BIF_MMU_STATUS_PM_FAULT_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_BIF_MMU_STATUS_PM_FAULT_EN (0x10000000U)
#define RGX_CR_BIF_MMU_STATUS_PC_DATA_SHIFT (20U)
#define RGX_CR_BIF_MMU_STATUS_PC_DATA_CLRMSK (0xF00FFFFFU)
#define RGX_CR_BIF_MMU_STATUS_PD_DATA_SHIFT (12U)
#define RGX_CR_BIF_MMU_STATUS_PD_DATA_CLRMSK (0xFFF00FFFU)
#define RGX_CR_BIF_MMU_STATUS_PT_DATA_SHIFT (4U)
#define RGX_CR_BIF_MMU_STATUS_PT_DATA_CLRMSK (0xFFFFF00FU)
#define RGX_CR_BIF_MMU_STATUS_STALLED_SHIFT (2U)
#define RGX_CR_BIF_MMU_STATUS_STALLED_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_BIF_MMU_STATUS_STALLED_EN (0x00000004U)
#define RGX_CR_BIF_MMU_STATUS_PAUSED_SHIFT (1U)
#define RGX_CR_BIF_MMU_STATUS_PAUSED_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_BIF_MMU_STATUS_PAUSED_EN (0x00000002U)
#define RGX_CR_BIF_MMU_STATUS_BUSY_SHIFT (0U)
#define RGX_CR_BIF_MMU_STATUS_BUSY_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_BIF_MMU_STATUS_BUSY_EN (0x00000001U)

/*
    Register group: RGX_CR_BIF_TILING_CFG, with 8 repeats
*/
#define RGX_CR_BIF_TILING_CFG_REPEATCOUNT (8U)
/*
    Register RGX_CR_BIF_TILING_CFG0
*/
#define RGX_CR_BIF_TILING_CFG0 (0x12D8U)
#define RGX_CR_BIF_TILING_CFG0_MASKFULL (IMG_UINT64_C(0xFFFFFFFF0FFFFFFF))
#define RGX_CR_BIF_TILING_CFG0_XSTRIDE_SHIFT (61U)
#define RGX_CR_BIF_TILING_CFG0_XSTRIDE_CLRMSK (IMG_UINT64_C(0x1FFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG0_ENABLE_SHIFT (60U)
#define RGX_CR_BIF_TILING_CFG0_ENABLE_CLRMSK (IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG0_ENABLE_EN (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_BIF_TILING_CFG0_MAX_ADDRESS_SHIFT (32U)
#define RGX_CR_BIF_TILING_CFG0_MAX_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xF0000000FFFFFFFF))
#define RGX_CR_BIF_TILING_CFG0_MAX_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG0_MAX_ADDRESS_ALIGNSIZE (4096U)
#define RGX_CR_BIF_TILING_CFG0_MIN_ADDRESS_SHIFT (0U)
#define RGX_CR_BIF_TILING_CFG0_MIN_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF0000000))
#define RGX_CR_BIF_TILING_CFG0_MIN_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG0_MIN_ADDRESS_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_TILING_CFG1
*/
#define RGX_CR_BIF_TILING_CFG1 (0x12E0U)
#define RGX_CR_BIF_TILING_CFG1_MASKFULL (IMG_UINT64_C(0xFFFFFFFF0FFFFFFF))
#define RGX_CR_BIF_TILING_CFG1_XSTRIDE_SHIFT (61U)
#define RGX_CR_BIF_TILING_CFG1_XSTRIDE_CLRMSK (IMG_UINT64_C(0x1FFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG1_ENABLE_SHIFT (60U)
#define RGX_CR_BIF_TILING_CFG1_ENABLE_CLRMSK (IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG1_ENABLE_EN (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_BIF_TILING_CFG1_MAX_ADDRESS_SHIFT (32U)
#define RGX_CR_BIF_TILING_CFG1_MAX_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xF0000000FFFFFFFF))
#define RGX_CR_BIF_TILING_CFG1_MAX_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG1_MAX_ADDRESS_ALIGNSIZE (4096U)
#define RGX_CR_BIF_TILING_CFG1_MIN_ADDRESS_SHIFT (0U)
#define RGX_CR_BIF_TILING_CFG1_MIN_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF0000000))
#define RGX_CR_BIF_TILING_CFG1_MIN_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG1_MIN_ADDRESS_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_TILING_CFG2
*/
#define RGX_CR_BIF_TILING_CFG2 (0x12E8U)
#define RGX_CR_BIF_TILING_CFG2_MASKFULL (IMG_UINT64_C(0xFFFFFFFF0FFFFFFF))
#define RGX_CR_BIF_TILING_CFG2_XSTRIDE_SHIFT (61U)
#define RGX_CR_BIF_TILING_CFG2_XSTRIDE_CLRMSK (IMG_UINT64_C(0x1FFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG2_ENABLE_SHIFT (60U)
#define RGX_CR_BIF_TILING_CFG2_ENABLE_CLRMSK (IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG2_ENABLE_EN (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_BIF_TILING_CFG2_MAX_ADDRESS_SHIFT (32U)
#define RGX_CR_BIF_TILING_CFG2_MAX_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xF0000000FFFFFFFF))
#define RGX_CR_BIF_TILING_CFG2_MAX_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG2_MAX_ADDRESS_ALIGNSIZE (4096U)
#define RGX_CR_BIF_TILING_CFG2_MIN_ADDRESS_SHIFT (0U)
#define RGX_CR_BIF_TILING_CFG2_MIN_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF0000000))
#define RGX_CR_BIF_TILING_CFG2_MIN_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG2_MIN_ADDRESS_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_TILING_CFG3
*/
#define RGX_CR_BIF_TILING_CFG3 (0x12F0U)
#define RGX_CR_BIF_TILING_CFG3_MASKFULL (IMG_UINT64_C(0xFFFFFFFF0FFFFFFF))
#define RGX_CR_BIF_TILING_CFG3_XSTRIDE_SHIFT (61U)
#define RGX_CR_BIF_TILING_CFG3_XSTRIDE_CLRMSK (IMG_UINT64_C(0x1FFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG3_ENABLE_SHIFT (60U)
#define RGX_CR_BIF_TILING_CFG3_ENABLE_CLRMSK (IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG3_ENABLE_EN (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_BIF_TILING_CFG3_MAX_ADDRESS_SHIFT (32U)
#define RGX_CR_BIF_TILING_CFG3_MAX_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xF0000000FFFFFFFF))
#define RGX_CR_BIF_TILING_CFG3_MAX_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG3_MAX_ADDRESS_ALIGNSIZE (4096U)
#define RGX_CR_BIF_TILING_CFG3_MIN_ADDRESS_SHIFT (0U)
#define RGX_CR_BIF_TILING_CFG3_MIN_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF0000000))
#define RGX_CR_BIF_TILING_CFG3_MIN_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG3_MIN_ADDRESS_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_TILING_CFG4
*/
#define RGX_CR_BIF_TILING_CFG4 (0x12F8U)
#define RGX_CR_BIF_TILING_CFG4_MASKFULL (IMG_UINT64_C(0xFFFFFFFF0FFFFFFF))
#define RGX_CR_BIF_TILING_CFG4_XSTRIDE_SHIFT (61U)
#define RGX_CR_BIF_TILING_CFG4_XSTRIDE_CLRMSK (IMG_UINT64_C(0x1FFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG4_ENABLE_SHIFT (60U)
#define RGX_CR_BIF_TILING_CFG4_ENABLE_CLRMSK (IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG4_ENABLE_EN (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_BIF_TILING_CFG4_MAX_ADDRESS_SHIFT (32U)
#define RGX_CR_BIF_TILING_CFG4_MAX_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xF0000000FFFFFFFF))
#define RGX_CR_BIF_TILING_CFG4_MAX_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG4_MAX_ADDRESS_ALIGNSIZE (4096U)
#define RGX_CR_BIF_TILING_CFG4_MIN_ADDRESS_SHIFT (0U)
#define RGX_CR_BIF_TILING_CFG4_MIN_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF0000000))
#define RGX_CR_BIF_TILING_CFG4_MIN_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG4_MIN_ADDRESS_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_TILING_CFG5
*/
#define RGX_CR_BIF_TILING_CFG5 (0x1300U)
#define RGX_CR_BIF_TILING_CFG5_MASKFULL (IMG_UINT64_C(0xFFFFFFFF0FFFFFFF))
#define RGX_CR_BIF_TILING_CFG5_XSTRIDE_SHIFT (61U)
#define RGX_CR_BIF_TILING_CFG5_XSTRIDE_CLRMSK (IMG_UINT64_C(0x1FFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG5_ENABLE_SHIFT (60U)
#define RGX_CR_BIF_TILING_CFG5_ENABLE_CLRMSK (IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG5_ENABLE_EN (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_BIF_TILING_CFG5_MAX_ADDRESS_SHIFT (32U)
#define RGX_CR_BIF_TILING_CFG5_MAX_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xF0000000FFFFFFFF))
#define RGX_CR_BIF_TILING_CFG5_MAX_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG5_MAX_ADDRESS_ALIGNSIZE (4096U)
#define RGX_CR_BIF_TILING_CFG5_MIN_ADDRESS_SHIFT (0U)
#define RGX_CR_BIF_TILING_CFG5_MIN_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF0000000))
#define RGX_CR_BIF_TILING_CFG5_MIN_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG5_MIN_ADDRESS_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_TILING_CFG6
*/
#define RGX_CR_BIF_TILING_CFG6 (0x1308U)
#define RGX_CR_BIF_TILING_CFG6_MASKFULL (IMG_UINT64_C(0xFFFFFFFF0FFFFFFF))
#define RGX_CR_BIF_TILING_CFG6_XSTRIDE_SHIFT (61U)
#define RGX_CR_BIF_TILING_CFG6_XSTRIDE_CLRMSK (IMG_UINT64_C(0x1FFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG6_ENABLE_SHIFT (60U)
#define RGX_CR_BIF_TILING_CFG6_ENABLE_CLRMSK (IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG6_ENABLE_EN (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_BIF_TILING_CFG6_MAX_ADDRESS_SHIFT (32U)
#define RGX_CR_BIF_TILING_CFG6_MAX_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xF0000000FFFFFFFF))
#define RGX_CR_BIF_TILING_CFG6_MAX_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG6_MAX_ADDRESS_ALIGNSIZE (4096U)
#define RGX_CR_BIF_TILING_CFG6_MIN_ADDRESS_SHIFT (0U)
#define RGX_CR_BIF_TILING_CFG6_MIN_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF0000000))
#define RGX_CR_BIF_TILING_CFG6_MIN_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG6_MIN_ADDRESS_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_TILING_CFG7
*/
#define RGX_CR_BIF_TILING_CFG7 (0x1310U)
#define RGX_CR_BIF_TILING_CFG7_MASKFULL (IMG_UINT64_C(0xFFFFFFFF0FFFFFFF))
#define RGX_CR_BIF_TILING_CFG7_XSTRIDE_SHIFT (61U)
#define RGX_CR_BIF_TILING_CFG7_XSTRIDE_CLRMSK (IMG_UINT64_C(0x1FFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG7_ENABLE_SHIFT (60U)
#define RGX_CR_BIF_TILING_CFG7_ENABLE_CLRMSK (IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_BIF_TILING_CFG7_ENABLE_EN (IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_BIF_TILING_CFG7_MAX_ADDRESS_SHIFT (32U)
#define RGX_CR_BIF_TILING_CFG7_MAX_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xF0000000FFFFFFFF))
#define RGX_CR_BIF_TILING_CFG7_MAX_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG7_MAX_ADDRESS_ALIGNSIZE (4096U)
#define RGX_CR_BIF_TILING_CFG7_MIN_ADDRESS_SHIFT (0U)
#define RGX_CR_BIF_TILING_CFG7_MIN_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF0000000))
#define RGX_CR_BIF_TILING_CFG7_MIN_ADDRESS_ALIGNSHIFT (12U)
#define RGX_CR_BIF_TILING_CFG7_MIN_ADDRESS_ALIGNSIZE (4096U)

/*
    Register RGX_CR_BIF_READS_EXT_STATUS
*/
#define RGX_CR_BIF_READS_EXT_STATUS (0x1320U)
#define RGX_CR_BIF_READS_EXT_STATUS_MASKFULL (IMG_UINT64_C(0x000000000FFFFFFF))
#define RGX_CR_BIF_READS_EXT_STATUS_MMU_SHIFT (16U)
#define RGX_CR_BIF_READS_EXT_STATUS_MMU_CLRMSK (0xF000FFFFU)
#define RGX_CR_BIF_READS_EXT_STATUS_BANK1_SHIFT (0U)
#define RGX_CR_BIF_READS_EXT_STATUS_BANK1_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_BIF_READS_INT_STATUS
*/
#define RGX_CR_BIF_READS_INT_STATUS (0x1328U)
#define RGX_CR_BIF_READS_INT_STATUS_MASKFULL (IMG_UINT64_C(0x0000000007FFFFFF))
#define RGX_CR_BIF_READS_INT_STATUS_MMU_SHIFT (16U)
#define RGX_CR_BIF_READS_INT_STATUS_MMU_CLRMSK (0xF800FFFFU)
#define RGX_CR_BIF_READS_INT_STATUS_BANK1_SHIFT (0U)
#define RGX_CR_BIF_READS_INT_STATUS_BANK1_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_BIFPM_READS_INT_STATUS
*/
#define RGX_CR_BIFPM_READS_INT_STATUS (0x1330U)
#define RGX_CR_BIFPM_READS_INT_STATUS_MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_BIFPM_READS_INT_STATUS_BANK0_SHIFT (0U)
#define RGX_CR_BIFPM_READS_INT_STATUS_BANK0_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_BIFPM_READS_EXT_STATUS
*/
#define RGX_CR_BIFPM_READS_EXT_STATUS (0x1338U)
#define RGX_CR_BIFPM_READS_EXT_STATUS_MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_BIFPM_READS_EXT_STATUS_BANK0_SHIFT (0U)
#define RGX_CR_BIFPM_READS_EXT_STATUS_BANK0_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_BIFPM_STATUS_MMU
*/
#define RGX_CR_BIFPM_STATUS_MMU (0x1350U)
#define RGX_CR_BIFPM_STATUS_MMU_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_BIFPM_STATUS_MMU_REQUESTS_SHIFT (0U)
#define RGX_CR_BIFPM_STATUS_MMU_REQUESTS_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_BIF_STATUS_MMU
*/
#define RGX_CR_BIF_STATUS_MMU (0x1358U)
#define RGX_CR_BIF_STATUS_MMU_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_BIF_STATUS_MMU_REQUESTS_SHIFT (0U)
#define RGX_CR_BIF_STATUS_MMU_REQUESTS_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_BIF_FAULT_READ
*/
#define RGX_CR_BIF_FAULT_READ (0x13E0U)
#define RGX_CR_BIF_FAULT_READ_MASKFULL (IMG_UINT64_C(0x000000FFFFFFFFF0))
#define RGX_CR_BIF_FAULT_READ_ADDRESS_SHIFT (4U)
#define RGX_CR_BIF_FAULT_READ_ADDRESS_CLRMSK (IMG_UINT64_C(0xFFFFFF000000000F))
#define RGX_CR_BIF_FAULT_READ_ADDRESS_ALIGNSHIFT (4U)
#define RGX_CR_BIF_FAULT_READ_ADDRESS_ALIGNSIZE (16U)

/*
    Register RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS
*/
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS (0x1430U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_MASKFULL \
	(IMG_UINT64_C(0x000000000000F775))
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_CAT_BASE_SHIFT (12U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_CAT_BASE_CLRMSK (0xFFFF0FFFU)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_PAGE_SIZE_SHIFT (8U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_PAGE_SIZE_CLRMSK (0xFFFFF8FFU)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_DATA_TYPE_SHIFT (5U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_DATA_TYPE_CLRMSK (0xFFFFFF9FU)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_FAULT_RO_SHIFT (4U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_FAULT_RO_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_FAULT_RO_EN (0x00000010U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_FAULT_PM_META_RO_SHIFT (2U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_FAULT_PM_META_RO_CLRMSK \
	(0xFFFFFFFBU)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_FAULT_PM_META_RO_EN \
	(0x00000004U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_FAULT_SHIFT (0U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_FAULT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_MMU_STATUS_FAULT_EN (0x00000001U)

/*
    Register RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS
*/
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS (0x1438U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_MASKFULL \
	(IMG_UINT64_C(0x0007FFFFFFFFFFF0))
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_RNW_SHIFT (50U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_RNW_CLRMSK \
	(IMG_UINT64_C(0xFFFBFFFFFFFFFFFF))
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_RNW_EN \
	(IMG_UINT64_C(0x0004000000000000))
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_TAG_SB_SHIFT (44U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_TAG_SB_CLRMSK \
	(IMG_UINT64_C(0xFFFC0FFFFFFFFFFF))
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_TAG_ID_SHIFT (40U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_TAG_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFF0FFFFFFFFFF))
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_ADDRESS_SHIFT (4U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF000000000F))
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_ADDRESS_ALIGNSHIFT (4U)
#define RGX_CR_TEXAS_BIF_FAULT_BANK0_REQ_STATUS_ADDRESS_ALIGNSIZE (16U)

/*
    Register RGX_CR_TFBC_COMPRESSION_CONTROL
*/
#define RGX_CR_TFBC_COMPRESSION_CONTROL (0x14A0U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_MASKFULL \
	(IMG_UINT64_C(0x00000000000001FF))
#define RGX_CR_TFBC_COMPRESSION_CONTROL_LOSSY_MIN_CHANNEL_OVERRIDE_SHIFT (8U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_LOSSY_MIN_CHANNEL_OVERRIDE_CLRMSK \
	(0xFFFFFEFFU)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_LOSSY_MIN_CHANNEL_OVERRIDE_EN \
	(0x00000100U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_YUV10_OVERRIDE_SHIFT (7U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_YUV10_OVERRIDE_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_YUV10_OVERRIDE_EN (0x00000080U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_QUALITY_SHIFT_SHIFT (4U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_QUALITY_SHIFT_CLRMSK (0xFFFFFF8FU)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_QUALITY_ENABLE_SHIFT (3U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_QUALITY_ENABLE_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_QUALITY_ENABLE_EN (0x00000008U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_SCHEME_SHIFT (1U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_SCHEME_CLRMSK (0xFFFFFFF9U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_SCHEME_DEFAULT (0x00000000U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_SCHEME_TFBC_DELTA_STANDARD_AND_CORRELATION \
	(0x00000002U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_SCHEME_TFBC_DELTA_STANDARD (0x00000004U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_SCHEME_RESERVED (0x00000006U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_GROUP_CONTROL_SHIFT (0U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_GROUP_CONTROL_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_GROUP_CONTROL_GROUP_0 (0x00000000U)
#define RGX_CR_TFBC_COMPRESSION_CONTROL_GROUP_CONTROL_GROUP_1 (0x00000001U)

/*
    Register RGX_CR_MCU_FENCE
*/
#define RGX_CR_MCU_FENCE (0x1740U)
#define RGX_CR_MCU_FENCE_MASKFULL (IMG_UINT64_C(0x000007FFFFFFFFE0))
#define RGX_CR_MCU_FENCE_DM_SHIFT (40U)
#define RGX_CR_MCU_FENCE_DM_CLRMSK (IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_MCU_FENCE_DM_VERTEX (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_MCU_FENCE_DM_PIXEL (IMG_UINT64_C(0x0000010000000000))
#define RGX_CR_MCU_FENCE_DM_COMPUTE (IMG_UINT64_C(0x0000020000000000))
#define RGX_CR_MCU_FENCE_DM_RAY_VERTEX (IMG_UINT64_C(0x0000030000000000))
#define RGX_CR_MCU_FENCE_DM_RAY (IMG_UINT64_C(0x0000040000000000))
#define RGX_CR_MCU_FENCE_DM_FASTRENDER (IMG_UINT64_C(0x0000050000000000))
#define RGX_CR_MCU_FENCE_ADDR_SHIFT (5U)
#define RGX_CR_MCU_FENCE_ADDR_CLRMSK (IMG_UINT64_C(0xFFFFFF000000001F))
#define RGX_CR_MCU_FENCE_ADDR_ALIGNSHIFT (5U)
#define RGX_CR_MCU_FENCE_ADDR_ALIGNSIZE (32U)

/*
    Register group: RGX_CR_SCRATCH, with 16 repeats
*/
#define RGX_CR_SCRATCH_REPEATCOUNT (16U)
/*
    Register RGX_CR_SCRATCH0
*/
#define RGX_CR_SCRATCH0 (0x1A00U)
#define RGX_CR_SCRATCH0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH0_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH1
*/
#define RGX_CR_SCRATCH1 (0x1A08U)
#define RGX_CR_SCRATCH1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH1_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH2
*/
#define RGX_CR_SCRATCH2 (0x1A10U)
#define RGX_CR_SCRATCH2_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH2_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH2_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH3
*/
#define RGX_CR_SCRATCH3 (0x1A18U)
#define RGX_CR_SCRATCH3_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH3_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH3_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH4
*/
#define RGX_CR_SCRATCH4 (0x1A20U)
#define RGX_CR_SCRATCH4_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH4_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH4_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH5
*/
#define RGX_CR_SCRATCH5 (0x1A28U)
#define RGX_CR_SCRATCH5_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH5_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH5_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH6
*/
#define RGX_CR_SCRATCH6 (0x1A30U)
#define RGX_CR_SCRATCH6_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH6_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH6_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH7
*/
#define RGX_CR_SCRATCH7 (0x1A38U)
#define RGX_CR_SCRATCH7_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH7_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH7_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH8
*/
#define RGX_CR_SCRATCH8 (0x1A40U)
#define RGX_CR_SCRATCH8_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH8_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH8_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH9
*/
#define RGX_CR_SCRATCH9 (0x1A48U)
#define RGX_CR_SCRATCH9_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH9_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH9_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH10
*/
#define RGX_CR_SCRATCH10 (0x1A50U)
#define RGX_CR_SCRATCH10_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH10_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH10_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH11
*/
#define RGX_CR_SCRATCH11 (0x1A58U)
#define RGX_CR_SCRATCH11_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH11_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH11_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH12
*/
#define RGX_CR_SCRATCH12 (0x1A60U)
#define RGX_CR_SCRATCH12_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH12_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH12_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH13
*/
#define RGX_CR_SCRATCH13 (0x1A68U)
#define RGX_CR_SCRATCH13_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH13_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH13_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH14
*/
#define RGX_CR_SCRATCH14 (0x1A70U)
#define RGX_CR_SCRATCH14_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH14_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH14_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SCRATCH15
*/
#define RGX_CR_SCRATCH15 (0x1A78U)
#define RGX_CR_SCRATCH15_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SCRATCH15_DATA_SHIFT (0U)
#define RGX_CR_SCRATCH15_DATA_CLRMSK (0x00000000U)

/*
    Register group: RGX_CR_OS0_SCRATCH, with 2 repeats
*/
#define RGX_CR_OS0_SCRATCH_REPEATCOUNT (2U)
/*
    Register RGX_CR_OS0_SCRATCH0
*/
#define RGX_CR_OS0_SCRATCH0 (0x1A80U)
#define RGX_CR_OS0_SCRATCH0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS0_SCRATCH0_DATA_SHIFT (0U)
#define RGX_CR_OS0_SCRATCH0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS0_SCRATCH1
*/
#define RGX_CR_OS0_SCRATCH1 (0x1A88U)
#define RGX_CR_OS0_SCRATCH1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS0_SCRATCH1_DATA_SHIFT (0U)
#define RGX_CR_OS0_SCRATCH1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS0_SCRATCH2
*/
#define RGX_CR_OS0_SCRATCH2 (0x1A90U)
#define RGX_CR_OS0_SCRATCH2_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS0_SCRATCH2_DATA_SHIFT (0U)
#define RGX_CR_OS0_SCRATCH2_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_OS0_SCRATCH3
*/
#define RGX_CR_OS0_SCRATCH3 (0x1A98U)
#define RGX_CR_OS0_SCRATCH3_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS0_SCRATCH3_DATA_SHIFT (0U)
#define RGX_CR_OS0_SCRATCH3_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register group: RGX_CR_OS1_SCRATCH, with 2 repeats
*/
#define RGX_CR_OS1_SCRATCH_REPEATCOUNT (2U)
/*
    Register RGX_CR_OS1_SCRATCH0
*/
#define RGX_CR_OS1_SCRATCH0 (0x11A80U)
#define RGX_CR_OS1_SCRATCH0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS1_SCRATCH0_DATA_SHIFT (0U)
#define RGX_CR_OS1_SCRATCH0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS1_SCRATCH1
*/
#define RGX_CR_OS1_SCRATCH1 (0x11A88U)
#define RGX_CR_OS1_SCRATCH1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS1_SCRATCH1_DATA_SHIFT (0U)
#define RGX_CR_OS1_SCRATCH1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS1_SCRATCH2
*/
#define RGX_CR_OS1_SCRATCH2 (0x11A90U)
#define RGX_CR_OS1_SCRATCH2_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS1_SCRATCH2_DATA_SHIFT (0U)
#define RGX_CR_OS1_SCRATCH2_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_OS1_SCRATCH3
*/
#define RGX_CR_OS1_SCRATCH3 (0x11A98U)
#define RGX_CR_OS1_SCRATCH3_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS1_SCRATCH3_DATA_SHIFT (0U)
#define RGX_CR_OS1_SCRATCH3_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register group: RGX_CR_OS2_SCRATCH, with 2 repeats
*/
#define RGX_CR_OS2_SCRATCH_REPEATCOUNT (2U)
/*
    Register RGX_CR_OS2_SCRATCH0
*/
#define RGX_CR_OS2_SCRATCH0 (0x21A80U)
#define RGX_CR_OS2_SCRATCH0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS2_SCRATCH0_DATA_SHIFT (0U)
#define RGX_CR_OS2_SCRATCH0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS2_SCRATCH1
*/
#define RGX_CR_OS2_SCRATCH1 (0x21A88U)
#define RGX_CR_OS2_SCRATCH1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS2_SCRATCH1_DATA_SHIFT (0U)
#define RGX_CR_OS2_SCRATCH1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS2_SCRATCH2
*/
#define RGX_CR_OS2_SCRATCH2 (0x21A90U)
#define RGX_CR_OS2_SCRATCH2_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS2_SCRATCH2_DATA_SHIFT (0U)
#define RGX_CR_OS2_SCRATCH2_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_OS2_SCRATCH3
*/
#define RGX_CR_OS2_SCRATCH3 (0x21A98U)
#define RGX_CR_OS2_SCRATCH3_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS2_SCRATCH3_DATA_SHIFT (0U)
#define RGX_CR_OS2_SCRATCH3_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register group: RGX_CR_OS3_SCRATCH, with 2 repeats
*/
#define RGX_CR_OS3_SCRATCH_REPEATCOUNT (2U)
/*
    Register RGX_CR_OS3_SCRATCH0
*/
#define RGX_CR_OS3_SCRATCH0 (0x31A80U)
#define RGX_CR_OS3_SCRATCH0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS3_SCRATCH0_DATA_SHIFT (0U)
#define RGX_CR_OS3_SCRATCH0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS3_SCRATCH1
*/
#define RGX_CR_OS3_SCRATCH1 (0x31A88U)
#define RGX_CR_OS3_SCRATCH1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS3_SCRATCH1_DATA_SHIFT (0U)
#define RGX_CR_OS3_SCRATCH1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS3_SCRATCH2
*/
#define RGX_CR_OS3_SCRATCH2 (0x31A90U)
#define RGX_CR_OS3_SCRATCH2_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS3_SCRATCH2_DATA_SHIFT (0U)
#define RGX_CR_OS3_SCRATCH2_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_OS3_SCRATCH3
*/
#define RGX_CR_OS3_SCRATCH3 (0x31A98U)
#define RGX_CR_OS3_SCRATCH3_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS3_SCRATCH3_DATA_SHIFT (0U)
#define RGX_CR_OS3_SCRATCH3_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register group: RGX_CR_OS4_SCRATCH, with 2 repeats
*/
#define RGX_CR_OS4_SCRATCH_REPEATCOUNT (2U)
/*
    Register RGX_CR_OS4_SCRATCH0
*/
#define RGX_CR_OS4_SCRATCH0 (0x41A80U)
#define RGX_CR_OS4_SCRATCH0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS4_SCRATCH0_DATA_SHIFT (0U)
#define RGX_CR_OS4_SCRATCH0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS4_SCRATCH1
*/
#define RGX_CR_OS4_SCRATCH1 (0x41A88U)
#define RGX_CR_OS4_SCRATCH1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS4_SCRATCH1_DATA_SHIFT (0U)
#define RGX_CR_OS4_SCRATCH1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS4_SCRATCH2
*/
#define RGX_CR_OS4_SCRATCH2 (0x41A90U)
#define RGX_CR_OS4_SCRATCH2_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS4_SCRATCH2_DATA_SHIFT (0U)
#define RGX_CR_OS4_SCRATCH2_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_OS4_SCRATCH3
*/
#define RGX_CR_OS4_SCRATCH3 (0x41A98U)
#define RGX_CR_OS4_SCRATCH3_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS4_SCRATCH3_DATA_SHIFT (0U)
#define RGX_CR_OS4_SCRATCH3_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register group: RGX_CR_OS5_SCRATCH, with 2 repeats
*/
#define RGX_CR_OS5_SCRATCH_REPEATCOUNT (2U)
/*
    Register RGX_CR_OS5_SCRATCH0
*/
#define RGX_CR_OS5_SCRATCH0 (0x51A80U)
#define RGX_CR_OS5_SCRATCH0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS5_SCRATCH0_DATA_SHIFT (0U)
#define RGX_CR_OS5_SCRATCH0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS5_SCRATCH1
*/
#define RGX_CR_OS5_SCRATCH1 (0x51A88U)
#define RGX_CR_OS5_SCRATCH1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS5_SCRATCH1_DATA_SHIFT (0U)
#define RGX_CR_OS5_SCRATCH1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS5_SCRATCH2
*/
#define RGX_CR_OS5_SCRATCH2 (0x51A90U)
#define RGX_CR_OS5_SCRATCH2_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS5_SCRATCH2_DATA_SHIFT (0U)
#define RGX_CR_OS5_SCRATCH2_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_OS5_SCRATCH3
*/
#define RGX_CR_OS5_SCRATCH3 (0x51A98U)
#define RGX_CR_OS5_SCRATCH3_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS5_SCRATCH3_DATA_SHIFT (0U)
#define RGX_CR_OS5_SCRATCH3_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register group: RGX_CR_OS6_SCRATCH, with 2 repeats
*/
#define RGX_CR_OS6_SCRATCH_REPEATCOUNT (2U)
/*
    Register RGX_CR_OS6_SCRATCH0
*/
#define RGX_CR_OS6_SCRATCH0 (0x61A80U)
#define RGX_CR_OS6_SCRATCH0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS6_SCRATCH0_DATA_SHIFT (0U)
#define RGX_CR_OS6_SCRATCH0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS6_SCRATCH1
*/
#define RGX_CR_OS6_SCRATCH1 (0x61A88U)
#define RGX_CR_OS6_SCRATCH1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS6_SCRATCH1_DATA_SHIFT (0U)
#define RGX_CR_OS6_SCRATCH1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS6_SCRATCH2
*/
#define RGX_CR_OS6_SCRATCH2 (0x61A90U)
#define RGX_CR_OS6_SCRATCH2_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS6_SCRATCH2_DATA_SHIFT (0U)
#define RGX_CR_OS6_SCRATCH2_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_OS6_SCRATCH3
*/
#define RGX_CR_OS6_SCRATCH3 (0x61A98U)
#define RGX_CR_OS6_SCRATCH3_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS6_SCRATCH3_DATA_SHIFT (0U)
#define RGX_CR_OS6_SCRATCH3_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register group: RGX_CR_OS7_SCRATCH, with 2 repeats
*/
#define RGX_CR_OS7_SCRATCH_REPEATCOUNT (2U)
/*
    Register RGX_CR_OS7_SCRATCH0
*/
#define RGX_CR_OS7_SCRATCH0 (0x71A80U)
#define RGX_CR_OS7_SCRATCH0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS7_SCRATCH0_DATA_SHIFT (0U)
#define RGX_CR_OS7_SCRATCH0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS7_SCRATCH1
*/
#define RGX_CR_OS7_SCRATCH1 (0x71A88U)
#define RGX_CR_OS7_SCRATCH1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_OS7_SCRATCH1_DATA_SHIFT (0U)
#define RGX_CR_OS7_SCRATCH1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OS7_SCRATCH2
*/
#define RGX_CR_OS7_SCRATCH2 (0x71A90U)
#define RGX_CR_OS7_SCRATCH2_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS7_SCRATCH2_DATA_SHIFT (0U)
#define RGX_CR_OS7_SCRATCH2_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_OS7_SCRATCH3
*/
#define RGX_CR_OS7_SCRATCH3 (0x71A98U)
#define RGX_CR_OS7_SCRATCH3_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_OS7_SCRATCH3_DATA_SHIFT (0U)
#define RGX_CR_OS7_SCRATCH3_DATA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_SPFILTER_SIGNAL_DESCR
*/
#define RGX_CR_SPFILTER_SIGNAL_DESCR (0x2700U)
#define RGX_CR_SPFILTER_SIGNAL_DESCR_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_SPFILTER_SIGNAL_DESCR_SIZE_SHIFT (0U)
#define RGX_CR_SPFILTER_SIGNAL_DESCR_SIZE_CLRMSK (0xFFFF0000U)
#define RGX_CR_SPFILTER_SIGNAL_DESCR_SIZE_ALIGNSHIFT (4U)
#define RGX_CR_SPFILTER_SIGNAL_DESCR_SIZE_ALIGNSIZE (16U)

/*
    Register RGX_CR_SPFILTER_SIGNAL_DESCR_MIN
*/
#define RGX_CR_SPFILTER_SIGNAL_DESCR_MIN (0x2708U)
#define RGX_CR_SPFILTER_SIGNAL_DESCR_MIN_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFF0))
#define RGX_CR_SPFILTER_SIGNAL_DESCR_MIN_ADDR_SHIFT (4U)
#define RGX_CR_SPFILTER_SIGNAL_DESCR_MIN_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF000000000F))
#define RGX_CR_SPFILTER_SIGNAL_DESCR_MIN_ADDR_ALIGNSHIFT (4U)
#define RGX_CR_SPFILTER_SIGNAL_DESCR_MIN_ADDR_ALIGNSIZE (16U)

/*
    Register group: RGX_CR_FWCORE_ADDR_REMAP_CONFIG, with 16 repeats
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG_REPEATCOUNT (16U)
/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG0
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0 (0x3000U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG0_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG1
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1 (0x3008U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG1_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG2
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2 (0x3010U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG2_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG3
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3 (0x3018U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG3_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG4
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4 (0x3020U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG4_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG5
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5 (0x3028U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG5_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG6
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6 (0x3030U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG6_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG7
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7 (0x3038U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG7_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG8
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8 (0x3040U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG8_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG9
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9 (0x3048U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG9_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG10
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10 (0x3050U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG10_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG11
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11 (0x3058U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG11_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG12
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12 (0x3060U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG12_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG13
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13 (0x3068U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG13_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG14
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14 (0x3070U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG14_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_ADDR_REMAP_CONFIG15
*/
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15 (0x3078U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_MASKFULL \
	(IMG_UINT64_C(0x7FFFF7FFFFFFF000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_TRUSTED_SHIFT (62U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_TRUSTED_CLRMSK \
	(IMG_UINT64_C(0xBFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_TRUSTED_EN \
	(IMG_UINT64_C(0x4000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_LOAD_STORE_EN_SHIFT (61U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_LOAD_STORE_EN_CLRMSK \
	(IMG_UINT64_C(0xDFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_LOAD_STORE_EN_EN \
	(IMG_UINT64_C(0x2000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_FETCH_EN_SHIFT (60U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_FETCH_EN_CLRMSK \
	(IMG_UINT64_C(0xEFFFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_FETCH_EN_EN \
	(IMG_UINT64_C(0x1000000000000000))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_SIZE_SHIFT (44U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_SIZE_CLRMSK \
	(IMG_UINT64_C(0xF0000FFFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_CBASE_SHIFT (40U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_CBASE_CLRMSK \
	(IMG_UINT64_C(0xFFFFF8FFFFFFFFFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_DEVVADDR_SHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_DEVVADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_DEVVADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_ADDR_REMAP_CONFIG15_DEVVADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_BOOT
*/
#define RGX_CR_FWCORE_BOOT (0x3090U)
#define RGX_CR_FWCORE_BOOT_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_FWCORE_BOOT_ENABLE_SHIFT (0U)
#define RGX_CR_FWCORE_BOOT_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_BOOT_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_FWCORE_RESET_ADDR
*/
#define RGX_CR_FWCORE_RESET_ADDR (0x3098U)
#define RGX_CR_FWCORE_RESET_ADDR_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFE))
#define RGX_CR_FWCORE_RESET_ADDR_ADDR_SHIFT (1U)
#define RGX_CR_FWCORE_RESET_ADDR_ADDR_CLRMSK (0x00000001U)
#define RGX_CR_FWCORE_RESET_ADDR_ADDR_ALIGNSHIFT (1U)
#define RGX_CR_FWCORE_RESET_ADDR_ADDR_ALIGNSIZE (2U)

/*
    Register RGX_CR_FWCORE_WRAPPER_NMI_ADDR
*/
#define RGX_CR_FWCORE_WRAPPER_NMI_ADDR (0x30A0U)
#define RGX_CR_FWCORE_WRAPPER_NMI_ADDR_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFE))
#define RGX_CR_FWCORE_WRAPPER_NMI_ADDR_ADDR_SHIFT (1U)
#define RGX_CR_FWCORE_WRAPPER_NMI_ADDR_ADDR_CLRMSK (0x00000001U)
#define RGX_CR_FWCORE_WRAPPER_NMI_ADDR_ADDR_ALIGNSHIFT (1U)
#define RGX_CR_FWCORE_WRAPPER_NMI_ADDR_ADDR_ALIGNSIZE (2U)

/*
    Register RGX_CR_FWCORE_WRAPPER_NMI_EVENT
*/
#define RGX_CR_FWCORE_WRAPPER_NMI_EVENT (0x30A8U)
#define RGX_CR_FWCORE_WRAPPER_NMI_EVENT_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_FWCORE_WRAPPER_NMI_EVENT_TRIGGER_EN_SHIFT (0U)
#define RGX_CR_FWCORE_WRAPPER_NMI_EVENT_TRIGGER_EN_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_WRAPPER_NMI_EVENT_TRIGGER_EN_EN (0x00000001U)

/*
    Register RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS
*/
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS (0x30B0U)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_MASKFULL \
	(IMG_UINT64_C(0x000000000000F771))
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_CAT_BASE_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_CAT_BASE_CLRMSK (0xFFFF0FFFU)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_PAGE_SIZE_SHIFT (8U)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_PAGE_SIZE_CLRMSK (0xFFFFF8FFU)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_DATA_TYPE_SHIFT (5U)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_DATA_TYPE_CLRMSK (0xFFFFFF9FU)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_FAULT_RO_SHIFT (4U)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_FAULT_RO_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_FAULT_RO_EN (0x00000010U)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_FAULT_SHIFT (0U)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_FAULT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_MEM_FAULT_MMU_STATUS_FAULT_EN (0x00000001U)

/*
    Register RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS
*/
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS (0x30B8U)
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_MASKFULL \
	(IMG_UINT64_C(0x001FFFFFFFFFFFF0))
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_RNW_SHIFT (52U)
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_RNW_CLRMSK \
	(IMG_UINT64_C(0xFFEFFFFFFFFFFFFF))
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_RNW_EN \
	(IMG_UINT64_C(0x0010000000000000))
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_TAG_SB_SHIFT (46U)
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_TAG_SB_CLRMSK \
	(IMG_UINT64_C(0xFFF03FFFFFFFFFFF))
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_TAG_ID_SHIFT (40U)
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_TAG_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFC0FFFFFFFFFF))
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_ADDRESS_SHIFT (4U)
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF000000000F))
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_ADDRESS_ALIGNSHIFT (4U)
#define RGX_CR_FWCORE_MEM_FAULT_REQ_STATUS_ADDRESS_ALIGNSIZE (16U)

/*
    Register RGX_CR_FWCORE_MEM_CTRL_INVAL
*/
#define RGX_CR_FWCORE_MEM_CTRL_INVAL (0x30C0U)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_MASKFULL (IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_TLB_SHIFT (3U)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_TLB_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_TLB_EN (0x00000008U)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_PC_SHIFT (2U)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_PC_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_PC_EN (0x00000004U)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_PD_SHIFT (1U)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_PD_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_PD_EN (0x00000002U)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_PT_SHIFT (0U)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_PT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_MEM_CTRL_INVAL_PT_EN (0x00000001U)

/*
    Register RGX_CR_FWCORE_MEM_MMU_STATUS
*/
#define RGX_CR_FWCORE_MEM_MMU_STATUS (0x30C8U)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_MASKFULL (IMG_UINT64_C(0x000000000FFFFFF7))
#define RGX_CR_FWCORE_MEM_MMU_STATUS_PC_DATA_SHIFT (20U)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_PC_DATA_CLRMSK (0xF00FFFFFU)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_PD_DATA_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_PD_DATA_CLRMSK (0xFFF00FFFU)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_PT_DATA_SHIFT (4U)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_PT_DATA_CLRMSK (0xFFFFF00FU)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_STALLED_SHIFT (2U)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_STALLED_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_STALLED_EN (0x00000004U)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_PAUSED_SHIFT (1U)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_PAUSED_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_PAUSED_EN (0x00000002U)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_BUSY_SHIFT (0U)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_BUSY_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_MEM_MMU_STATUS_BUSY_EN (0x00000001U)

/*
    Register RGX_CR_FWCORE_MEM_READS_EXT_STATUS
*/
#define RGX_CR_FWCORE_MEM_READS_EXT_STATUS (0x30D8U)
#define RGX_CR_FWCORE_MEM_READS_EXT_STATUS_MASKFULL \
	(IMG_UINT64_C(0x0000000000000FFF))
#define RGX_CR_FWCORE_MEM_READS_EXT_STATUS_MMU_SHIFT (0U)
#define RGX_CR_FWCORE_MEM_READS_EXT_STATUS_MMU_CLRMSK (0xFFFFF000U)

/*
    Register RGX_CR_FWCORE_MEM_READS_INT_STATUS
*/
#define RGX_CR_FWCORE_MEM_READS_INT_STATUS (0x30E0U)
#define RGX_CR_FWCORE_MEM_READS_INT_STATUS_MASKFULL \
	(IMG_UINT64_C(0x00000000000007FF))
#define RGX_CR_FWCORE_MEM_READS_INT_STATUS_MMU_SHIFT (0U)
#define RGX_CR_FWCORE_MEM_READS_INT_STATUS_MMU_CLRMSK (0xFFFFF800U)

/*
    Register RGX_CR_FWCORE_WRAPPER_FENCE
*/
#define RGX_CR_FWCORE_WRAPPER_FENCE (0x30E8U)
#define RGX_CR_FWCORE_WRAPPER_FENCE_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_FWCORE_WRAPPER_FENCE_ID_SHIFT (0U)
#define RGX_CR_FWCORE_WRAPPER_FENCE_ID_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_WRAPPER_FENCE_ID_EN (0x00000001U)

/*
    Register group: RGX_CR_FWCORE_MEM_CAT_BASE, with 8 repeats
*/
#define RGX_CR_FWCORE_MEM_CAT_BASE_REPEATCOUNT (8U)
/*
    Register RGX_CR_FWCORE_MEM_CAT_BASE0
*/
#define RGX_CR_FWCORE_MEM_CAT_BASE0 (0x30F0U)
#define RGX_CR_FWCORE_MEM_CAT_BASE0_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_FWCORE_MEM_CAT_BASE0_ADDR_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE0_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_MEM_CAT_BASE0_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE0_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_MEM_CAT_BASE1
*/
#define RGX_CR_FWCORE_MEM_CAT_BASE1 (0x30F8U)
#define RGX_CR_FWCORE_MEM_CAT_BASE1_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_FWCORE_MEM_CAT_BASE1_ADDR_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE1_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_MEM_CAT_BASE1_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE1_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_MEM_CAT_BASE2
*/
#define RGX_CR_FWCORE_MEM_CAT_BASE2 (0x3100U)
#define RGX_CR_FWCORE_MEM_CAT_BASE2_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_FWCORE_MEM_CAT_BASE2_ADDR_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE2_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_MEM_CAT_BASE2_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE2_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_MEM_CAT_BASE3
*/
#define RGX_CR_FWCORE_MEM_CAT_BASE3 (0x3108U)
#define RGX_CR_FWCORE_MEM_CAT_BASE3_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_FWCORE_MEM_CAT_BASE3_ADDR_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE3_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_MEM_CAT_BASE3_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE3_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_MEM_CAT_BASE4
*/
#define RGX_CR_FWCORE_MEM_CAT_BASE4 (0x3110U)
#define RGX_CR_FWCORE_MEM_CAT_BASE4_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_FWCORE_MEM_CAT_BASE4_ADDR_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE4_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_MEM_CAT_BASE4_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE4_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_MEM_CAT_BASE5
*/
#define RGX_CR_FWCORE_MEM_CAT_BASE5 (0x3118U)
#define RGX_CR_FWCORE_MEM_CAT_BASE5_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_FWCORE_MEM_CAT_BASE5_ADDR_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE5_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_MEM_CAT_BASE5_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE5_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_MEM_CAT_BASE6
*/
#define RGX_CR_FWCORE_MEM_CAT_BASE6 (0x3120U)
#define RGX_CR_FWCORE_MEM_CAT_BASE6_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_FWCORE_MEM_CAT_BASE6_ADDR_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE6_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_MEM_CAT_BASE6_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE6_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_MEM_CAT_BASE7
*/
#define RGX_CR_FWCORE_MEM_CAT_BASE7 (0x3128U)
#define RGX_CR_FWCORE_MEM_CAT_BASE7_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_CR_FWCORE_MEM_CAT_BASE7_ADDR_SHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE7_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))
#define RGX_CR_FWCORE_MEM_CAT_BASE7_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_FWCORE_MEM_CAT_BASE7_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_FWCORE_WDT_RESET
*/
#define RGX_CR_FWCORE_WDT_RESET (0x3130U)
#define RGX_CR_FWCORE_WDT_RESET_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_FWCORE_WDT_RESET_EN_SHIFT (0U)
#define RGX_CR_FWCORE_WDT_RESET_EN_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_WDT_RESET_EN_EN (0x00000001U)

/*
    Register RGX_CR_FWCORE_WDT_CTRL
*/
#define RGX_CR_FWCORE_WDT_CTRL (0x3138U)
#define RGX_CR_FWCORE_WDT_CTRL_MASKFULL (IMG_UINT64_C(0x00000000FFFF1F01))
#define RGX_CR_FWCORE_WDT_CTRL_PROT_SHIFT (16U)
#define RGX_CR_FWCORE_WDT_CTRL_PROT_CLRMSK (0x0000FFFFU)
#define RGX_CR_FWCORE_WDT_CTRL_THRESHOLD_SHIFT (8U)
#define RGX_CR_FWCORE_WDT_CTRL_THRESHOLD_CLRMSK (0xFFFFE0FFU)
#define RGX_CR_FWCORE_WDT_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_FWCORE_WDT_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_WDT_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_FWCORE_WDT_COUNT
*/
#define RGX_CR_FWCORE_WDT_COUNT (0x3140U)
#define RGX_CR_FWCORE_WDT_COUNT_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_WDT_COUNT_VALUE_SHIFT (0U)
#define RGX_CR_FWCORE_WDT_COUNT_VALUE_CLRMSK (0x00000000U)

/*
    Register group: RGX_CR_FWCORE_DMI_RESERVED0, with 4 repeats
*/
#define RGX_CR_FWCORE_DMI_RESERVED0_REPEATCOUNT (4U)
/*
    Register RGX_CR_FWCORE_DMI_RESERVED00
*/
#define RGX_CR_FWCORE_DMI_RESERVED00 (0x3400U)
#define RGX_CR_FWCORE_DMI_RESERVED00_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED01
*/
#define RGX_CR_FWCORE_DMI_RESERVED01 (0x3408U)
#define RGX_CR_FWCORE_DMI_RESERVED01_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED02
*/
#define RGX_CR_FWCORE_DMI_RESERVED02 (0x3410U)
#define RGX_CR_FWCORE_DMI_RESERVED02_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED03
*/
#define RGX_CR_FWCORE_DMI_RESERVED03 (0x3418U)
#define RGX_CR_FWCORE_DMI_RESERVED03_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_DATA0
*/
#define RGX_CR_FWCORE_DMI_DATA0 (0x3420U)
#define RGX_CR_FWCORE_DMI_DATA0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_DMI_DATA0_VAL_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_DATA0_VAL_CLRMSK (0x00000000U)

/*
    Register RGX_CR_FWCORE_DMI_DATA1
*/
#define RGX_CR_FWCORE_DMI_DATA1 (0x3428U)
#define RGX_CR_FWCORE_DMI_DATA1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_DMI_DATA1_VAL_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_DATA1_VAL_CLRMSK (0x00000000U)

/*
    Register group: RGX_CR_FWCORE_DMI_RESERVED1, with 10 repeats
*/
#define RGX_CR_FWCORE_DMI_RESERVED1_REPEATCOUNT (10U)
/*
    Register RGX_CR_FWCORE_DMI_RESERVED10
*/
#define RGX_CR_FWCORE_DMI_RESERVED10 (0x3430U)
#define RGX_CR_FWCORE_DMI_RESERVED10_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED11
*/
#define RGX_CR_FWCORE_DMI_RESERVED11 (0x3438U)
#define RGX_CR_FWCORE_DMI_RESERVED11_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED12
*/
#define RGX_CR_FWCORE_DMI_RESERVED12 (0x3440U)
#define RGX_CR_FWCORE_DMI_RESERVED12_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED13
*/
#define RGX_CR_FWCORE_DMI_RESERVED13 (0x3448U)
#define RGX_CR_FWCORE_DMI_RESERVED13_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED14
*/
#define RGX_CR_FWCORE_DMI_RESERVED14 (0x3450U)
#define RGX_CR_FWCORE_DMI_RESERVED14_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_DMCONTROL
*/
#define RGX_CR_FWCORE_DMI_DMCONTROL (0x3480U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_MASKFULL (IMG_UINT64_C(0x00000000D0000003))
#define RGX_CR_FWCORE_DMI_DMCONTROL_HALTREQ_SHIFT (31U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_HALTREQ_CLRMSK (0x7FFFFFFFU)
#define RGX_CR_FWCORE_DMI_DMCONTROL_HALTREQ_EN (0x80000000U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_RESUMEREQ_SHIFT (30U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_RESUMEREQ_CLRMSK (0xBFFFFFFFU)
#define RGX_CR_FWCORE_DMI_DMCONTROL_RESUMEREQ_EN (0x40000000U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_ACKHAVERESET_SHIFT (28U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_ACKHAVERESET_CLRMSK (0xEFFFFFFFU)
#define RGX_CR_FWCORE_DMI_DMCONTROL_ACKHAVERESET_EN (0x10000000U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_NDMRESET_SHIFT (1U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_NDMRESET_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_FWCORE_DMI_DMCONTROL_NDMRESET_EN (0x00000002U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_DMACTIVE_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_DMCONTROL_DMACTIVE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_DMI_DMCONTROL_DMACTIVE_EN (0x00000001U)

/*
    Register RGX_CR_FWCORE_DMI_DMSTATUS
*/
#define RGX_CR_FWCORE_DMI_DMSTATUS (0x3488U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_MASKFULL (IMG_UINT64_C(0x00000000004FFFFF))
#define RGX_CR_FWCORE_DMI_DMSTATUS_IMPEBREAK_SHIFT (22U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_IMPEBREAK_CLRMSK (0xFFBFFFFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_IMPEBREAK_EN (0x00400000U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLHAVERESET_SHIFT (19U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLHAVERESET_CLRMSK (0xFFF7FFFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLHAVERESET_EN (0x00080000U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYHAVERESET_SHIFT (18U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYHAVERESET_CLRMSK (0xFFFBFFFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYHAVERESET_EN (0x00040000U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLRESUMEACK_SHIFT (17U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLRESUMEACK_CLRMSK (0xFFFDFFFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLRESUMEACK_EN (0x00020000U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYRESUMEACK_SHIFT (16U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYRESUMEACK_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYRESUMEACK_EN (0x00010000U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLNONEXISTENT_SHIFT (15U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLNONEXISTENT_CLRMSK (0xFFFF7FFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLNONEXISTENT_EN (0x00008000U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYNONEXISTENT_SHIFT (14U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYNONEXISTENT_CLRMSK (0xFFFFBFFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYNONEXISTENT_EN (0x00004000U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLUNAVAIL_SHIFT (13U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLUNAVAIL_CLRMSK (0xFFFFDFFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLUNAVAIL_EN (0x00002000U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYUNAVAIL_SHIFT (12U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYUNAVAIL_CLRMSK (0xFFFFEFFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYUNAVAIL_EN (0x00001000U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLRUNNING_SHIFT (11U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLRUNNING_CLRMSK (0xFFFFF7FFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLRUNNING_EN (0x00000800U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYRUNNING_SHIFT (10U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYRUNNING_CLRMSK (0xFFFFFBFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYRUNNING_EN (0x00000400U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLHALTED_SHIFT (9U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLHALTED_CLRMSK (0xFFFFFDFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ALLHALTED_EN (0x00000200U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYHALTED_SHIFT (8U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYHALTED_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_ANYHALTED_EN (0x00000100U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_AUTHENTICATED_SHIFT (7U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_AUTHENTICATED_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_AUTHENTICATED_EN (0x00000080U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_AUTHBUSY_SHIFT (6U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_AUTHBUSY_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_AUTHBUSY_EN (0x00000040U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_HASRESETHALTREQ_SHIFT (5U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_HASRESETHALTREQ_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_HASRESETHALTREQ_EN (0x00000020U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_CONFSTRPTRVALID_SHIFT (4U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_CONFSTRPTRVALID_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_FWCORE_DMI_DMSTATUS_CONFSTRPTRVALID_EN (0x00000010U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_VERSION_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_DMSTATUS_VERSION_CLRMSK (0xFFFFFFF0U)

/*
    Register group: RGX_CR_FWCORE_DMI_RESERVED2, with 4 repeats
*/
#define RGX_CR_FWCORE_DMI_RESERVED2_REPEATCOUNT (4U)
/*
    Register RGX_CR_FWCORE_DMI_RESERVED20
*/
#define RGX_CR_FWCORE_DMI_RESERVED20 (0x3490U)
#define RGX_CR_FWCORE_DMI_RESERVED20_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED21
*/
#define RGX_CR_FWCORE_DMI_RESERVED21 (0x3498U)
#define RGX_CR_FWCORE_DMI_RESERVED21_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED22
*/
#define RGX_CR_FWCORE_DMI_RESERVED22 (0x34A0U)
#define RGX_CR_FWCORE_DMI_RESERVED22_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED23
*/
#define RGX_CR_FWCORE_DMI_RESERVED23 (0x34A8U)
#define RGX_CR_FWCORE_DMI_RESERVED23_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_ABSTRACTCS
*/
#define RGX_CR_FWCORE_DMI_ABSTRACTCS (0x34B0U)
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_MASKFULL (IMG_UINT64_C(0x000000001F00170F))
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_PROGBUFSIZE_SHIFT (24U)
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_PROGBUFSIZE_CLRMSK (0xE0FFFFFFU)
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_BUSY_SHIFT (12U)
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_BUSY_CLRMSK (0xFFFFEFFFU)
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_BUSY_EN (0x00001000U)
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_CMDERR_SHIFT (8U)
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_CMDERR_CLRMSK (0xFFFFF8FFU)
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_DATACOUNT_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_ABSTRACTCS_DATACOUNT_CLRMSK (0xFFFFFFF0U)

/*
    Register RGX_CR_FWCORE_DMI_COMMAND
*/
#define RGX_CR_FWCORE_DMI_COMMAND (0x34B8U)
#define RGX_CR_FWCORE_DMI_COMMAND_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_DMI_COMMAND_CMDTYPE_SHIFT (24U)
#define RGX_CR_FWCORE_DMI_COMMAND_CMDTYPE_CLRMSK (0x00FFFFFFU)
#define RGX_CR_FWCORE_DMI_COMMAND_CONTROL_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_COMMAND_CONTROL_CLRMSK (0xFF000000U)

/*
    Register group: RGX_CR_FWCORE_DMI_RESERVED3, with 32 repeats
*/
#define RGX_CR_FWCORE_DMI_RESERVED3_REPEATCOUNT (32U)
/*
    Register RGX_CR_FWCORE_DMI_RESERVED30
*/
#define RGX_CR_FWCORE_DMI_RESERVED30 (0x34C0U)
#define RGX_CR_FWCORE_DMI_RESERVED30_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_RESERVED31
*/
#define RGX_CR_FWCORE_DMI_RESERVED31 (0x34C8U)
#define RGX_CR_FWCORE_DMI_RESERVED31_MASKFULL (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_FWCORE_DMI_SBCS
*/
#define RGX_CR_FWCORE_DMI_SBCS (0x35C0U)
#define RGX_CR_FWCORE_DMI_SBCS_MASKFULL (IMG_UINT64_C(0x00000000E07FFFFF))
#define RGX_CR_FWCORE_DMI_SBCS_SBVERSION_SHIFT (29U)
#define RGX_CR_FWCORE_DMI_SBCS_SBVERSION_CLRMSK (0x1FFFFFFFU)
#define RGX_CR_FWCORE_DMI_SBCS_SBBUSYERROR_SHIFT (22U)
#define RGX_CR_FWCORE_DMI_SBCS_SBBUSYERROR_CLRMSK (0xFFBFFFFFU)
#define RGX_CR_FWCORE_DMI_SBCS_SBBUSYERROR_EN (0x00400000U)
#define RGX_CR_FWCORE_DMI_SBCS_SBBUSY_SHIFT (21U)
#define RGX_CR_FWCORE_DMI_SBCS_SBBUSY_CLRMSK (0xFFDFFFFFU)
#define RGX_CR_FWCORE_DMI_SBCS_SBBUSY_EN (0x00200000U)
#define RGX_CR_FWCORE_DMI_SBCS_SBREADONADDR_SHIFT (20U)
#define RGX_CR_FWCORE_DMI_SBCS_SBREADONADDR_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_FWCORE_DMI_SBCS_SBREADONADDR_EN (0x00100000U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS_SHIFT (17U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS_CLRMSK (0xFFF1FFFFU)
#define RGX_CR_FWCORE_DMI_SBCS_SBAUTOINCREMENT_SHIFT (16U)
#define RGX_CR_FWCORE_DMI_SBCS_SBAUTOINCREMENT_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_FWCORE_DMI_SBCS_SBAUTOINCREMENT_EN (0x00010000U)
#define RGX_CR_FWCORE_DMI_SBCS_SBREADONDATA_SHIFT (15U)
#define RGX_CR_FWCORE_DMI_SBCS_SBREADONDATA_CLRMSK (0xFFFF7FFFU)
#define RGX_CR_FWCORE_DMI_SBCS_SBREADONDATA_EN (0x00008000U)
#define RGX_CR_FWCORE_DMI_SBCS_SBERROR_SHIFT (12U)
#define RGX_CR_FWCORE_DMI_SBCS_SBERROR_CLRMSK (0xFFFF8FFFU)
#define RGX_CR_FWCORE_DMI_SBCS_SBASIZE_SHIFT (5U)
#define RGX_CR_FWCORE_DMI_SBCS_SBASIZE_CLRMSK (0xFFFFF01FU)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS128_SHIFT (4U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS128_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS128_EN (0x00000010U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS64_SHIFT (3U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS64_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS64_EN (0x00000008U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS32_SHIFT (2U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS32_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS32_EN (0x00000004U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS16_SHIFT (1U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS16_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS16_EN (0x00000002U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS8_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS8_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FWCORE_DMI_SBCS_SBACCESS8_EN (0x00000001U)

/*
    Register RGX_CR_FWCORE_DMI_SBADDRESS0
*/
#define RGX_CR_FWCORE_DMI_SBADDRESS0 (0x35C8U)
#define RGX_CR_FWCORE_DMI_SBADDRESS0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_DMI_SBADDRESS0_ADDRESS_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_SBADDRESS0_ADDRESS_CLRMSK (0x00000000U)

/*
    Register group: RGX_CR_FWCORE_DMI_SBDATA, with 4 repeats
*/
#define RGX_CR_FWCORE_DMI_SBDATA_REPEATCOUNT (4U)
/*
    Register RGX_CR_FWCORE_DMI_SBDATA0
*/
#define RGX_CR_FWCORE_DMI_SBDATA0 (0x35E0U)
#define RGX_CR_FWCORE_DMI_SBDATA0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_DMI_SBDATA0_DATA_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_SBDATA0_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_FWCORE_DMI_SBDATA1
*/
#define RGX_CR_FWCORE_DMI_SBDATA1 (0x35E8U)
#define RGX_CR_FWCORE_DMI_SBDATA1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_DMI_SBDATA1_DATA_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_SBDATA1_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_FWCORE_DMI_SBDATA2
*/
#define RGX_CR_FWCORE_DMI_SBDATA2 (0x35F0U)
#define RGX_CR_FWCORE_DMI_SBDATA2_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_DMI_SBDATA2_DATA_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_SBDATA2_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_FWCORE_DMI_SBDATA3
*/
#define RGX_CR_FWCORE_DMI_SBDATA3 (0x35F8U)
#define RGX_CR_FWCORE_DMI_SBDATA3_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_DMI_SBDATA3_DATA_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_SBDATA3_DATA_CLRMSK (0x00000000U)

/*
    Register RGX_CR_FWCORE_DMI_HALTSUM0
*/
#define RGX_CR_FWCORE_DMI_HALTSUM0 (0x3600U)
#define RGX_CR_FWCORE_DMI_HALTSUM0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FWCORE_DMI_HALTSUM0_VAL_SHIFT (0U)
#define RGX_CR_FWCORE_DMI_HALTSUM0_VAL_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SLC_CTRL_MISC
*/
#define RGX_CR_SLC_CTRL_MISC (0x3800U)
#define RGX_CR_SLC_CTRL_MISC_MASKFULL (IMG_UINT64_C(0xFFFFFFFF03FF010F))
#define RGX_CR_SLC_CTRL_MISC_SCRAMBLE_BITS_SHIFT (32U)
#define RGX_CR_SLC_CTRL_MISC_SCRAMBLE_BITS_CLRMSK \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SLC_CTRL_MISC_TAG_ID_LIMIT_CONTROL_SHIFT (25U)
#define RGX_CR_SLC_CTRL_MISC_TAG_ID_LIMIT_CONTROL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFDFFFFFF))
#define RGX_CR_SLC_CTRL_MISC_TAG_ID_LIMIT_CONTROL_EN \
	(IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_SLC_CTRL_MISC_LAZYWB_OVERRIDE_SHIFT (24U)
#define RGX_CR_SLC_CTRL_MISC_LAZYWB_OVERRIDE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFEFFFFFF))
#define RGX_CR_SLC_CTRL_MISC_LAZYWB_OVERRIDE_EN \
	(IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_SLC_CTRL_MISC_ADDR_DECODE_MODE_SHIFT (16U)
#define RGX_CR_SLC_CTRL_MISC_ADDR_DECODE_MODE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFF00FFFF))
#define RGX_CR_SLC_CTRL_MISC_ADDR_DECODE_MODE_INTERLEAVED_64_BYTE \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_SLC_CTRL_MISC_ADDR_DECODE_MODE_INTERLEAVED_128_BYTE \
	(IMG_UINT64_C(0x0000000000010000))
#define RGX_CR_SLC_CTRL_MISC_ADDR_DECODE_MODE_SIMPLE_HASH1 \
	(IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_SLC_CTRL_MISC_ADDR_DECODE_MODE_SIMPLE_HASH2 \
	(IMG_UINT64_C(0x0000000000110000))
#define RGX_CR_SLC_CTRL_MISC_ADDR_DECODE_MODE_PVR_HASH1 \
	(IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_SLC_CTRL_MISC_ADDR_DECODE_MODE_PVR_HASH2_SCRAMBLE \
	(IMG_UINT64_C(0x0000000000210000))
#define RGX_CR_SLC_CTRL_MISC_PAUSE_SHIFT (8U)
#define RGX_CR_SLC_CTRL_MISC_PAUSE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFEFF))
#define RGX_CR_SLC_CTRL_MISC_PAUSE_EN (IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_SLC_CTRL_MISC_RESP_PRIORITY_SHIFT (3U)
#define RGX_CR_SLC_CTRL_MISC_RESP_PRIORITY_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFF7))
#define RGX_CR_SLC_CTRL_MISC_RESP_PRIORITY_EN (IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_SLC_CTRL_MISC_ENABLE_LINE_USE_LIMIT_SHIFT (2U)
#define RGX_CR_SLC_CTRL_MISC_ENABLE_LINE_USE_LIMIT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_CR_SLC_CTRL_MISC_ENABLE_LINE_USE_LIMIT_EN \
	(IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_SLC_CTRL_MISC_ENABLE_PSG_HAZARD_CHECK_SHIFT (1U)
#define RGX_CR_SLC_CTRL_MISC_ENABLE_PSG_HAZARD_CHECK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_SLC_CTRL_MISC_ENABLE_PSG_HAZARD_CHECK_EN \
	(IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_SLC_CTRL_MISC_BYPASS_BURST_COMBINER_SHIFT (0U)
#define RGX_CR_SLC_CTRL_MISC_BYPASS_BURST_COMBINER_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_SLC_CTRL_MISC_BYPASS_BURST_COMBINER_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_SLC_CTRL_FLUSH_INVAL
*/
#define RGX_CR_SLC_CTRL_FLUSH_INVAL (0x3818U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_MASKFULL (IMG_UINT64_C(0x0000000080000FFF))
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_LAZY_SHIFT (31U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_LAZY_CLRMSK (0x7FFFFFFFU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_LAZY_EN (0x80000000U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_FASTRENDER_SHIFT (11U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_FASTRENDER_CLRMSK (0xFFFFF7FFU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_FASTRENDER_EN (0x00000800U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_RAY_VERTEX_SHIFT (10U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_RAY_VERTEX_CLRMSK (0xFFFFFBFFU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_RAY_VERTEX_EN (0x00000400U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_RAY_SHIFT (9U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_RAY_CLRMSK (0xFFFFFDFFU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_RAY_EN (0x00000200U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_FRC_SHIFT (8U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_FRC_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_FRC_EN (0x00000100U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_VXE_SHIFT (7U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_VXE_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_VXE_EN (0x00000080U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_VXD_SHIFT (6U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_VXD_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_VXD_EN (0x00000040U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_HOST_META_SHIFT (5U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_HOST_META_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_HOST_META_EN (0x00000020U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_MMU_SHIFT (4U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_MMU_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_MMU_EN (0x00000010U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_COMPUTE_SHIFT (3U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_COMPUTE_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_COMPUTE_EN (0x00000008U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_PIXEL_SHIFT (2U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_PIXEL_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_PIXEL_EN (0x00000004U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_TA_SHIFT (1U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_TA_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_DM_TA_EN (0x00000002U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_ALL_SHIFT (0U)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_ALL_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SLC_CTRL_FLUSH_INVAL_ALL_EN (0x00000001U)

/*
    Register RGX_CR_SLC_STATUS0
*/
#define RGX_CR_SLC_STATUS0 (0x3820U)
#define RGX_CR_SLC_STATUS0_MASKFULL (IMG_UINT64_C(0x0000000000000007))
#define RGX_CR_SLC_STATUS0_FLUSH_INVAL_PENDING_SHIFT (2U)
#define RGX_CR_SLC_STATUS0_FLUSH_INVAL_PENDING_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_SLC_STATUS0_FLUSH_INVAL_PENDING_EN (0x00000004U)
#define RGX_CR_SLC_STATUS0_INVAL_PENDING_SHIFT (1U)
#define RGX_CR_SLC_STATUS0_INVAL_PENDING_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SLC_STATUS0_INVAL_PENDING_EN (0x00000002U)
#define RGX_CR_SLC_STATUS0_FLUSH_PENDING_SHIFT (0U)
#define RGX_CR_SLC_STATUS0_FLUSH_PENDING_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SLC_STATUS0_FLUSH_PENDING_EN (0x00000001U)

/*
    Register RGX_CR_SLC_CTRL_BYPASS
*/
#define RGX_CR_SLC_CTRL_BYPASS (0x3828U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__MASKFULL \
	(IMG_UINT64_C(0x0FFFFFFFFFFF7FFF))
#define RGX_CR_SLC_CTRL_BYPASS_MASKFULL (IMG_UINT64_C(0x000000000FFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_COMP_ZLS_SHIFT (59U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_COMP_ZLS_CLRMSK \
	(IMG_UINT64_C(0xF7FFFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_COMP_ZLS_EN \
	(IMG_UINT64_C(0x0800000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_ZLS_HEADER_SHIFT (58U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_ZLS_HEADER_CLRMSK \
	(IMG_UINT64_C(0xFBFFFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_ZLS_HEADER_EN \
	(IMG_UINT64_C(0x0400000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_TCU_HEADER_SHIFT (57U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_TCU_HEADER_CLRMSK \
	(IMG_UINT64_C(0xFDFFFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_TCU_HEADER_EN \
	(IMG_UINT64_C(0x0200000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_ZLS_DATA_SHIFT (56U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_ZLS_DATA_CLRMSK \
	(IMG_UINT64_C(0xFEFFFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_ZLS_DATA_EN \
	(IMG_UINT64_C(0x0100000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_TCU_DATA_SHIFT (55U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_TCU_DATA_CLRMSK \
	(IMG_UINT64_C(0xFF7FFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_DECOMP_TCU_DATA_EN \
	(IMG_UINT64_C(0x0080000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_COMP_PBE_SHIFT (54U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_COMP_PBE_CLRMSK \
	(IMG_UINT64_C(0xFFBFFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TFBC_COMP_PBE_EN \
	(IMG_UINT64_C(0x0040000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TCU_DM_COMPUTE_SHIFT (53U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TCU_DM_COMPUTE_CLRMSK \
	(IMG_UINT64_C(0xFFDFFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TCU_DM_COMPUTE_EN \
	(IMG_UINT64_C(0x0020000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__PDSRW_NOLINEFILL_SHIFT (52U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__PDSRW_NOLINEFILL_CLRMSK \
	(IMG_UINT64_C(0xFFEFFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__PDSRW_NOLINEFILL_EN \
	(IMG_UINT64_C(0x0010000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__PBE_NOLINEFILL_SHIFT (51U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__PBE_NOLINEFILL_CLRMSK \
	(IMG_UINT64_C(0xFFF7FFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__PBE_NOLINEFILL_EN \
	(IMG_UINT64_C(0x0008000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_FBC_SHIFT (50U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_FBC_CLRMSK \
	(IMG_UINT64_C(0xFFFBFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_FBC_EN \
	(IMG_UINT64_C(0x0004000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_RREQ_SHIFT (49U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_RREQ_CLRMSK \
	(IMG_UINT64_C(0xFFFDFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_RREQ_EN \
	(IMG_UINT64_C(0x0002000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_CREQ_SHIFT (48U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_CREQ_CLRMSK \
	(IMG_UINT64_C(0xFFFEFFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_CREQ_EN \
	(IMG_UINT64_C(0x0001000000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_PREQ_SHIFT (47U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_PREQ_CLRMSK \
	(IMG_UINT64_C(0xFFFF7FFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_PREQ_EN \
	(IMG_UINT64_C(0x0000800000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_DBSC_SHIFT (46U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_DBSC_CLRMSK \
	(IMG_UINT64_C(0xFFFFBFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_IPF_DBSC_EN \
	(IMG_UINT64_C(0x0000400000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TCU_SHIFT (45U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TCU_CLRMSK \
	(IMG_UINT64_C(0xFFFFDFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TCU_EN \
	(IMG_UINT64_C(0x0000200000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_PBE_SHIFT (44U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_PBE_CLRMSK \
	(IMG_UINT64_C(0xFFFFEFFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_PBE_EN \
	(IMG_UINT64_C(0x0000100000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_ISP_SHIFT (43U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_ISP_CLRMSK \
	(IMG_UINT64_C(0xFFFFF7FFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_ISP_EN \
	(IMG_UINT64_C(0x0000080000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_PM_SHIFT (42U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_PM_CLRMSK \
	(IMG_UINT64_C(0xFFFFFBFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_PM_EN \
	(IMG_UINT64_C(0x0000040000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TDM_SHIFT (41U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TDM_CLRMSK \
	(IMG_UINT64_C(0xFFFFFDFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TDM_EN \
	(IMG_UINT64_C(0x0000020000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_CDM_SHIFT (40U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_CDM_CLRMSK \
	(IMG_UINT64_C(0xFFFFFEFFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_CDM_EN \
	(IMG_UINT64_C(0x0000010000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TSPF_PDS_STATE_SHIFT (39U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TSPF_PDS_STATE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF7FFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TSPF_PDS_STATE_EN \
	(IMG_UINT64_C(0x0000008000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TSPF_DB_SHIFT (38U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TSPF_DB_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFBFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TSPF_DB_EN \
	(IMG_UINT64_C(0x0000004000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TSPF_VTX_VAR_SHIFT (37U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TSPF_VTX_VAR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFDFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TSPF_VTX_VAR_EN \
	(IMG_UINT64_C(0x0000002000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_VDM_SHIFT (36U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_VDM_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFEFFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_VDM_EN \
	(IMG_UINT64_C(0x0000001000000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_PSG_STREAM_SHIFT (35U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_PSG_STREAM_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFF7FFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_PSG_STREAM_EN \
	(IMG_UINT64_C(0x0000000800000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_PSG_REGION_SHIFT (34U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_PSG_REGION_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFBFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_PSG_REGION_EN \
	(IMG_UINT64_C(0x0000000400000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_VCE_SHIFT (33U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_VCE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFDFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_VCE_EN \
	(IMG_UINT64_C(0x0000000200000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_PPP_SHIFT (32U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_PPP_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFEFFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__REQ_TA_PPP_EN \
	(IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_FASTRENDER_SHIFT (31U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_FASTRENDER_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF7FFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_FASTRENDER_EN \
	(IMG_UINT64_C(0x0000000080000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_PM_ALIST_SHIFT (30U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_PM_ALIST_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFBFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_PM_ALIST_EN \
	(IMG_UINT64_C(0x0000000040000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_PB_TE_SHIFT (29U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_PB_TE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFDFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_PB_TE_EN \
	(IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_PB_VCE_SHIFT (28U)
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_PB_VCE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFEFFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS__XE_MEM__DM_PB_VCE_EN \
	(IMG_UINT64_C(0x0000000010000000))
#define RGX_CR_SLC_CTRL_BYPASS_DM_RAY_VERTEX_SHIFT (27U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_RAY_VERTEX_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF7FFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_DM_RAY_VERTEX_EN \
	(IMG_UINT64_C(0x0000000008000000))
#define RGX_CR_SLC_CTRL_BYPASS_DM_RAY_SHIFT (26U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_RAY_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFBFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_DM_RAY_EN (IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_IPF_CPF_SHIFT (25U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_IPF_CPF_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFDFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_IPF_CPF_EN (IMG_UINT64_C(0x0000000002000000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TPU_SHIFT (24U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TPU_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFEFFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TPU_EN (IMG_UINT64_C(0x0000000001000000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_FBDC_SHIFT (23U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_FBDC_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFF7FFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_FBDC_EN (IMG_UINT64_C(0x0000000000800000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TLA_SHIFT (22U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TLA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFBFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TLA_EN (IMG_UINT64_C(0x0000000000400000))
#define RGX_CR_SLC_CTRL_BYPASS_BYP_CC_N_SHIFT (21U)
#define RGX_CR_SLC_CTRL_BYPASS_BYP_CC_N_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_BYP_CC_N_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_SLC_CTRL_BYPASS_BYP_CC_SHIFT (20U)
#define RGX_CR_SLC_CTRL_BYPASS_BYP_CC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFEFFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_BYP_CC_EN (IMG_UINT64_C(0x0000000000100000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MCU_SHIFT (19U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MCU_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFF7FFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MCU_EN (IMG_UINT64_C(0x0000000000080000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_PDS_SHIFT (18U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_PDS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFBFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_PDS_EN (IMG_UINT64_C(0x0000000000040000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TPF_SHIFT (17U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TPF_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFDFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TPF_EN (IMG_UINT64_C(0x0000000000020000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TA_TPC_SHIFT (16U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TA_TPC_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFEFFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_TA_TPC_EN (IMG_UINT64_C(0x0000000000010000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_IPF_OBJ_SHIFT (15U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_IPF_OBJ_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF7FFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_IPF_OBJ_EN (IMG_UINT64_C(0x0000000000008000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_USC_SHIFT (14U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_USC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFBFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_USC_EN (IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_META_SHIFT (13U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_META_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFDFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_META_EN (IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_HOST_SHIFT (12U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_HOST_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFEFFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_HOST_EN (IMG_UINT64_C(0x0000000000001000))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MMU_PT_SHIFT (11U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MMU_PT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF7FF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MMU_PT_EN (IMG_UINT64_C(0x0000000000000800))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MMU_PD_SHIFT (10U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MMU_PD_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFBFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MMU_PD_EN (IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MMU_PC_SHIFT (9U)
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MMU_PC_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFDFF))
#define RGX_CR_SLC_CTRL_BYPASS_REQ_MMU_PC_EN (IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_SLC_CTRL_BYPASS_DM_FRC_SHIFT (8U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_FRC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFEFF))
#define RGX_CR_SLC_CTRL_BYPASS_DM_FRC_EN (IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_SLC_CTRL_BYPASS_DM_VXE_SHIFT (7U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_VXE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFF7F))
#define RGX_CR_SLC_CTRL_BYPASS_DM_VXE_EN (IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_SLC_CTRL_BYPASS_DM_VXD_SHIFT (6U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_VXD_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFBF))
#define RGX_CR_SLC_CTRL_BYPASS_DM_VXD_EN (IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_SLC_CTRL_BYPASS_DM_HOST_META_SHIFT (5U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_HOST_META_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_SLC_CTRL_BYPASS_DM_HOST_META_EN \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_SLC_CTRL_BYPASS_DM_MMU_SHIFT (4U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_MMU_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFEF))
#define RGX_CR_SLC_CTRL_BYPASS_DM_MMU_EN (IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_SLC_CTRL_BYPASS_DM_COMPUTE_SHIFT (3U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_COMPUTE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFF7))
#define RGX_CR_SLC_CTRL_BYPASS_DM_COMPUTE_EN (IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_SLC_CTRL_BYPASS_DM_PIXEL_SHIFT (2U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_PIXEL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_CR_SLC_CTRL_BYPASS_DM_PIXEL_EN (IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_SLC_CTRL_BYPASS_DM_TA_SHIFT (1U)
#define RGX_CR_SLC_CTRL_BYPASS_DM_TA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_SLC_CTRL_BYPASS_DM_TA_EN (IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_SLC_CTRL_BYPASS_ALL_SHIFT (0U)
#define RGX_CR_SLC_CTRL_BYPASS_ALL_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_SLC_CTRL_BYPASS_ALL_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_SLC_STATUS1
*/
#define RGX_CR_SLC_STATUS1 (0x3870U)
#define RGX_CR_SLC_STATUS1_MASKFULL (IMG_UINT64_C(0x800003FF03FFFFFF))
#define RGX_CR_SLC_STATUS1_PAUSED_SHIFT (63U)
#define RGX_CR_SLC_STATUS1_PAUSED_CLRMSK (IMG_UINT64_C(0x7FFFFFFFFFFFFFFF))
#define RGX_CR_SLC_STATUS1_PAUSED_EN (IMG_UINT64_C(0x8000000000000000))
#define RGX_CR_SLC_STATUS1_READS1_SHIFT (32U)
#define RGX_CR_SLC_STATUS1_READS1_CLRMSK (IMG_UINT64_C(0xFFFFFC00FFFFFFFF))
#define RGX_CR_SLC_STATUS1_READS0_SHIFT (16U)
#define RGX_CR_SLC_STATUS1_READS0_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFC00FFFF))
#define RGX_CR_SLC_STATUS1_READS1_EXT_SHIFT (8U)
#define RGX_CR_SLC_STATUS1_READS1_EXT_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFF00FF))
#define RGX_CR_SLC_STATUS1_READS0_EXT_SHIFT (0U)
#define RGX_CR_SLC_STATUS1_READS0_EXT_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFF00))

/*
    Register RGX_CR_SLC_IDLE
*/
#define RGX_CR_SLC_IDLE (0x3898U)
#define RGX_CR_SLC_IDLE__XE_MEM__MASKFULL (IMG_UINT64_C(0x00000000000003FF))
#define RGX_CR_SLC_IDLE_MASKFULL (IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_SLC_IDLE__XE_MEM__MH_SYSARB1_SHIFT (9U)
#define RGX_CR_SLC_IDLE__XE_MEM__MH_SYSARB1_CLRMSK (0xFFFFFDFFU)
#define RGX_CR_SLC_IDLE__XE_MEM__MH_SYSARB1_EN (0x00000200U)
#define RGX_CR_SLC_IDLE__XE_MEM__MH_SYSARB0_SHIFT (8U)
#define RGX_CR_SLC_IDLE__XE_MEM__MH_SYSARB0_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_SLC_IDLE__XE_MEM__MH_SYSARB0_EN (0x00000100U)
#define RGX_CR_SLC_IDLE_IMGBV4_SHIFT (7U)
#define RGX_CR_SLC_IDLE_IMGBV4_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_SLC_IDLE_IMGBV4_EN (0x00000080U)
#define RGX_CR_SLC_IDLE_CACHE_BANKS_SHIFT (6U)
#define RGX_CR_SLC_IDLE_CACHE_BANKS_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_SLC_IDLE_CACHE_BANKS_EN (0x00000040U)
#define RGX_CR_SLC_IDLE_RBOFIFO_SHIFT (5U)
#define RGX_CR_SLC_IDLE_RBOFIFO_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_SLC_IDLE_RBOFIFO_EN (0x00000020U)
#define RGX_CR_SLC_IDLE_FRC_CONV_SHIFT (4U)
#define RGX_CR_SLC_IDLE_FRC_CONV_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_SLC_IDLE_FRC_CONV_EN (0x00000010U)
#define RGX_CR_SLC_IDLE_VXE_CONV_SHIFT (3U)
#define RGX_CR_SLC_IDLE_VXE_CONV_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_SLC_IDLE_VXE_CONV_EN (0x00000008U)
#define RGX_CR_SLC_IDLE_VXD_CONV_SHIFT (2U)
#define RGX_CR_SLC_IDLE_VXD_CONV_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_SLC_IDLE_VXD_CONV_EN (0x00000004U)
#define RGX_CR_SLC_IDLE_BIF1_CONV_SHIFT (1U)
#define RGX_CR_SLC_IDLE_BIF1_CONV_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SLC_IDLE_BIF1_CONV_EN (0x00000002U)
#define RGX_CR_SLC_IDLE_CBAR_SHIFT (0U)
#define RGX_CR_SLC_IDLE_CBAR_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SLC_IDLE_CBAR_EN (0x00000001U)

/*
    Register RGX_CR_SLC_STATUS2
*/
#define RGX_CR_SLC_STATUS2 (0x3908U)
#define RGX_CR_SLC_STATUS2_MASKFULL (IMG_UINT64_C(0x000003FF03FFFFFF))
#define RGX_CR_SLC_STATUS2_READS3_SHIFT (32U)
#define RGX_CR_SLC_STATUS2_READS3_CLRMSK (IMG_UINT64_C(0xFFFFFC00FFFFFFFF))
#define RGX_CR_SLC_STATUS2_READS2_SHIFT (16U)
#define RGX_CR_SLC_STATUS2_READS2_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFC00FFFF))
#define RGX_CR_SLC_STATUS2_READS3_EXT_SHIFT (8U)
#define RGX_CR_SLC_STATUS2_READS3_EXT_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFF00FF))
#define RGX_CR_SLC_STATUS2_READS2_EXT_SHIFT (0U)
#define RGX_CR_SLC_STATUS2_READS2_EXT_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFF00))

/*
    Register RGX_CR_SLC_CTRL_MISC2
*/
#define RGX_CR_SLC_CTRL_MISC2 (0x3930U)
#define RGX_CR_SLC_CTRL_MISC2_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SLC_CTRL_MISC2_SCRAMBLE_BITS_SHIFT (0U)
#define RGX_CR_SLC_CTRL_MISC2_SCRAMBLE_BITS_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SLC_CROSSBAR_LOAD_BALANCE
*/
#define RGX_CR_SLC_CROSSBAR_LOAD_BALANCE (0x3938U)
#define RGX_CR_SLC_CROSSBAR_LOAD_BALANCE_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_SLC_CROSSBAR_LOAD_BALANCE_BYPASS_SHIFT (0U)
#define RGX_CR_SLC_CROSSBAR_LOAD_BALANCE_BYPASS_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SLC_CROSSBAR_LOAD_BALANCE_BYPASS_EN (0x00000001U)

/*
    Register RGX_CR_SLC_SIZE_IN_KB
*/
#define RGX_CR_SLC_SIZE_IN_KB (0x3970U)
#define RGX_CR_SLC_SIZE_IN_KB_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_SLC_SIZE_IN_KB_SIZE_SHIFT (0U)
#define RGX_CR_SLC_SIZE_IN_KB_SIZE_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_USC_TIMER
*/
#define RGX_CR_USC_TIMER (0x46C8U)
#define RGX_CR_USC_TIMER_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_USC_TIMER_CNT_SHIFT (0U)
#define RGX_CR_USC_TIMER_CNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_USC_TIMER_CNT
*/
#define RGX_CR_USC_TIMER_CNT (0x46D0U)
#define RGX_CR_USC_TIMER_CNT_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_USC_TIMER_CNT_RESET_SHIFT (0U)
#define RGX_CR_USC_TIMER_CNT_RESET_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_USC_TIMER_CNT_RESET_EN (0x00000001U)

/*
    Register RGX_CR_USC_UVS0_CHECKSUM
*/
#define RGX_CR_USC_UVS0_CHECKSUM (0x5000U)
#define RGX_CR_USC_UVS0_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_USC_UVS0_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_USC_UVS0_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_USC_UVS1_CHECKSUM
*/
#define RGX_CR_USC_UVS1_CHECKSUM (0x5008U)
#define RGX_CR_USC_UVS1_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_USC_UVS1_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_USC_UVS1_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_USC_UVS2_CHECKSUM
*/
#define RGX_CR_USC_UVS2_CHECKSUM (0x5010U)
#define RGX_CR_USC_UVS2_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_USC_UVS2_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_USC_UVS2_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_USC_UVS3_CHECKSUM
*/
#define RGX_CR_USC_UVS3_CHECKSUM (0x5018U)
#define RGX_CR_USC_UVS3_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_USC_UVS3_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_USC_UVS3_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PPP_SIGNATURE
*/
#define RGX_CR_PPP_SIGNATURE (0x5020U)
#define RGX_CR_PPP_SIGNATURE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PPP_SIGNATURE_VALUE_SHIFT (0U)
#define RGX_CR_PPP_SIGNATURE_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TE_SIGNATURE
*/
#define RGX_CR_TE_SIGNATURE (0x5028U)
#define RGX_CR_TE_SIGNATURE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TE_SIGNATURE_VALUE_SHIFT (0U)
#define RGX_CR_TE_SIGNATURE_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TE_CHECKSUM
*/
#define RGX_CR_TE_CHECKSUM (0x5110U)
#define RGX_CR_TE_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TE_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_TE_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_USC_UVB_CHECKSUM
*/
#define RGX_CR_USC_UVB_CHECKSUM (0x5118U)
#define RGX_CR_USC_UVB_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_USC_UVB_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_USC_UVB_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_VCE_CHECKSUM
*/
#define RGX_CR_VCE_CHECKSUM (0x5030U)
#define RGX_CR_VCE_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_VCE_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_VCE_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_ISP_PDS_CHECKSUM
*/
#define RGX_CR_ISP_PDS_CHECKSUM (0x5038U)
#define RGX_CR_ISP_PDS_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_ISP_PDS_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_ISP_PDS_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_ISP_TPF_CHECKSUM
*/
#define RGX_CR_ISP_TPF_CHECKSUM (0x5040U)
#define RGX_CR_ISP_TPF_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_ISP_TPF_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_ISP_TPF_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TFPU_PLANE0_CHECKSUM
*/
#define RGX_CR_TFPU_PLANE0_CHECKSUM (0x5048U)
#define RGX_CR_TFPU_PLANE0_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TFPU_PLANE0_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_TFPU_PLANE0_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TFPU_PLANE1_CHECKSUM
*/
#define RGX_CR_TFPU_PLANE1_CHECKSUM (0x5050U)
#define RGX_CR_TFPU_PLANE1_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TFPU_PLANE1_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_TFPU_PLANE1_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PBE_CHECKSUM
*/
#define RGX_CR_PBE_CHECKSUM (0x5058U)
#define RGX_CR_PBE_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PBE_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_PBE_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PDS_DOUTM_STM_SIGNATURE
*/
#define RGX_CR_PDS_DOUTM_STM_SIGNATURE (0x5060U)
#define RGX_CR_PDS_DOUTM_STM_SIGNATURE_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PDS_DOUTM_STM_SIGNATURE_VALUE_SHIFT (0U)
#define RGX_CR_PDS_DOUTM_STM_SIGNATURE_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_IFPU_ISP_CHECKSUM
*/
#define RGX_CR_IFPU_ISP_CHECKSUM (0x5068U)
#define RGX_CR_IFPU_ISP_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_IFPU_ISP_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_IFPU_ISP_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_USC_UVS4_CHECKSUM
*/
#define RGX_CR_USC_UVS4_CHECKSUM (0x5100U)
#define RGX_CR_USC_UVS4_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_USC_UVS4_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_USC_UVS4_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_USC_UVS5_CHECKSUM
*/
#define RGX_CR_USC_UVS5_CHECKSUM (0x5108U)
#define RGX_CR_USC_UVS5_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_USC_UVS5_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_USC_UVS5_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PPP_CLIP_CHECKSUM
*/
#define RGX_CR_PPP_CLIP_CHECKSUM (0x5120U)
#define RGX_CR_PPP_CLIP_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PPP_CLIP_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_PPP_CLIP_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_TA_PHASE
*/
#define RGX_CR_PERF_TA_PHASE (0x6008U)
#define RGX_CR_PERF_TA_PHASE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_TA_PHASE_COUNT_SHIFT (0U)
#define RGX_CR_PERF_TA_PHASE_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_3D_PHASE
*/
#define RGX_CR_PERF_3D_PHASE (0x6010U)
#define RGX_CR_PERF_3D_PHASE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_3D_PHASE_COUNT_SHIFT (0U)
#define RGX_CR_PERF_3D_PHASE_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_COMPUTE_PHASE
*/
#define RGX_CR_PERF_COMPUTE_PHASE (0x6018U)
#define RGX_CR_PERF_COMPUTE_PHASE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_COMPUTE_PHASE_COUNT_SHIFT (0U)
#define RGX_CR_PERF_COMPUTE_PHASE_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_TA_CYCLE
*/
#define RGX_CR_PERF_TA_CYCLE (0x6020U)
#define RGX_CR_PERF_TA_CYCLE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_TA_CYCLE_COUNT_SHIFT (0U)
#define RGX_CR_PERF_TA_CYCLE_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_3D_CYCLE
*/
#define RGX_CR_PERF_3D_CYCLE (0x6028U)
#define RGX_CR_PERF_3D_CYCLE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_3D_CYCLE_COUNT_SHIFT (0U)
#define RGX_CR_PERF_3D_CYCLE_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_COMPUTE_CYCLE
*/
#define RGX_CR_PERF_COMPUTE_CYCLE (0x6030U)
#define RGX_CR_PERF_COMPUTE_CYCLE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_COMPUTE_CYCLE_COUNT_SHIFT (0U)
#define RGX_CR_PERF_COMPUTE_CYCLE_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_TA_OR_3D_CYCLE
*/
#define RGX_CR_PERF_TA_OR_3D_CYCLE (0x6038U)
#define RGX_CR_PERF_TA_OR_3D_CYCLE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_TA_OR_3D_CYCLE_COUNT_SHIFT (0U)
#define RGX_CR_PERF_TA_OR_3D_CYCLE_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_INITIAL_TA_CYCLE
*/
#define RGX_CR_PERF_INITIAL_TA_CYCLE (0x6040U)
#define RGX_CR_PERF_INITIAL_TA_CYCLE_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_INITIAL_TA_CYCLE_COUNT_SHIFT (0U)
#define RGX_CR_PERF_INITIAL_TA_CYCLE_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_SLC0_READ_STALL
*/
#define RGX_CR_PERF_SLC0_READ_STALL (0x60B8U)
#define RGX_CR_PERF_SLC0_READ_STALL_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_SLC0_READ_STALL_COUNT_SHIFT (0U)
#define RGX_CR_PERF_SLC0_READ_STALL_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_SLC0_WRITE_STALL
*/
#define RGX_CR_PERF_SLC0_WRITE_STALL (0x60C0U)
#define RGX_CR_PERF_SLC0_WRITE_STALL_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_SLC0_WRITE_STALL_COUNT_SHIFT (0U)
#define RGX_CR_PERF_SLC0_WRITE_STALL_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_SLC1_READ_STALL
*/
#define RGX_CR_PERF_SLC1_READ_STALL (0x60E0U)
#define RGX_CR_PERF_SLC1_READ_STALL_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_SLC1_READ_STALL_COUNT_SHIFT (0U)
#define RGX_CR_PERF_SLC1_READ_STALL_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_SLC1_WRITE_STALL
*/
#define RGX_CR_PERF_SLC1_WRITE_STALL (0x60E8U)
#define RGX_CR_PERF_SLC1_WRITE_STALL_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_SLC1_WRITE_STALL_COUNT_SHIFT (0U)
#define RGX_CR_PERF_SLC1_WRITE_STALL_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_SLC2_READ_STALL
*/
#define RGX_CR_PERF_SLC2_READ_STALL (0x6158U)
#define RGX_CR_PERF_SLC2_READ_STALL_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_SLC2_READ_STALL_COUNT_SHIFT (0U)
#define RGX_CR_PERF_SLC2_READ_STALL_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_SLC2_WRITE_STALL
*/
#define RGX_CR_PERF_SLC2_WRITE_STALL (0x6160U)
#define RGX_CR_PERF_SLC2_WRITE_STALL_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_SLC2_WRITE_STALL_COUNT_SHIFT (0U)
#define RGX_CR_PERF_SLC2_WRITE_STALL_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_SLC3_READ_STALL
*/
#define RGX_CR_PERF_SLC3_READ_STALL (0x6180U)
#define RGX_CR_PERF_SLC3_READ_STALL_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_SLC3_READ_STALL_COUNT_SHIFT (0U)
#define RGX_CR_PERF_SLC3_READ_STALL_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_SLC3_WRITE_STALL
*/
#define RGX_CR_PERF_SLC3_WRITE_STALL (0x6188U)
#define RGX_CR_PERF_SLC3_WRITE_STALL_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_SLC3_WRITE_STALL_COUNT_SHIFT (0U)
#define RGX_CR_PERF_SLC3_WRITE_STALL_COUNT_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PERF_3D_SPINUP
*/
#define RGX_CR_PERF_3D_SPINUP (0x6220U)
#define RGX_CR_PERF_3D_SPINUP_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PERF_3D_SPINUP_CYCLES_SHIFT (0U)
#define RGX_CR_PERF_3D_SPINUP_CYCLES_CLRMSK (0x00000000U)

/*
    Register RGX_CR_AXI_ACE_LITE_CONFIGURATION
*/
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION (0x38C0U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_MASKFULL \
	(IMG_UINT64_C(0x00003FFFFFFFFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ENABLE_FENCE_OUT_SHIFT (45U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ENABLE_FENCE_OUT_CLRMSK \
	(IMG_UINT64_C(0xFFFFDFFFFFFFFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ENABLE_FENCE_OUT_EN \
	(IMG_UINT64_C(0x0000200000000000))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_OSID_SECURITY_SHIFT (37U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_OSID_SECURITY_CLRMSK \
	(IMG_UINT64_C(0xFFFFE01FFFFFFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_DISABLE_COHERENT_WRITELINEUNIQUE_SHIFT \
	(36U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_DISABLE_COHERENT_WRITELINEUNIQUE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFEFFFFFFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_DISABLE_COHERENT_WRITELINEUNIQUE_EN \
	(IMG_UINT64_C(0x0000001000000000))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_DISABLE_COHERENT_WRITE_SHIFT (35U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_DISABLE_COHERENT_WRITE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFF7FFFFFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_DISABLE_COHERENT_WRITE_EN \
	(IMG_UINT64_C(0x0000000800000000))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_DISABLE_COHERENT_READ_SHIFT (34U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_DISABLE_COHERENT_READ_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFBFFFFFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_DISABLE_COHERENT_READ_EN \
	(IMG_UINT64_C(0x0000000400000000))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARCACHE_CACHE_MAINTENANCE_SHIFT (30U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARCACHE_CACHE_MAINTENANCE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFC3FFFFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARCACHE_COHERENT_SHIFT (26U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARCACHE_COHERENT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFC3FFFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWCACHE_COHERENT_SHIFT (22U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWCACHE_COHERENT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFC3FFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARDOMAIN_BARRIER_SHIFT (20U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARDOMAIN_BARRIER_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFCFFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWDOMAIN_BARRIER_SHIFT (18U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWDOMAIN_BARRIER_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFF3FFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARDOMAIN_CACHE_MAINTENANCE_SHIFT (16U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARDOMAIN_CACHE_MAINTENANCE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFCFFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWDOMAIN_COHERENT_SHIFT (14U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWDOMAIN_COHERENT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF3FFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARDOMAIN_COHERENT_SHIFT (12U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARDOMAIN_COHERENT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFCFFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARDOMAIN_NON_SNOOPING_SHIFT (10U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARDOMAIN_NON_SNOOPING_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF3FF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWDOMAIN_NON_SNOOPING_SHIFT (8U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWDOMAIN_NON_SNOOPING_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFCFF))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARCACHE_NON_SNOOPING_SHIFT (4U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_ARCACHE_NON_SNOOPING_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFF0F))
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWCACHE_NON_SNOOPING_SHIFT (0U)
#define RGX_CR_AXI_ACE_LITE_CONFIGURATION_AWCACHE_NON_SNOOPING_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFF0))

/*
    Register RGX_CR_POWER_ESTIMATE_RESULT
*/
#define RGX_CR_POWER_ESTIMATE_RESULT (0x6328U)
#define RGX_CR_POWER_ESTIMATE_RESULT_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_POWER_ESTIMATE_RESULT_VALUE_SHIFT (0U)
#define RGX_CR_POWER_ESTIMATE_RESULT_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TA_PERF
*/
#define RGX_CR_TA_PERF (0x7600U)
#define RGX_CR_TA_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_TA_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_TA_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_TA_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_TA_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_TA_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_TA_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_TA_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_TA_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_TA_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_TA_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_TA_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_TA_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_TA_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_TA_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_TA_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_TA_PERF_SELECT0
*/
#define RGX_CR_TA_PERF_SELECT0 (0x7608U)
#define RGX_CR_TA_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_TA_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_TA_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_TA_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_TA_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_TA_PERF_SELECT0_MODE_SHIFT (21U)
#define RGX_CR_TA_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_TA_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_TA_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_TA_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_TA_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_TA_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_TA_PERF_SELECT1
*/
#define RGX_CR_TA_PERF_SELECT1 (0x7610U)
#define RGX_CR_TA_PERF_SELECT1_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_TA_PERF_SELECT1_BATCH_MAX_SHIFT (48U)
#define RGX_CR_TA_PERF_SELECT1_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_TA_PERF_SELECT1_BATCH_MIN_SHIFT (32U)
#define RGX_CR_TA_PERF_SELECT1_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_TA_PERF_SELECT1_MODE_SHIFT (21U)
#define RGX_CR_TA_PERF_SELECT1_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_TA_PERF_SELECT1_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_TA_PERF_SELECT1_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_TA_PERF_SELECT1_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_TA_PERF_SELECT1_BIT_SELECT_SHIFT (0U)
#define RGX_CR_TA_PERF_SELECT1_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_TA_PERF_SELECT2
*/
#define RGX_CR_TA_PERF_SELECT2 (0x7618U)
#define RGX_CR_TA_PERF_SELECT2_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_TA_PERF_SELECT2_BATCH_MAX_SHIFT (48U)
#define RGX_CR_TA_PERF_SELECT2_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_TA_PERF_SELECT2_BATCH_MIN_SHIFT (32U)
#define RGX_CR_TA_PERF_SELECT2_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_TA_PERF_SELECT2_MODE_SHIFT (21U)
#define RGX_CR_TA_PERF_SELECT2_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_TA_PERF_SELECT2_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_TA_PERF_SELECT2_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_TA_PERF_SELECT2_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_TA_PERF_SELECT2_BIT_SELECT_SHIFT (0U)
#define RGX_CR_TA_PERF_SELECT2_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_TA_PERF_SELECT3
*/
#define RGX_CR_TA_PERF_SELECT3 (0x7620U)
#define RGX_CR_TA_PERF_SELECT3_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_TA_PERF_SELECT3_BATCH_MAX_SHIFT (48U)
#define RGX_CR_TA_PERF_SELECT3_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_TA_PERF_SELECT3_BATCH_MIN_SHIFT (32U)
#define RGX_CR_TA_PERF_SELECT3_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_TA_PERF_SELECT3_MODE_SHIFT (21U)
#define RGX_CR_TA_PERF_SELECT3_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_TA_PERF_SELECT3_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_TA_PERF_SELECT3_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_TA_PERF_SELECT3_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_TA_PERF_SELECT3_BIT_SELECT_SHIFT (0U)
#define RGX_CR_TA_PERF_SELECT3_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_TA_PERF_SELECTED_BITS
*/
#define RGX_CR_TA_PERF_SELECTED_BITS (0x7648U)
#define RGX_CR_TA_PERF_SELECTED_BITS_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_TA_PERF_SELECTED_BITS_REG3_SHIFT (48U)
#define RGX_CR_TA_PERF_SELECTED_BITS_REG3_CLRMSK \
	(IMG_UINT64_C(0x0000FFFFFFFFFFFF))
#define RGX_CR_TA_PERF_SELECTED_BITS_REG2_SHIFT (32U)
#define RGX_CR_TA_PERF_SELECTED_BITS_REG2_CLRMSK \
	(IMG_UINT64_C(0xFFFF0000FFFFFFFF))
#define RGX_CR_TA_PERF_SELECTED_BITS_REG1_SHIFT (16U)
#define RGX_CR_TA_PERF_SELECTED_BITS_REG1_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF0000FFFF))
#define RGX_CR_TA_PERF_SELECTED_BITS_REG0_SHIFT (0U)
#define RGX_CR_TA_PERF_SELECTED_BITS_REG0_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_TA_PERF_COUNTER_0
*/
#define RGX_CR_TA_PERF_COUNTER_0 (0x7650U)
#define RGX_CR_TA_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TA_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_TA_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TA_PERF_COUNTER_1
*/
#define RGX_CR_TA_PERF_COUNTER_1 (0x7658U)
#define RGX_CR_TA_PERF_COUNTER_1_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TA_PERF_COUNTER_1_REG_SHIFT (0U)
#define RGX_CR_TA_PERF_COUNTER_1_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TA_PERF_COUNTER_2
*/
#define RGX_CR_TA_PERF_COUNTER_2 (0x7660U)
#define RGX_CR_TA_PERF_COUNTER_2_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TA_PERF_COUNTER_2_REG_SHIFT (0U)
#define RGX_CR_TA_PERF_COUNTER_2_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TA_PERF_COUNTER_3
*/
#define RGX_CR_TA_PERF_COUNTER_3 (0x7668U)
#define RGX_CR_TA_PERF_COUNTER_3_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TA_PERF_COUNTER_3_REG_SHIFT (0U)
#define RGX_CR_TA_PERF_COUNTER_3_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_RASTERISATION_PERF
*/
#define RGX_CR_RASTERISATION_PERF (0x7700U)
#define RGX_CR_RASTERISATION_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_RASTERISATION_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_RASTERISATION_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_RASTERISATION_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_RASTERISATION_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_RASTERISATION_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_RASTERISATION_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_RASTERISATION_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_RASTERISATION_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_RASTERISATION_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_RASTERISATION_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_RASTERISATION_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_RASTERISATION_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_RASTERISATION_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_RASTERISATION_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_RASTERISATION_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_RASTERISATION_PERF_SELECT0
*/
#define RGX_CR_RASTERISATION_PERF_SELECT0 (0x7708U)
#define RGX_CR_RASTERISATION_PERF_SELECT0_MASKFULL \
	(IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_RASTERISATION_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_RASTERISATION_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_RASTERISATION_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_RASTERISATION_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_RASTERISATION_PERF_SELECT0_MODE_SHIFT (21U)
#define RGX_CR_RASTERISATION_PERF_SELECT0_MODE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_RASTERISATION_PERF_SELECT0_MODE_EN \
	(IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_RASTERISATION_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_RASTERISATION_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_RASTERISATION_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_RASTERISATION_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_RASTERISATION_PERF_COUNTER_0
*/
#define RGX_CR_RASTERISATION_PERF_COUNTER_0 (0x7750U)
#define RGX_CR_RASTERISATION_PERF_COUNTER_0_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_RASTERISATION_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_RASTERISATION_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_HUB_BIFPMCACHE_PERF
*/
#define RGX_CR_HUB_BIFPMCACHE_PERF (0x7800U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_HUB_BIFPMCACHE_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0
*/
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0 (0x7808U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_MASKFULL \
	(IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_MODE_SHIFT (21U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_MODE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_MODE_EN \
	(IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_HUB_BIFPMCACHE_PERF_COUNTER_0
*/
#define RGX_CR_HUB_BIFPMCACHE_PERF_COUNTER_0 (0x7850U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_COUNTER_0_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_HUB_BIFPMCACHE_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_HUB_BIFPMCACHE_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TPU_MCU_L0_PERF
*/
#define RGX_CR_TPU_MCU_L0_PERF (0x7900U)
#define RGX_CR_TPU_MCU_L0_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_TPU_MCU_L0_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_TPU_MCU_L0_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_TPU_MCU_L0_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_TPU_MCU_L0_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_TPU_MCU_L0_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_TPU_MCU_L0_PERF_SELECT0
*/
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0 (0x7908U)
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_MASKFULL \
	(IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_MODE_SHIFT (21U)
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_MODE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_MODE_EN \
	(IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_TPU_MCU_L0_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_TPU_MCU_L0_PERF_COUNTER_0
*/
#define RGX_CR_TPU_MCU_L0_PERF_COUNTER_0 (0x7950U)
#define RGX_CR_TPU_MCU_L0_PERF_COUNTER_0_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TPU_MCU_L0_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_TPU_MCU_L0_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_USC_PERF
*/
#define RGX_CR_USC_PERF (0x8100U)
#define RGX_CR_USC_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_USC_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_USC_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_USC_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_USC_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_USC_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_USC_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_USC_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_USC_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_USC_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_USC_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_USC_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_USC_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_USC_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_USC_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_USC_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_USC_PERF_SELECT0
*/
#define RGX_CR_USC_PERF_SELECT0 (0x8108U)
#define RGX_CR_USC_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_USC_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_USC_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_USC_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_USC_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_USC_PERF_SELECT0_MODE_SHIFT (21U)
#define RGX_CR_USC_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_USC_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_USC_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_USC_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_USC_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_USC_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_USC_PERF_COUNTER_0
*/
#define RGX_CR_USC_PERF_COUNTER_0 (0x8150U)
#define RGX_CR_USC_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_USC_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_USC_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_JONES_IDLE
*/
#define RGX_CR_JONES_IDLE (0x8328U)
#define RGX_CR_JONES_IDLE_MASKFULL (IMG_UINT64_C(0x0000000000007FFF))
#define RGX_CR_JONES_IDLE_TDM_SHIFT (14U)
#define RGX_CR_JONES_IDLE_TDM_CLRMSK (0xFFFFBFFFU)
#define RGX_CR_JONES_IDLE_TDM_EN (0x00004000U)
#define RGX_CR_JONES_IDLE_FB_CDC_TLA_SHIFT (13U)
#define RGX_CR_JONES_IDLE_FB_CDC_TLA_CLRMSK (0xFFFFDFFFU)
#define RGX_CR_JONES_IDLE_FB_CDC_TLA_EN (0x00002000U)
#define RGX_CR_JONES_IDLE_FB_CDC_SHIFT (12U)
#define RGX_CR_JONES_IDLE_FB_CDC_CLRMSK (0xFFFFEFFFU)
#define RGX_CR_JONES_IDLE_FB_CDC_EN (0x00001000U)
#define RGX_CR_JONES_IDLE_MMU_SHIFT (11U)
#define RGX_CR_JONES_IDLE_MMU_CLRMSK (0xFFFFF7FFU)
#define RGX_CR_JONES_IDLE_MMU_EN (0x00000800U)
#define RGX_CR_JONES_IDLE_TLA_SHIFT (10U)
#define RGX_CR_JONES_IDLE_TLA_CLRMSK (0xFFFFFBFFU)
#define RGX_CR_JONES_IDLE_TLA_EN (0x00000400U)
#define RGX_CR_JONES_IDLE_GARTEN_SHIFT (9U)
#define RGX_CR_JONES_IDLE_GARTEN_CLRMSK (0xFFFFFDFFU)
#define RGX_CR_JONES_IDLE_GARTEN_EN (0x00000200U)
#define RGX_CR_JONES_IDLE_HOSTIF_SHIFT (8U)
#define RGX_CR_JONES_IDLE_HOSTIF_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_JONES_IDLE_HOSTIF_EN (0x00000100U)
#define RGX_CR_JONES_IDLE_SOCIF_SHIFT (7U)
#define RGX_CR_JONES_IDLE_SOCIF_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_JONES_IDLE_SOCIF_EN (0x00000080U)
#define RGX_CR_JONES_IDLE_TILING_SHIFT (6U)
#define RGX_CR_JONES_IDLE_TILING_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_JONES_IDLE_TILING_EN (0x00000040U)
#define RGX_CR_JONES_IDLE_IPP_SHIFT (5U)
#define RGX_CR_JONES_IDLE_IPP_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_JONES_IDLE_IPP_EN (0x00000020U)
#define RGX_CR_JONES_IDLE_USCS_SHIFT (4U)
#define RGX_CR_JONES_IDLE_USCS_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_JONES_IDLE_USCS_EN (0x00000010U)
#define RGX_CR_JONES_IDLE_PM_SHIFT (3U)
#define RGX_CR_JONES_IDLE_PM_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_JONES_IDLE_PM_EN (0x00000008U)
#define RGX_CR_JONES_IDLE_CDM_SHIFT (2U)
#define RGX_CR_JONES_IDLE_CDM_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_JONES_IDLE_CDM_EN (0x00000004U)
#define RGX_CR_JONES_IDLE_VDM_SHIFT (1U)
#define RGX_CR_JONES_IDLE_VDM_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_JONES_IDLE_VDM_EN (0x00000002U)
#define RGX_CR_JONES_IDLE_BIF_SHIFT (0U)
#define RGX_CR_JONES_IDLE_BIF_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_JONES_IDLE_BIF_EN (0x00000001U)

/*
    Register RGX_CR_TORNADO_PERF
*/
#define RGX_CR_TORNADO_PERF (0x8228U)
#define RGX_CR_TORNADO_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_TORNADO_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_TORNADO_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_TORNADO_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_TORNADO_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_TORNADO_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_TORNADO_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_TORNADO_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_TORNADO_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_TORNADO_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_TORNADO_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_TORNADO_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_TORNADO_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_TORNADO_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_TORNADO_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_TORNADO_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_TORNADO_PERF_SELECT0
*/
#define RGX_CR_TORNADO_PERF_SELECT0 (0x8230U)
#define RGX_CR_TORNADO_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_TORNADO_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_TORNADO_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_TORNADO_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_TORNADO_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_TORNADO_PERF_SELECT0_MODE_SHIFT (21U)
#define RGX_CR_TORNADO_PERF_SELECT0_MODE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_TORNADO_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_TORNADO_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_TORNADO_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_TORNADO_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_TORNADO_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_TORNADO_PERF_COUNTER_0
*/
#define RGX_CR_TORNADO_PERF_COUNTER_0 (0x8268U)
#define RGX_CR_TORNADO_PERF_COUNTER_0_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TORNADO_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_TORNADO_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_TEXAS_PERF
*/
#define RGX_CR_TEXAS_PERF (0x8290U)
#define RGX_CR_TEXAS_PERF_MASKFULL (IMG_UINT64_C(0x000000000000007F))
#define RGX_CR_TEXAS_PERF_CLR_5_SHIFT (6U)
#define RGX_CR_TEXAS_PERF_CLR_5_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_TEXAS_PERF_CLR_5_EN (0x00000040U)
#define RGX_CR_TEXAS_PERF_CLR_4_SHIFT (5U)
#define RGX_CR_TEXAS_PERF_CLR_4_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_TEXAS_PERF_CLR_4_EN (0x00000020U)
#define RGX_CR_TEXAS_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_TEXAS_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_TEXAS_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_TEXAS_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_TEXAS_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_TEXAS_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_TEXAS_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_TEXAS_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_TEXAS_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_TEXAS_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_TEXAS_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_TEXAS_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_TEXAS_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_TEXAS_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_TEXAS_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_TEXAS_PERF_SELECT0
*/
#define RGX_CR_TEXAS_PERF_SELECT0 (0x8298U)
#define RGX_CR_TEXAS_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF803FFFFF))
#define RGX_CR_TEXAS_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_TEXAS_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_TEXAS_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_TEXAS_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_TEXAS_PERF_SELECT0_MODE_SHIFT (31U)
#define RGX_CR_TEXAS_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFF7FFFFFFF))
#define RGX_CR_TEXAS_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000080000000))
#define RGX_CR_TEXAS_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_TEXAS_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFC0FFFF))
#define RGX_CR_TEXAS_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_TEXAS_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_TEXAS_PERF_COUNTER_0
*/
#define RGX_CR_TEXAS_PERF_COUNTER_0 (0x82D8U)
#define RGX_CR_TEXAS_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_TEXAS_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_TEXAS_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_JONES_PERF
*/
#define RGX_CR_JONES_PERF (0x8330U)
#define RGX_CR_JONES_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_JONES_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_JONES_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_JONES_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_JONES_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_JONES_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_JONES_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_JONES_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_JONES_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_JONES_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_JONES_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_JONES_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_JONES_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_JONES_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_JONES_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_JONES_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_JONES_PERF_SELECT0
*/
#define RGX_CR_JONES_PERF_SELECT0 (0x8338U)
#define RGX_CR_JONES_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_JONES_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_JONES_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_JONES_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_JONES_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_JONES_PERF_SELECT0_MODE_SHIFT (21U)
#define RGX_CR_JONES_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_JONES_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_JONES_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_JONES_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_JONES_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_JONES_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_JONES_PERF_COUNTER_0
*/
#define RGX_CR_JONES_PERF_COUNTER_0 (0x8368U)
#define RGX_CR_JONES_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_JONES_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_JONES_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_BLACKPEARL_PERF
*/
#define RGX_CR_BLACKPEARL_PERF (0x8400U)
#define RGX_CR_BLACKPEARL_PERF_MASKFULL (IMG_UINT64_C(0x000000000000007F))
#define RGX_CR_BLACKPEARL_PERF_CLR_5_SHIFT (6U)
#define RGX_CR_BLACKPEARL_PERF_CLR_5_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_BLACKPEARL_PERF_CLR_5_EN (0x00000040U)
#define RGX_CR_BLACKPEARL_PERF_CLR_4_SHIFT (5U)
#define RGX_CR_BLACKPEARL_PERF_CLR_4_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_BLACKPEARL_PERF_CLR_4_EN (0x00000020U)
#define RGX_CR_BLACKPEARL_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_BLACKPEARL_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_BLACKPEARL_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_BLACKPEARL_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_BLACKPEARL_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_BLACKPEARL_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_BLACKPEARL_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_BLACKPEARL_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_BLACKPEARL_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_BLACKPEARL_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_BLACKPEARL_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_BLACKPEARL_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_BLACKPEARL_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_BLACKPEARL_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_BLACKPEARL_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_BLACKPEARL_PERF_SELECT0
*/
#define RGX_CR_BLACKPEARL_PERF_SELECT0 (0x8408U)
#define RGX_CR_BLACKPEARL_PERF_SELECT0_MASKFULL \
	(IMG_UINT64_C(0x3FFF3FFF803FFFFF))
#define RGX_CR_BLACKPEARL_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_BLACKPEARL_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_BLACKPEARL_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_BLACKPEARL_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_BLACKPEARL_PERF_SELECT0_MODE_SHIFT (31U)
#define RGX_CR_BLACKPEARL_PERF_SELECT0_MODE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF7FFFFFFF))
#define RGX_CR_BLACKPEARL_PERF_SELECT0_MODE_EN \
	(IMG_UINT64_C(0x0000000080000000))
#define RGX_CR_BLACKPEARL_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_BLACKPEARL_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFC0FFFF))
#define RGX_CR_BLACKPEARL_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_BLACKPEARL_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_BLACKPEARL_PERF_COUNTER_0
*/
#define RGX_CR_BLACKPEARL_PERF_COUNTER_0 (0x8448U)
#define RGX_CR_BLACKPEARL_PERF_COUNTER_0_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_BLACKPEARL_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_BLACKPEARL_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_PBE_PERF
*/
#define RGX_CR_PBE_PERF (0x8478U)
#define RGX_CR_PBE_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_PBE_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_PBE_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_PBE_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_PBE_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_PBE_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_PBE_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_PBE_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_PBE_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_PBE_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_PBE_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_PBE_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_PBE_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_PBE_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_PBE_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_PBE_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_PBE_PERF_SELECT0
*/
#define RGX_CR_PBE_PERF_SELECT0 (0x8480U)
#define RGX_CR_PBE_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_PBE_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_PBE_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_PBE_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_PBE_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_PBE_PERF_SELECT0_MODE_SHIFT (21U)
#define RGX_CR_PBE_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_PBE_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_PBE_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_PBE_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_PBE_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_PBE_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_PBE_PERF_COUNTER_0
*/
#define RGX_CR_PBE_PERF_COUNTER_0 (0x84B0U)
#define RGX_CR_PBE_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_PBE_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_PBE_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_OCP_REVINFO
*/
#define RGX_CR_OCP_REVINFO (0x9000U)
#define RGX_CR_OCP_REVINFO_MASKFULL (IMG_UINT64_C(0x00000007FFFFFFFF))
#define RGX_CR_OCP_REVINFO_HWINFO_SYSBUS_SHIFT (33U)
#define RGX_CR_OCP_REVINFO_HWINFO_SYSBUS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFF9FFFFFFFF))
#define RGX_CR_OCP_REVINFO_HWINFO_MEMBUS_SHIFT (32U)
#define RGX_CR_OCP_REVINFO_HWINFO_MEMBUS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFEFFFFFFFF))
#define RGX_CR_OCP_REVINFO_HWINFO_MEMBUS_EN (IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_OCP_REVINFO_REVISION_SHIFT (0U)
#define RGX_CR_OCP_REVINFO_REVISION_CLRMSK (IMG_UINT64_C(0xFFFFFFFF00000000))

/*
    Register RGX_CR_OCP_SYSCONFIG
*/
#define RGX_CR_OCP_SYSCONFIG (0x9010U)
#define RGX_CR_OCP_SYSCONFIG_MASKFULL (IMG_UINT64_C(0x0000000000000FFF))
#define RGX_CR_OCP_SYSCONFIG_DUST2_STANDBY_MODE_SHIFT (10U)
#define RGX_CR_OCP_SYSCONFIG_DUST2_STANDBY_MODE_CLRMSK (0xFFFFF3FFU)
#define RGX_CR_OCP_SYSCONFIG_DUST1_STANDBY_MODE_SHIFT (8U)
#define RGX_CR_OCP_SYSCONFIG_DUST1_STANDBY_MODE_CLRMSK (0xFFFFFCFFU)
#define RGX_CR_OCP_SYSCONFIG_DUST0_STANDBY_MODE_SHIFT (6U)
#define RGX_CR_OCP_SYSCONFIG_DUST0_STANDBY_MODE_CLRMSK (0xFFFFFF3FU)
#define RGX_CR_OCP_SYSCONFIG_RASCAL_STANDBYMODE_SHIFT (4U)
#define RGX_CR_OCP_SYSCONFIG_RASCAL_STANDBYMODE_CLRMSK (0xFFFFFFCFU)
#define RGX_CR_OCP_SYSCONFIG_STANDBY_MODE_SHIFT (2U)
#define RGX_CR_OCP_SYSCONFIG_STANDBY_MODE_CLRMSK (0xFFFFFFF3U)
#define RGX_CR_OCP_SYSCONFIG_IDLE_MODE_SHIFT (0U)
#define RGX_CR_OCP_SYSCONFIG_IDLE_MODE_CLRMSK (0xFFFFFFFCU)

/*
    Register RGX_CR_OCP_IRQSTATUS_RAW_0
*/
#define RGX_CR_OCP_IRQSTATUS_RAW_0 (0x9020U)
#define RGX_CR_OCP_IRQSTATUS_RAW_0_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQSTATUS_RAW_0_INIT_MINTERRUPT_RAW_SHIFT (0U)
#define RGX_CR_OCP_IRQSTATUS_RAW_0_INIT_MINTERRUPT_RAW_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQSTATUS_RAW_0_INIT_MINTERRUPT_RAW_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQSTATUS_RAW_1
*/
#define RGX_CR_OCP_IRQSTATUS_RAW_1 (0x9028U)
#define RGX_CR_OCP_IRQSTATUS_RAW_1_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQSTATUS_RAW_1_TARGET_SINTERRUPT_RAW_SHIFT (0U)
#define RGX_CR_OCP_IRQSTATUS_RAW_1_TARGET_SINTERRUPT_RAW_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQSTATUS_RAW_1_TARGET_SINTERRUPT_RAW_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQSTATUS_RAW_2
*/
#define RGX_CR_OCP_IRQSTATUS_RAW_2 (0x9030U)
#define RGX_CR_OCP_IRQSTATUS_RAW_2_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQSTATUS_RAW_2_RGX_IRQ_RAW_SHIFT (0U)
#define RGX_CR_OCP_IRQSTATUS_RAW_2_RGX_IRQ_RAW_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQSTATUS_RAW_2_RGX_IRQ_RAW_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQSTATUS_0
*/
#define RGX_CR_OCP_IRQSTATUS_0 (0x9038U)
#define RGX_CR_OCP_IRQSTATUS_0_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQSTATUS_0_INIT_MINTERRUPT_STATUS_SHIFT (0U)
#define RGX_CR_OCP_IRQSTATUS_0_INIT_MINTERRUPT_STATUS_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQSTATUS_0_INIT_MINTERRUPT_STATUS_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQSTATUS_1
*/
#define RGX_CR_OCP_IRQSTATUS_1 (0x9040U)
#define RGX_CR_OCP_IRQSTATUS_1_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQSTATUS_1_TARGET_SINTERRUPT_STATUS_SHIFT (0U)
#define RGX_CR_OCP_IRQSTATUS_1_TARGET_SINTERRUPT_STATUS_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQSTATUS_1_TARGET_SINTERRUPT_STATUS_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQSTATUS_2
*/
#define RGX_CR_OCP_IRQSTATUS_2 (0x9048U)
#define RGX_CR_OCP_IRQSTATUS_2_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQSTATUS_2_RGX_IRQ_STATUS_SHIFT (0U)
#define RGX_CR_OCP_IRQSTATUS_2_RGX_IRQ_STATUS_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQSTATUS_2_RGX_IRQ_STATUS_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQENABLE_SET_0
*/
#define RGX_CR_OCP_IRQENABLE_SET_0 (0x9050U)
#define RGX_CR_OCP_IRQENABLE_SET_0_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQENABLE_SET_0_INIT_MINTERRUPT_ENABLE_SHIFT (0U)
#define RGX_CR_OCP_IRQENABLE_SET_0_INIT_MINTERRUPT_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQENABLE_SET_0_INIT_MINTERRUPT_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQENABLE_SET_1
*/
#define RGX_CR_OCP_IRQENABLE_SET_1 (0x9058U)
#define RGX_CR_OCP_IRQENABLE_SET_1_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQENABLE_SET_1_TARGET_SINTERRUPT_ENABLE_SHIFT (0U)
#define RGX_CR_OCP_IRQENABLE_SET_1_TARGET_SINTERRUPT_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQENABLE_SET_1_TARGET_SINTERRUPT_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQENABLE_SET_2
*/
#define RGX_CR_OCP_IRQENABLE_SET_2 (0x9060U)
#define RGX_CR_OCP_IRQENABLE_SET_2_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQENABLE_SET_2_RGX_IRQ_ENABLE_SHIFT (0U)
#define RGX_CR_OCP_IRQENABLE_SET_2_RGX_IRQ_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQENABLE_SET_2_RGX_IRQ_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQENABLE_CLR_0
*/
#define RGX_CR_OCP_IRQENABLE_CLR_0 (0x9068U)
#define RGX_CR_OCP_IRQENABLE_CLR_0_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQENABLE_CLR_0_INIT_MINTERRUPT_DISABLE_SHIFT (0U)
#define RGX_CR_OCP_IRQENABLE_CLR_0_INIT_MINTERRUPT_DISABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQENABLE_CLR_0_INIT_MINTERRUPT_DISABLE_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQENABLE_CLR_1
*/
#define RGX_CR_OCP_IRQENABLE_CLR_1 (0x9070U)
#define RGX_CR_OCP_IRQENABLE_CLR_1_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQENABLE_CLR_1_TARGET_SINTERRUPT_DISABLE_SHIFT (0U)
#define RGX_CR_OCP_IRQENABLE_CLR_1_TARGET_SINTERRUPT_DISABLE_CLRMSK \
	(0xFFFFFFFEU)
#define RGX_CR_OCP_IRQENABLE_CLR_1_TARGET_SINTERRUPT_DISABLE_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQENABLE_CLR_2
*/
#define RGX_CR_OCP_IRQENABLE_CLR_2 (0x9078U)
#define RGX_CR_OCP_IRQENABLE_CLR_2_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_IRQENABLE_CLR_2_RGX_IRQ_DISABLE_SHIFT (0U)
#define RGX_CR_OCP_IRQENABLE_CLR_2_RGX_IRQ_DISABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_IRQENABLE_CLR_2_RGX_IRQ_DISABLE_EN (0x00000001U)

/*
    Register RGX_CR_OCP_IRQ_EVENT
*/
#define RGX_CR_OCP_IRQ_EVENT (0x9080U)
#define RGX_CR_OCP_IRQ_EVENT_MASKFULL (IMG_UINT64_C(0x00000000000FFFFF))
#define RGX_CR_OCP_IRQ_EVENT_TARGETH_RCVD_UNEXPECTED_RDATA_SHIFT (19U)
#define RGX_CR_OCP_IRQ_EVENT_TARGETH_RCVD_UNEXPECTED_RDATA_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFF7FFFF))
#define RGX_CR_OCP_IRQ_EVENT_TARGETH_RCVD_UNEXPECTED_RDATA_EN \
	(IMG_UINT64_C(0x0000000000080000))
#define RGX_CR_OCP_IRQ_EVENT_TARGETH_RCVD_UNSUPPORTED_MCMD_SHIFT (18U)
#define RGX_CR_OCP_IRQ_EVENT_TARGETH_RCVD_UNSUPPORTED_MCMD_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFBFFFF))
#define RGX_CR_OCP_IRQ_EVENT_TARGETH_RCVD_UNSUPPORTED_MCMD_EN \
	(IMG_UINT64_C(0x0000000000040000))
#define RGX_CR_OCP_IRQ_EVENT_TARGETS_RCVD_UNEXPECTED_RDATA_SHIFT (17U)
#define RGX_CR_OCP_IRQ_EVENT_TARGETS_RCVD_UNEXPECTED_RDATA_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFDFFFF))
#define RGX_CR_OCP_IRQ_EVENT_TARGETS_RCVD_UNEXPECTED_RDATA_EN \
	(IMG_UINT64_C(0x0000000000020000))
#define RGX_CR_OCP_IRQ_EVENT_TARGETS_RCVD_UNSUPPORTED_MCMD_SHIFT (16U)
#define RGX_CR_OCP_IRQ_EVENT_TARGETS_RCVD_UNSUPPORTED_MCMD_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFEFFFF))
#define RGX_CR_OCP_IRQ_EVENT_TARGETS_RCVD_UNSUPPORTED_MCMD_EN \
	(IMG_UINT64_C(0x0000000000010000))
#define RGX_CR_OCP_IRQ_EVENT_INIT3_IMG_PAGE_BOUNDARY_CROSS_SHIFT (15U)
#define RGX_CR_OCP_IRQ_EVENT_INIT3_IMG_PAGE_BOUNDARY_CROSS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF7FFF))
#define RGX_CR_OCP_IRQ_EVENT_INIT3_IMG_PAGE_BOUNDARY_CROSS_EN \
	(IMG_UINT64_C(0x0000000000008000))
#define RGX_CR_OCP_IRQ_EVENT_INIT3_RCVD_RESP_ERR_FAIL_SHIFT (14U)
#define RGX_CR_OCP_IRQ_EVENT_INIT3_RCVD_RESP_ERR_FAIL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFBFFF))
#define RGX_CR_OCP_IRQ_EVENT_INIT3_RCVD_RESP_ERR_FAIL_EN \
	(IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_OCP_IRQ_EVENT_INIT3_RCVD_UNUSED_TAGID_SHIFT (13U)
#define RGX_CR_OCP_IRQ_EVENT_INIT3_RCVD_UNUSED_TAGID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFDFFF))
#define RGX_CR_OCP_IRQ_EVENT_INIT3_RCVD_UNUSED_TAGID_EN \
	(IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_OCP_IRQ_EVENT_INIT3_RDATA_FIFO_OVERFILL_SHIFT (12U)
#define RGX_CR_OCP_IRQ_EVENT_INIT3_RDATA_FIFO_OVERFILL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFEFFF))
#define RGX_CR_OCP_IRQ_EVENT_INIT3_RDATA_FIFO_OVERFILL_EN \
	(IMG_UINT64_C(0x0000000000001000))
#define RGX_CR_OCP_IRQ_EVENT_INIT2_IMG_PAGE_BOUNDARY_CROSS_SHIFT (11U)
#define RGX_CR_OCP_IRQ_EVENT_INIT2_IMG_PAGE_BOUNDARY_CROSS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF7FF))
#define RGX_CR_OCP_IRQ_EVENT_INIT2_IMG_PAGE_BOUNDARY_CROSS_EN \
	(IMG_UINT64_C(0x0000000000000800))
#define RGX_CR_OCP_IRQ_EVENT_INIT2_RCVD_RESP_ERR_FAIL_SHIFT (10U)
#define RGX_CR_OCP_IRQ_EVENT_INIT2_RCVD_RESP_ERR_FAIL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFBFF))
#define RGX_CR_OCP_IRQ_EVENT_INIT2_RCVD_RESP_ERR_FAIL_EN \
	(IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_OCP_IRQ_EVENT_INIT2_RCVD_UNUSED_TAGID_SHIFT (9U)
#define RGX_CR_OCP_IRQ_EVENT_INIT2_RCVD_UNUSED_TAGID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFDFF))
#define RGX_CR_OCP_IRQ_EVENT_INIT2_RCVD_UNUSED_TAGID_EN \
	(IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_OCP_IRQ_EVENT_INIT2_RDATA_FIFO_OVERFILL_SHIFT (8U)
#define RGX_CR_OCP_IRQ_EVENT_INIT2_RDATA_FIFO_OVERFILL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFEFF))
#define RGX_CR_OCP_IRQ_EVENT_INIT2_RDATA_FIFO_OVERFILL_EN \
	(IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_OCP_IRQ_EVENT_INIT1_IMG_PAGE_BOUNDARY_CROSS_SHIFT (7U)
#define RGX_CR_OCP_IRQ_EVENT_INIT1_IMG_PAGE_BOUNDARY_CROSS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFF7F))
#define RGX_CR_OCP_IRQ_EVENT_INIT1_IMG_PAGE_BOUNDARY_CROSS_EN \
	(IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_OCP_IRQ_EVENT_INIT1_RCVD_RESP_ERR_FAIL_SHIFT (6U)
#define RGX_CR_OCP_IRQ_EVENT_INIT1_RCVD_RESP_ERR_FAIL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFBF))
#define RGX_CR_OCP_IRQ_EVENT_INIT1_RCVD_RESP_ERR_FAIL_EN \
	(IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_OCP_IRQ_EVENT_INIT1_RCVD_UNUSED_TAGID_SHIFT (5U)
#define RGX_CR_OCP_IRQ_EVENT_INIT1_RCVD_UNUSED_TAGID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_OCP_IRQ_EVENT_INIT1_RCVD_UNUSED_TAGID_EN \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_OCP_IRQ_EVENT_INIT1_RDATA_FIFO_OVERFILL_SHIFT (4U)
#define RGX_CR_OCP_IRQ_EVENT_INIT1_RDATA_FIFO_OVERFILL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFEF))
#define RGX_CR_OCP_IRQ_EVENT_INIT1_RDATA_FIFO_OVERFILL_EN \
	(IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_OCP_IRQ_EVENT_INIT0_IMG_PAGE_BOUNDARY_CROSS_SHIFT (3U)
#define RGX_CR_OCP_IRQ_EVENT_INIT0_IMG_PAGE_BOUNDARY_CROSS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFF7))
#define RGX_CR_OCP_IRQ_EVENT_INIT0_IMG_PAGE_BOUNDARY_CROSS_EN \
	(IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_OCP_IRQ_EVENT_INIT0_RCVD_RESP_ERR_FAIL_SHIFT (2U)
#define RGX_CR_OCP_IRQ_EVENT_INIT0_RCVD_RESP_ERR_FAIL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_CR_OCP_IRQ_EVENT_INIT0_RCVD_RESP_ERR_FAIL_EN \
	(IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_OCP_IRQ_EVENT_INIT0_RCVD_UNUSED_TAGID_SHIFT (1U)
#define RGX_CR_OCP_IRQ_EVENT_INIT0_RCVD_UNUSED_TAGID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_CR_OCP_IRQ_EVENT_INIT0_RCVD_UNUSED_TAGID_EN \
	(IMG_UINT64_C(0x0000000000000002))
#define RGX_CR_OCP_IRQ_EVENT_INIT0_RDATA_FIFO_OVERFILL_SHIFT (0U)
#define RGX_CR_OCP_IRQ_EVENT_INIT0_RDATA_FIFO_OVERFILL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_OCP_IRQ_EVENT_INIT0_RDATA_FIFO_OVERFILL_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_OCP_DEBUG_CONFIG
*/
#define RGX_CR_OCP_DEBUG_CONFIG (0x9088U)
#define RGX_CR_OCP_DEBUG_CONFIG_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_OCP_DEBUG_CONFIG_REG_SHIFT (0U)
#define RGX_CR_OCP_DEBUG_CONFIG_REG_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_OCP_DEBUG_CONFIG_REG_EN (0x00000001U)

/*
    Register RGX_CR_OCP_DEBUG_STATUS
*/
#define RGX_CR_OCP_DEBUG_STATUS (0x9090U)
#define RGX_CR_OCP_DEBUG_STATUS_MASKFULL (IMG_UINT64_C(0x001F1F77FFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_SDISCACK_SHIFT (51U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_SDISCACK_CLRMSK \
	(IMG_UINT64_C(0xFFE7FFFFFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_SCONNECT_SHIFT (50U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_SCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFBFFFFFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_SCONNECT_EN \
	(IMG_UINT64_C(0x0004000000000000))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_MCONNECT_SHIFT (48U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_MCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFCFFFFFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_SDISCACK_SHIFT (43U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_SDISCACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFE7FFFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_SCONNECT_SHIFT (42U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_SCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFBFFFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_SCONNECT_EN \
	(IMG_UINT64_C(0x0000040000000000))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_MCONNECT_SHIFT (40U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_MCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFCFFFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_BUSY_SHIFT (38U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_BUSY_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFBFFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_BUSY_EN \
	(IMG_UINT64_C(0x0000004000000000))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_CMD_FIFO_FULL_SHIFT (37U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_CMD_FIFO_FULL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFDFFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_CMD_FIFO_FULL_EN \
	(IMG_UINT64_C(0x0000002000000000))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_SRESP_ERROR_SHIFT (36U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_SRESP_ERROR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFEFFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETH_SRESP_ERROR_EN \
	(IMG_UINT64_C(0x0000001000000000))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_BUSY_SHIFT (34U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_BUSY_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFBFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_BUSY_EN \
	(IMG_UINT64_C(0x0000000400000000))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_CMD_FIFO_FULL_SHIFT (33U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_CMD_FIFO_FULL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFDFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_CMD_FIFO_FULL_EN \
	(IMG_UINT64_C(0x0000000200000000))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_SRESP_ERROR_SHIFT (32U)
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_SRESP_ERROR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFEFFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_TARGETS_SRESP_ERROR_EN \
	(IMG_UINT64_C(0x0000000100000000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_RESERVED_SHIFT (31U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_RESERVED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF7FFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_RESERVED_EN \
	(IMG_UINT64_C(0x0000000080000000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_SWAIT_SHIFT (30U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_SWAIT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFBFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_SWAIT_EN \
	(IMG_UINT64_C(0x0000000040000000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_MDISCREQ_SHIFT (29U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_MDISCREQ_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFDFFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_MDISCREQ_EN \
	(IMG_UINT64_C(0x0000000020000000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_MDISCACK_SHIFT (27U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_MDISCACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFE7FFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_SCONNECT_SHIFT (26U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_SCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFBFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_SCONNECT_EN \
	(IMG_UINT64_C(0x0000000004000000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_MCONNECT_SHIFT (24U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT3_MCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFCFFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_RESERVED_SHIFT (23U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_RESERVED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFF7FFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_RESERVED_EN \
	(IMG_UINT64_C(0x0000000000800000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_SWAIT_SHIFT (22U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_SWAIT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFBFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_SWAIT_EN \
	(IMG_UINT64_C(0x0000000000400000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_MDISCREQ_SHIFT (21U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_MDISCREQ_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_MDISCREQ_EN \
	(IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_MDISCACK_SHIFT (19U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_MDISCACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE7FFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_SCONNECT_SHIFT (18U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_SCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFBFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_SCONNECT_EN \
	(IMG_UINT64_C(0x0000000000040000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_MCONNECT_SHIFT (16U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT2_MCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFCFFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_RESERVED_SHIFT (15U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_RESERVED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF7FFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_RESERVED_EN \
	(IMG_UINT64_C(0x0000000000008000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_SWAIT_SHIFT (14U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_SWAIT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFBFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_SWAIT_EN \
	(IMG_UINT64_C(0x0000000000004000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_MDISCREQ_SHIFT (13U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_MDISCREQ_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFDFFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_MDISCREQ_EN \
	(IMG_UINT64_C(0x0000000000002000))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_MDISCACK_SHIFT (11U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_MDISCACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFE7FF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_SCONNECT_SHIFT (10U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_SCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFBFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_SCONNECT_EN \
	(IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_MCONNECT_SHIFT (8U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT1_MCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFCFF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_RESERVED_SHIFT (7U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_RESERVED_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFF7F))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_RESERVED_EN \
	(IMG_UINT64_C(0x0000000000000080))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_SWAIT_SHIFT (6U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_SWAIT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFBF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_SWAIT_EN \
	(IMG_UINT64_C(0x0000000000000040))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_MDISCREQ_SHIFT (5U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_MDISCREQ_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_MDISCREQ_EN \
	(IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_MDISCACK_SHIFT (3U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_MDISCACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFE7))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_SCONNECT_SHIFT (2U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_SCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_SCONNECT_EN \
	(IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_MCONNECT_SHIFT (0U)
#define RGX_CR_OCP_DEBUG_STATUS_INIT0_MCONNECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFC))

#define RGX_CR_BIF_TRUST_DM_TYPE_PM_ALIST_SHIFT (6U)
#define RGX_CR_BIF_TRUST_DM_TYPE_PM_ALIST_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_BIF_TRUST_DM_TYPE_PM_ALIST_EN (0x00000040U)
#define RGX_CR_BIF_TRUST_DM_TYPE_HOST_SHIFT (5U)
#define RGX_CR_BIF_TRUST_DM_TYPE_HOST_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_BIF_TRUST_DM_TYPE_HOST_EN (0x00000020U)
#define RGX_CR_BIF_TRUST_DM_TYPE_META_SHIFT (4U)
#define RGX_CR_BIF_TRUST_DM_TYPE_META_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_BIF_TRUST_DM_TYPE_META_EN (0x00000010U)
#define RGX_CR_BIF_TRUST_DM_TYPE_PB_ZLS_SHIFT (3U)
#define RGX_CR_BIF_TRUST_DM_TYPE_PB_ZLS_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_BIF_TRUST_DM_TYPE_PB_ZLS_EN (0x00000008U)
#define RGX_CR_BIF_TRUST_DM_TYPE_PB_TE_SHIFT (2U)
#define RGX_CR_BIF_TRUST_DM_TYPE_PB_TE_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_BIF_TRUST_DM_TYPE_PB_TE_EN (0x00000004U)
#define RGX_CR_BIF_TRUST_DM_TYPE_PB_VCE_SHIFT (1U)
#define RGX_CR_BIF_TRUST_DM_TYPE_PB_VCE_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_BIF_TRUST_DM_TYPE_PB_VCE_EN (0x00000002U)
#define RGX_CR_BIF_TRUST_DM_TYPE_TLA_SHIFT (0U)
#define RGX_CR_BIF_TRUST_DM_TYPE_TLA_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_BIF_TRUST_DM_TYPE_TLA_EN (0x00000001U)

#define RGX_CR_BIF_TRUST_DM_MASK (0x0000007FU)

/*
    Register RGX_CR_BIF_TRUST
*/
#define RGX_CR_BIF_TRUST (0xA000U)
#define RGX_CR_BIF_TRUST_MASKFULL (IMG_UINT64_C(0x00000000001FFFFF))
#define RGX_CR_BIF_TRUST_OTHER_RAY_VERTEX_DM_TRUSTED_SHIFT (20U)
#define RGX_CR_BIF_TRUST_OTHER_RAY_VERTEX_DM_TRUSTED_CLRMSK (0xFFEFFFFFU)
#define RGX_CR_BIF_TRUST_OTHER_RAY_VERTEX_DM_TRUSTED_EN (0x00100000U)
#define RGX_CR_BIF_TRUST_MCU_RAY_VERTEX_DM_TRUSTED_SHIFT (19U)
#define RGX_CR_BIF_TRUST_MCU_RAY_VERTEX_DM_TRUSTED_CLRMSK (0xFFF7FFFFU)
#define RGX_CR_BIF_TRUST_MCU_RAY_VERTEX_DM_TRUSTED_EN (0x00080000U)
#define RGX_CR_BIF_TRUST_OTHER_RAY_DM_TRUSTED_SHIFT (18U)
#define RGX_CR_BIF_TRUST_OTHER_RAY_DM_TRUSTED_CLRMSK (0xFFFBFFFFU)
#define RGX_CR_BIF_TRUST_OTHER_RAY_DM_TRUSTED_EN (0x00040000U)
#define RGX_CR_BIF_TRUST_MCU_RAY_DM_TRUSTED_SHIFT (17U)
#define RGX_CR_BIF_TRUST_MCU_RAY_DM_TRUSTED_CLRMSK (0xFFFDFFFFU)
#define RGX_CR_BIF_TRUST_MCU_RAY_DM_TRUSTED_EN (0x00020000U)
#define RGX_CR_BIF_TRUST_ENABLE_SHIFT (16U)
#define RGX_CR_BIF_TRUST_ENABLE_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_BIF_TRUST_ENABLE_EN (0x00010000U)
#define RGX_CR_BIF_TRUST_DM_TRUSTED_SHIFT (9U)
#define RGX_CR_BIF_TRUST_DM_TRUSTED_CLRMSK (0xFFFF01FFU)
#define RGX_CR_BIF_TRUST_OTHER_COMPUTE_DM_TRUSTED_SHIFT (8U)
#define RGX_CR_BIF_TRUST_OTHER_COMPUTE_DM_TRUSTED_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_BIF_TRUST_OTHER_COMPUTE_DM_TRUSTED_EN (0x00000100U)
#define RGX_CR_BIF_TRUST_MCU_COMPUTE_DM_TRUSTED_SHIFT (7U)
#define RGX_CR_BIF_TRUST_MCU_COMPUTE_DM_TRUSTED_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_BIF_TRUST_MCU_COMPUTE_DM_TRUSTED_EN (0x00000080U)
#define RGX_CR_BIF_TRUST_PBE_COMPUTE_DM_TRUSTED_SHIFT (6U)
#define RGX_CR_BIF_TRUST_PBE_COMPUTE_DM_TRUSTED_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_BIF_TRUST_PBE_COMPUTE_DM_TRUSTED_EN (0x00000040U)
#define RGX_CR_BIF_TRUST_OTHER_PIXEL_DM_TRUSTED_SHIFT (5U)
#define RGX_CR_BIF_TRUST_OTHER_PIXEL_DM_TRUSTED_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_BIF_TRUST_OTHER_PIXEL_DM_TRUSTED_EN (0x00000020U)
#define RGX_CR_BIF_TRUST_MCU_PIXEL_DM_TRUSTED_SHIFT (4U)
#define RGX_CR_BIF_TRUST_MCU_PIXEL_DM_TRUSTED_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_BIF_TRUST_MCU_PIXEL_DM_TRUSTED_EN (0x00000010U)
#define RGX_CR_BIF_TRUST_PBE_PIXEL_DM_TRUSTED_SHIFT (3U)
#define RGX_CR_BIF_TRUST_PBE_PIXEL_DM_TRUSTED_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_BIF_TRUST_PBE_PIXEL_DM_TRUSTED_EN (0x00000008U)
#define RGX_CR_BIF_TRUST_OTHER_VERTEX_DM_TRUSTED_SHIFT (2U)
#define RGX_CR_BIF_TRUST_OTHER_VERTEX_DM_TRUSTED_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_BIF_TRUST_OTHER_VERTEX_DM_TRUSTED_EN (0x00000004U)
#define RGX_CR_BIF_TRUST_MCU_VERTEX_DM_TRUSTED_SHIFT (1U)
#define RGX_CR_BIF_TRUST_MCU_VERTEX_DM_TRUSTED_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_BIF_TRUST_MCU_VERTEX_DM_TRUSTED_EN (0x00000002U)
#define RGX_CR_BIF_TRUST_PBE_VERTEX_DM_TRUSTED_SHIFT (0U)
#define RGX_CR_BIF_TRUST_PBE_VERTEX_DM_TRUSTED_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_BIF_TRUST_PBE_VERTEX_DM_TRUSTED_EN (0x00000001U)

/*
    Register RGX_CR_SYS_BUS_SECURE
*/
#define RGX_CR_SYS_BUS_SECURE (0xA100U)
#define RGX_CR_SYS_BUS_SECURE__SECR__MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_SYS_BUS_SECURE_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_SYS_BUS_SECURE_ENABLE_SHIFT (0U)
#define RGX_CR_SYS_BUS_SECURE_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SYS_BUS_SECURE_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_FBA_FC0_CHECKSUM
*/
#define RGX_CR_FBA_FC0_CHECKSUM (0xD170U)
#define RGX_CR_FBA_FC0_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FBA_FC0_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_FBA_FC0_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_FBA_FC1_CHECKSUM
*/
#define RGX_CR_FBA_FC1_CHECKSUM (0xD178U)
#define RGX_CR_FBA_FC1_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FBA_FC1_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_FBA_FC1_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_FBA_FC2_CHECKSUM
*/
#define RGX_CR_FBA_FC2_CHECKSUM (0xD180U)
#define RGX_CR_FBA_FC2_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FBA_FC2_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_FBA_FC2_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_FBA_FC3_CHECKSUM
*/
#define RGX_CR_FBA_FC3_CHECKSUM (0xD188U)
#define RGX_CR_FBA_FC3_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_FBA_FC3_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_FBA_FC3_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_CLK_CTRL2
*/
#define RGX_CR_CLK_CTRL2 (0xD200U)
#define RGX_CR_CLK_CTRL2_MASKFULL (IMG_UINT64_C(0x0000000000000F33))
#define RGX_CR_CLK_CTRL2_MCU_FBTC_SHIFT (10U)
#define RGX_CR_CLK_CTRL2_MCU_FBTC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFF3FF))
#define RGX_CR_CLK_CTRL2_MCU_FBTC_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL2_MCU_FBTC_ON (IMG_UINT64_C(0x0000000000000400))
#define RGX_CR_CLK_CTRL2_MCU_FBTC_AUTO (IMG_UINT64_C(0x0000000000000800))
#define RGX_CR_CLK_CTRL2_VRDM_SHIFT (8U)
#define RGX_CR_CLK_CTRL2_VRDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFCFF))
#define RGX_CR_CLK_CTRL2_VRDM_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL2_VRDM_ON (IMG_UINT64_C(0x0000000000000100))
#define RGX_CR_CLK_CTRL2_VRDM_AUTO (IMG_UINT64_C(0x0000000000000200))
#define RGX_CR_CLK_CTRL2_SH_SHIFT (4U)
#define RGX_CR_CLK_CTRL2_SH_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFCF))
#define RGX_CR_CLK_CTRL2_SH_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL2_SH_ON (IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_CLK_CTRL2_SH_AUTO (IMG_UINT64_C(0x0000000000000020))
#define RGX_CR_CLK_CTRL2_FBA_SHIFT (0U)
#define RGX_CR_CLK_CTRL2_FBA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFC))
#define RGX_CR_CLK_CTRL2_FBA_OFF (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_CTRL2_FBA_ON (IMG_UINT64_C(0x0000000000000001))
#define RGX_CR_CLK_CTRL2_FBA_AUTO (IMG_UINT64_C(0x0000000000000002))

/*
    Register RGX_CR_CLK_STATUS2
*/
#define RGX_CR_CLK_STATUS2 (0xD208U)
#define RGX_CR_CLK_STATUS2_MASKFULL (IMG_UINT64_C(0x0000000000000015))
#define RGX_CR_CLK_STATUS2_VRDM_SHIFT (4U)
#define RGX_CR_CLK_STATUS2_VRDM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFEF))
#define RGX_CR_CLK_STATUS2_VRDM_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS2_VRDM_RUNNING (IMG_UINT64_C(0x0000000000000010))
#define RGX_CR_CLK_STATUS2_SH_SHIFT (2U)
#define RGX_CR_CLK_STATUS2_SH_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_CR_CLK_STATUS2_SH_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS2_SH_RUNNING (IMG_UINT64_C(0x0000000000000004))
#define RGX_CR_CLK_STATUS2_FBA_SHIFT (0U)
#define RGX_CR_CLK_STATUS2_FBA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_CLK_STATUS2_FBA_GATED (IMG_UINT64_C(0x0000000000000000))
#define RGX_CR_CLK_STATUS2_FBA_RUNNING (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_RPM_SHF_FPL
*/
#define RGX_CR_RPM_SHF_FPL (0xD520U)
#define RGX_CR_RPM_SHF_FPL_MASKFULL (IMG_UINT64_C(0x3FFFFFFFFFFFFFFC))
#define RGX_CR_RPM_SHF_FPL_SIZE_SHIFT (40U)
#define RGX_CR_RPM_SHF_FPL_SIZE_CLRMSK (IMG_UINT64_C(0xC00000FFFFFFFFFF))
#define RGX_CR_RPM_SHF_FPL_BASE_SHIFT (2U)
#define RGX_CR_RPM_SHF_FPL_BASE_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000003))
#define RGX_CR_RPM_SHF_FPL_BASE_ALIGNSHIFT (2U)
#define RGX_CR_RPM_SHF_FPL_BASE_ALIGNSIZE (4U)

/*
    Register RGX_CR_RPM_SHF_FPL_READ
*/
#define RGX_CR_RPM_SHF_FPL_READ (0xD528U)
#define RGX_CR_RPM_SHF_FPL_READ_MASKFULL (IMG_UINT64_C(0x00000000007FFFFF))
#define RGX_CR_RPM_SHF_FPL_READ_TOGGLE_SHIFT (22U)
#define RGX_CR_RPM_SHF_FPL_READ_TOGGLE_CLRMSK (0xFFBFFFFFU)
#define RGX_CR_RPM_SHF_FPL_READ_TOGGLE_EN (0x00400000U)
#define RGX_CR_RPM_SHF_FPL_READ_OFFSET_SHIFT (0U)
#define RGX_CR_RPM_SHF_FPL_READ_OFFSET_CLRMSK (0xFFC00000U)

/*
    Register RGX_CR_RPM_SHF_FPL_WRITE
*/
#define RGX_CR_RPM_SHF_FPL_WRITE (0xD530U)
#define RGX_CR_RPM_SHF_FPL_WRITE_MASKFULL (IMG_UINT64_C(0x00000000007FFFFF))
#define RGX_CR_RPM_SHF_FPL_WRITE_TOGGLE_SHIFT (22U)
#define RGX_CR_RPM_SHF_FPL_WRITE_TOGGLE_CLRMSK (0xFFBFFFFFU)
#define RGX_CR_RPM_SHF_FPL_WRITE_TOGGLE_EN (0x00400000U)
#define RGX_CR_RPM_SHF_FPL_WRITE_OFFSET_SHIFT (0U)
#define RGX_CR_RPM_SHF_FPL_WRITE_OFFSET_CLRMSK (0xFFC00000U)

/*
    Register RGX_CR_RPM_SHG_FPL
*/
#define RGX_CR_RPM_SHG_FPL (0xD538U)
#define RGX_CR_RPM_SHG_FPL_MASKFULL (IMG_UINT64_C(0x3FFFFFFFFFFFFFFC))
#define RGX_CR_RPM_SHG_FPL_SIZE_SHIFT (40U)
#define RGX_CR_RPM_SHG_FPL_SIZE_CLRMSK (IMG_UINT64_C(0xC00000FFFFFFFFFF))
#define RGX_CR_RPM_SHG_FPL_BASE_SHIFT (2U)
#define RGX_CR_RPM_SHG_FPL_BASE_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000003))
#define RGX_CR_RPM_SHG_FPL_BASE_ALIGNSHIFT (2U)
#define RGX_CR_RPM_SHG_FPL_BASE_ALIGNSIZE (4U)

/*
    Register RGX_CR_RPM_SHG_FPL_READ
*/
#define RGX_CR_RPM_SHG_FPL_READ (0xD540U)
#define RGX_CR_RPM_SHG_FPL_READ_MASKFULL (IMG_UINT64_C(0x00000000007FFFFF))
#define RGX_CR_RPM_SHG_FPL_READ_TOGGLE_SHIFT (22U)
#define RGX_CR_RPM_SHG_FPL_READ_TOGGLE_CLRMSK (0xFFBFFFFFU)
#define RGX_CR_RPM_SHG_FPL_READ_TOGGLE_EN (0x00400000U)
#define RGX_CR_RPM_SHG_FPL_READ_OFFSET_SHIFT (0U)
#define RGX_CR_RPM_SHG_FPL_READ_OFFSET_CLRMSK (0xFFC00000U)

/*
    Register RGX_CR_RPM_SHG_FPL_WRITE
*/
#define RGX_CR_RPM_SHG_FPL_WRITE (0xD548U)
#define RGX_CR_RPM_SHG_FPL_WRITE_MASKFULL (IMG_UINT64_C(0x00000000007FFFFF))
#define RGX_CR_RPM_SHG_FPL_WRITE_TOGGLE_SHIFT (22U)
#define RGX_CR_RPM_SHG_FPL_WRITE_TOGGLE_CLRMSK (0xFFBFFFFFU)
#define RGX_CR_RPM_SHG_FPL_WRITE_TOGGLE_EN (0x00400000U)
#define RGX_CR_RPM_SHG_FPL_WRITE_OFFSET_SHIFT (0U)
#define RGX_CR_RPM_SHG_FPL_WRITE_OFFSET_CLRMSK (0xFFC00000U)

/*
    Register RGX_CR_SH_PERF
*/
#define RGX_CR_SH_PERF (0xD5F8U)
#define RGX_CR_SH_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_SH_PERF_CLR_3_SHIFT (4U)
#define RGX_CR_SH_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_SH_PERF_CLR_3_EN (0x00000010U)
#define RGX_CR_SH_PERF_CLR_2_SHIFT (3U)
#define RGX_CR_SH_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_SH_PERF_CLR_2_EN (0x00000008U)
#define RGX_CR_SH_PERF_CLR_1_SHIFT (2U)
#define RGX_CR_SH_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_SH_PERF_CLR_1_EN (0x00000004U)
#define RGX_CR_SH_PERF_CLR_0_SHIFT (1U)
#define RGX_CR_SH_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SH_PERF_CLR_0_EN (0x00000002U)
#define RGX_CR_SH_PERF_CTRL_ENABLE_SHIFT (0U)
#define RGX_CR_SH_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SH_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_CR_SH_PERF_SELECT0
*/
#define RGX_CR_SH_PERF_SELECT0 (0xD600U)
#define RGX_CR_SH_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define RGX_CR_SH_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define RGX_CR_SH_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_CR_SH_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define RGX_CR_SH_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_CR_SH_PERF_SELECT0_MODE_SHIFT (21U)
#define RGX_CR_SH_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define RGX_CR_SH_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define RGX_CR_SH_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define RGX_CR_SH_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define RGX_CR_SH_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define RGX_CR_SH_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_SH_PERF_COUNTER_0
*/
#define RGX_CR_SH_PERF_COUNTER_0 (0xD628U)
#define RGX_CR_SH_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SH_PERF_COUNTER_0_REG_SHIFT (0U)
#define RGX_CR_SH_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SHF_SHG_CHECKSUM
*/
#define RGX_CR_SHF_SHG_CHECKSUM (0xD1C0U)
#define RGX_CR_SHF_SHG_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SHF_SHG_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_SHF_SHG_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SHF_VERTEX_BIF_CHECKSUM
*/
#define RGX_CR_SHF_VERTEX_BIF_CHECKSUM (0xD1C8U)
#define RGX_CR_SHF_VERTEX_BIF_CHECKSUM_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SHF_VERTEX_BIF_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_SHF_VERTEX_BIF_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SHF_VARY_BIF_CHECKSUM
*/
#define RGX_CR_SHF_VARY_BIF_CHECKSUM (0xD1D0U)
#define RGX_CR_SHF_VARY_BIF_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SHF_VARY_BIF_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_SHF_VARY_BIF_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_RPM_BIF_CHECKSUM
*/
#define RGX_CR_RPM_BIF_CHECKSUM (0xD1D8U)
#define RGX_CR_RPM_BIF_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_RPM_BIF_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_RPM_BIF_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SHG_BIF_CHECKSUM
*/
#define RGX_CR_SHG_BIF_CHECKSUM (0xD1E0U)
#define RGX_CR_SHG_BIF_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SHG_BIF_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_SHG_BIF_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register RGX_CR_SHG_FE_BE_CHECKSUM
*/
#define RGX_CR_SHG_FE_BE_CHECKSUM (0xD1E8U)
#define RGX_CR_SHG_FE_BE_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_SHG_FE_BE_CHECKSUM_VALUE_SHIFT (0U)
#define RGX_CR_SHG_FE_BE_CHECKSUM_VALUE_CLRMSK (0x00000000U)

/*
    Register DPX_CR_BF_PERF
*/
#define DPX_CR_BF_PERF (0xC458U)
#define DPX_CR_BF_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define DPX_CR_BF_PERF_CLR_3_SHIFT (4U)
#define DPX_CR_BF_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define DPX_CR_BF_PERF_CLR_3_EN (0x00000010U)
#define DPX_CR_BF_PERF_CLR_2_SHIFT (3U)
#define DPX_CR_BF_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define DPX_CR_BF_PERF_CLR_2_EN (0x00000008U)
#define DPX_CR_BF_PERF_CLR_1_SHIFT (2U)
#define DPX_CR_BF_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define DPX_CR_BF_PERF_CLR_1_EN (0x00000004U)
#define DPX_CR_BF_PERF_CLR_0_SHIFT (1U)
#define DPX_CR_BF_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define DPX_CR_BF_PERF_CLR_0_EN (0x00000002U)
#define DPX_CR_BF_PERF_CTRL_ENABLE_SHIFT (0U)
#define DPX_CR_BF_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define DPX_CR_BF_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register DPX_CR_BF_PERF_SELECT0
*/
#define DPX_CR_BF_PERF_SELECT0 (0xC460U)
#define DPX_CR_BF_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define DPX_CR_BF_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define DPX_CR_BF_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define DPX_CR_BF_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define DPX_CR_BF_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define DPX_CR_BF_PERF_SELECT0_MODE_SHIFT (21U)
#define DPX_CR_BF_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define DPX_CR_BF_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define DPX_CR_BF_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define DPX_CR_BF_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define DPX_CR_BF_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define DPX_CR_BF_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register DPX_CR_BF_PERF_COUNTER_0
*/
#define DPX_CR_BF_PERF_COUNTER_0 (0xC488U)
#define DPX_CR_BF_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define DPX_CR_BF_PERF_COUNTER_0_REG_SHIFT (0U)
#define DPX_CR_BF_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register DPX_CR_BT_PERF
*/
#define DPX_CR_BT_PERF (0xC3D0U)
#define DPX_CR_BT_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define DPX_CR_BT_PERF_CLR_3_SHIFT (4U)
#define DPX_CR_BT_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define DPX_CR_BT_PERF_CLR_3_EN (0x00000010U)
#define DPX_CR_BT_PERF_CLR_2_SHIFT (3U)
#define DPX_CR_BT_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define DPX_CR_BT_PERF_CLR_2_EN (0x00000008U)
#define DPX_CR_BT_PERF_CLR_1_SHIFT (2U)
#define DPX_CR_BT_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define DPX_CR_BT_PERF_CLR_1_EN (0x00000004U)
#define DPX_CR_BT_PERF_CLR_0_SHIFT (1U)
#define DPX_CR_BT_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define DPX_CR_BT_PERF_CLR_0_EN (0x00000002U)
#define DPX_CR_BT_PERF_CTRL_ENABLE_SHIFT (0U)
#define DPX_CR_BT_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define DPX_CR_BT_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register DPX_CR_BT_PERF_SELECT0
*/
#define DPX_CR_BT_PERF_SELECT0 (0xC3D8U)
#define DPX_CR_BT_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define DPX_CR_BT_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define DPX_CR_BT_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define DPX_CR_BT_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define DPX_CR_BT_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define DPX_CR_BT_PERF_SELECT0_MODE_SHIFT (21U)
#define DPX_CR_BT_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define DPX_CR_BT_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define DPX_CR_BT_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define DPX_CR_BT_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define DPX_CR_BT_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define DPX_CR_BT_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register DPX_CR_BT_PERF_COUNTER_0
*/
#define DPX_CR_BT_PERF_COUNTER_0 (0xC420U)
#define DPX_CR_BT_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define DPX_CR_BT_PERF_COUNTER_0_REG_SHIFT (0U)
#define DPX_CR_BT_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register DPX_CR_RQ_USC_DEBUG
*/
#define DPX_CR_RQ_USC_DEBUG (0xC110U)
#define DPX_CR_RQ_USC_DEBUG_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define DPX_CR_RQ_USC_DEBUG_CHECKSUM_SHIFT (0U)
#define DPX_CR_RQ_USC_DEBUG_CHECKSUM_CLRMSK (IMG_UINT64_C(0xFFFFFFFF00000000))

/*
    Register DPX_CR_BIF_FAULT_BANK_MMU_STATUS
*/
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS (0xC5C8U)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_MASKFULL \
	(IMG_UINT64_C(0x000000000000F775))
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_CAT_BASE_SHIFT (12U)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_CAT_BASE_CLRMSK (0xFFFF0FFFU)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_PAGE_SIZE_SHIFT (8U)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_PAGE_SIZE_CLRMSK (0xFFFFF8FFU)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_DATA_TYPE_SHIFT (5U)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_DATA_TYPE_CLRMSK (0xFFFFFF9FU)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_FAULT_RO_SHIFT (4U)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_FAULT_RO_CLRMSK (0xFFFFFFEFU)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_FAULT_RO_EN (0x00000010U)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_FAULT_PM_META_RO_SHIFT (2U)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_FAULT_PM_META_RO_CLRMSK (0xFFFFFFFBU)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_FAULT_PM_META_RO_EN (0x00000004U)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_FAULT_SHIFT (0U)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_FAULT_CLRMSK (0xFFFFFFFEU)
#define DPX_CR_BIF_FAULT_BANK_MMU_STATUS_FAULT_EN (0x00000001U)

/*
    Register DPX_CR_BIF_FAULT_BANK_REQ_STATUS
*/
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS (0xC5D0U)
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_MASKFULL \
	(IMG_UINT64_C(0x03FFFFFFFFFFFFF0))
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_RNW_SHIFT (57U)
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_RNW_CLRMSK \
	(IMG_UINT64_C(0xFDFFFFFFFFFFFFFF))
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_RNW_EN \
	(IMG_UINT64_C(0x0200000000000000))
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_TAG_SB_SHIFT (44U)
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_TAG_SB_CLRMSK \
	(IMG_UINT64_C(0xFE000FFFFFFFFFFF))
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_TAG_ID_SHIFT (40U)
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_TAG_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFF0FFFFFFFFFF))
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_ADDRESS_SHIFT (4U)
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF000000000F))
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_ADDRESS_ALIGNSHIFT (4U)
#define DPX_CR_BIF_FAULT_BANK_REQ_STATUS_ADDRESS_ALIGNSIZE (16U)

/*
    Register DPX_CR_BIF_MMU_STATUS
*/
#define DPX_CR_BIF_MMU_STATUS (0xC5D8U)
#define DPX_CR_BIF_MMU_STATUS_MASKFULL (IMG_UINT64_C(0x000000000FFFFFF7))
#define DPX_CR_BIF_MMU_STATUS_PC_DATA_SHIFT (20U)
#define DPX_CR_BIF_MMU_STATUS_PC_DATA_CLRMSK (0xF00FFFFFU)
#define DPX_CR_BIF_MMU_STATUS_PD_DATA_SHIFT (12U)
#define DPX_CR_BIF_MMU_STATUS_PD_DATA_CLRMSK (0xFFF00FFFU)
#define DPX_CR_BIF_MMU_STATUS_PT_DATA_SHIFT (4U)
#define DPX_CR_BIF_MMU_STATUS_PT_DATA_CLRMSK (0xFFFFF00FU)
#define DPX_CR_BIF_MMU_STATUS_STALLED_SHIFT (2U)
#define DPX_CR_BIF_MMU_STATUS_STALLED_CLRMSK (0xFFFFFFFBU)
#define DPX_CR_BIF_MMU_STATUS_STALLED_EN (0x00000004U)
#define DPX_CR_BIF_MMU_STATUS_PAUSED_SHIFT (1U)
#define DPX_CR_BIF_MMU_STATUS_PAUSED_CLRMSK (0xFFFFFFFDU)
#define DPX_CR_BIF_MMU_STATUS_PAUSED_EN (0x00000002U)
#define DPX_CR_BIF_MMU_STATUS_BUSY_SHIFT (0U)
#define DPX_CR_BIF_MMU_STATUS_BUSY_CLRMSK (0xFFFFFFFEU)
#define DPX_CR_BIF_MMU_STATUS_BUSY_EN (0x00000001U)

/*
    Register DPX_CR_RT_PERF
*/
#define DPX_CR_RT_PERF (0xC700U)
#define DPX_CR_RT_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define DPX_CR_RT_PERF_CLR_3_SHIFT (4U)
#define DPX_CR_RT_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define DPX_CR_RT_PERF_CLR_3_EN (0x00000010U)
#define DPX_CR_RT_PERF_CLR_2_SHIFT (3U)
#define DPX_CR_RT_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define DPX_CR_RT_PERF_CLR_2_EN (0x00000008U)
#define DPX_CR_RT_PERF_CLR_1_SHIFT (2U)
#define DPX_CR_RT_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define DPX_CR_RT_PERF_CLR_1_EN (0x00000004U)
#define DPX_CR_RT_PERF_CLR_0_SHIFT (1U)
#define DPX_CR_RT_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define DPX_CR_RT_PERF_CLR_0_EN (0x00000002U)
#define DPX_CR_RT_PERF_CTRL_ENABLE_SHIFT (0U)
#define DPX_CR_RT_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define DPX_CR_RT_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register DPX_CR_RT_PERF_SELECT0
*/
#define DPX_CR_RT_PERF_SELECT0 (0xC708U)
#define DPX_CR_RT_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define DPX_CR_RT_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define DPX_CR_RT_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define DPX_CR_RT_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define DPX_CR_RT_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define DPX_CR_RT_PERF_SELECT0_MODE_SHIFT (21U)
#define DPX_CR_RT_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define DPX_CR_RT_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define DPX_CR_RT_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define DPX_CR_RT_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define DPX_CR_RT_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define DPX_CR_RT_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register DPX_CR_RT_PERF_COUNTER_0
*/
#define DPX_CR_RT_PERF_COUNTER_0 (0xC730U)
#define DPX_CR_RT_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define DPX_CR_RT_PERF_COUNTER_0_REG_SHIFT (0U)
#define DPX_CR_RT_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register DPX_CR_BX_TU_PERF
*/
#define DPX_CR_BX_TU_PERF (0xC908U)
#define DPX_CR_BX_TU_PERF_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define DPX_CR_BX_TU_PERF_CLR_3_SHIFT (4U)
#define DPX_CR_BX_TU_PERF_CLR_3_CLRMSK (0xFFFFFFEFU)
#define DPX_CR_BX_TU_PERF_CLR_3_EN (0x00000010U)
#define DPX_CR_BX_TU_PERF_CLR_2_SHIFT (3U)
#define DPX_CR_BX_TU_PERF_CLR_2_CLRMSK (0xFFFFFFF7U)
#define DPX_CR_BX_TU_PERF_CLR_2_EN (0x00000008U)
#define DPX_CR_BX_TU_PERF_CLR_1_SHIFT (2U)
#define DPX_CR_BX_TU_PERF_CLR_1_CLRMSK (0xFFFFFFFBU)
#define DPX_CR_BX_TU_PERF_CLR_1_EN (0x00000004U)
#define DPX_CR_BX_TU_PERF_CLR_0_SHIFT (1U)
#define DPX_CR_BX_TU_PERF_CLR_0_CLRMSK (0xFFFFFFFDU)
#define DPX_CR_BX_TU_PERF_CLR_0_EN (0x00000002U)
#define DPX_CR_BX_TU_PERF_CTRL_ENABLE_SHIFT (0U)
#define DPX_CR_BX_TU_PERF_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define DPX_CR_BX_TU_PERF_CTRL_ENABLE_EN (0x00000001U)

/*
    Register DPX_CR_BX_TU_PERF_SELECT0
*/
#define DPX_CR_BX_TU_PERF_SELECT0 (0xC910U)
#define DPX_CR_BX_TU_PERF_SELECT0_MASKFULL (IMG_UINT64_C(0x3FFF3FFF003FFFFF))
#define DPX_CR_BX_TU_PERF_SELECT0_BATCH_MAX_SHIFT (48U)
#define DPX_CR_BX_TU_PERF_SELECT0_BATCH_MAX_CLRMSK \
	(IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define DPX_CR_BX_TU_PERF_SELECT0_BATCH_MIN_SHIFT (32U)
#define DPX_CR_BX_TU_PERF_SELECT0_BATCH_MIN_CLRMSK \
	(IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define DPX_CR_BX_TU_PERF_SELECT0_MODE_SHIFT (21U)
#define DPX_CR_BX_TU_PERF_SELECT0_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFDFFFFF))
#define DPX_CR_BX_TU_PERF_SELECT0_MODE_EN (IMG_UINT64_C(0x0000000000200000))
#define DPX_CR_BX_TU_PERF_SELECT0_GROUP_SELECT_SHIFT (16U)
#define DPX_CR_BX_TU_PERF_SELECT0_GROUP_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFE0FFFF))
#define DPX_CR_BX_TU_PERF_SELECT0_BIT_SELECT_SHIFT (0U)
#define DPX_CR_BX_TU_PERF_SELECT0_BIT_SELECT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register DPX_CR_BX_TU_PERF_COUNTER_0
*/
#define DPX_CR_BX_TU_PERF_COUNTER_0 (0xC938U)
#define DPX_CR_BX_TU_PERF_COUNTER_0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define DPX_CR_BX_TU_PERF_COUNTER_0_REG_SHIFT (0U)
#define DPX_CR_BX_TU_PERF_COUNTER_0_REG_CLRMSK (0x00000000U)

/*
    Register DPX_CR_RS_PDS_RR_CHECKSUM
*/
#define DPX_CR_RS_PDS_RR_CHECKSUM (0xC0F0U)
#define DPX_CR_RS_PDS_RR_CHECKSUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define DPX_CR_RS_PDS_RR_CHECKSUM_VALUE_SHIFT (0U)
#define DPX_CR_RS_PDS_RR_CHECKSUM_VALUE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00000000))

/*
    Register RGX_CR_MMU_CBASE_MAPPING_CONTEXT
*/
#define RGX_CR_MMU_CBASE_MAPPING_CONTEXT (0xE140U)
#define RGX_CR_MMU_CBASE_MAPPING_CONTEXT_MASKFULL \
	(IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_MMU_CBASE_MAPPING_CONTEXT_ID_SHIFT (0U)
#define RGX_CR_MMU_CBASE_MAPPING_CONTEXT_ID_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_MMU_CBASE_MAPPING
*/
#define RGX_CR_MMU_CBASE_MAPPING (0xE148U)
#define RGX_CR_MMU_CBASE_MAPPING_MASKFULL (IMG_UINT64_C(0x000000000FFFFFFF))
#define RGX_CR_MMU_CBASE_MAPPING_BASE_ADDR_SHIFT (0U)
#define RGX_CR_MMU_CBASE_MAPPING_BASE_ADDR_CLRMSK (0xF0000000U)
#define RGX_CR_MMU_CBASE_MAPPING_BASE_ADDR_ALIGNSHIFT (12U)
#define RGX_CR_MMU_CBASE_MAPPING_BASE_ADDR_ALIGNSIZE (4096U)

/*
    Register RGX_CR_MMU_FAULT_STATUS
*/
#define RGX_CR_MMU_FAULT_STATUS (0xE150U)
#define RGX_CR_MMU_FAULT_STATUS_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_MMU_FAULT_STATUS_ADDRESS_SHIFT (28U)
#define RGX_CR_MMU_FAULT_STATUS_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0x000000000FFFFFFF))
#define RGX_CR_MMU_FAULT_STATUS_CONTEXT_SHIFT (20U)
#define RGX_CR_MMU_FAULT_STATUS_CONTEXT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF00FFFFF))
#define RGX_CR_MMU_FAULT_STATUS_TAG_SB_SHIFT (12U)
#define RGX_CR_MMU_FAULT_STATUS_TAG_SB_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFF00FFF))
#define RGX_CR_MMU_FAULT_STATUS_REQ_ID_SHIFT (6U)
#define RGX_CR_MMU_FAULT_STATUS_REQ_ID_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFF03F))
#define RGX_CR_MMU_FAULT_STATUS_LEVEL_SHIFT (4U)
#define RGX_CR_MMU_FAULT_STATUS_LEVEL_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFCF))
#define RGX_CR_MMU_FAULT_STATUS_RNW_SHIFT (3U)
#define RGX_CR_MMU_FAULT_STATUS_RNW_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFF7))
#define RGX_CR_MMU_FAULT_STATUS_RNW_EN (IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_MMU_FAULT_STATUS_TYPE_SHIFT (1U)
#define RGX_CR_MMU_FAULT_STATUS_TYPE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFF9))
#define RGX_CR_MMU_FAULT_STATUS_FAULT_SHIFT (0U)
#define RGX_CR_MMU_FAULT_STATUS_FAULT_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MMU_FAULT_STATUS_FAULT_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_MMU_FAULT_STATUS_META
*/
#define RGX_CR_MMU_FAULT_STATUS_META (0xE158U)
#define RGX_CR_MMU_FAULT_STATUS_META_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_MMU_FAULT_STATUS_META_ADDRESS_SHIFT (28U)
#define RGX_CR_MMU_FAULT_STATUS_META_ADDRESS_CLRMSK \
	(IMG_UINT64_C(0x000000000FFFFFFF))
#define RGX_CR_MMU_FAULT_STATUS_META_CONTEXT_SHIFT (20U)
#define RGX_CR_MMU_FAULT_STATUS_META_CONTEXT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF00FFFFF))
#define RGX_CR_MMU_FAULT_STATUS_META_TAG_SB_SHIFT (12U)
#define RGX_CR_MMU_FAULT_STATUS_META_TAG_SB_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFF00FFF))
#define RGX_CR_MMU_FAULT_STATUS_META_REQ_ID_SHIFT (6U)
#define RGX_CR_MMU_FAULT_STATUS_META_REQ_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF03F))
#define RGX_CR_MMU_FAULT_STATUS_META_LEVEL_SHIFT (4U)
#define RGX_CR_MMU_FAULT_STATUS_META_LEVEL_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFCF))
#define RGX_CR_MMU_FAULT_STATUS_META_RNW_SHIFT (3U)
#define RGX_CR_MMU_FAULT_STATUS_META_RNW_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFF7))
#define RGX_CR_MMU_FAULT_STATUS_META_RNW_EN (IMG_UINT64_C(0x0000000000000008))
#define RGX_CR_MMU_FAULT_STATUS_META_TYPE_SHIFT (1U)
#define RGX_CR_MMU_FAULT_STATUS_META_TYPE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFF9))
#define RGX_CR_MMU_FAULT_STATUS_META_FAULT_SHIFT (0U)
#define RGX_CR_MMU_FAULT_STATUS_META_FAULT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_CR_MMU_FAULT_STATUS_META_FAULT_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_CR_SLC3_CTRL_MISC
*/
#define RGX_CR_SLC3_CTRL_MISC (0xE200U)
#define RGX_CR_SLC3_CTRL_MISC_MASKFULL (IMG_UINT64_C(0x0000000000000107))
#define RGX_CR_SLC3_CTRL_MISC_WRITE_COMBINER_SHIFT (8U)
#define RGX_CR_SLC3_CTRL_MISC_WRITE_COMBINER_CLRMSK (0xFFFFFEFFU)
#define RGX_CR_SLC3_CTRL_MISC_WRITE_COMBINER_EN (0x00000100U)
#define RGX_CR_SLC3_CTRL_MISC_ADDR_DECODE_MODE_SHIFT (0U)
#define RGX_CR_SLC3_CTRL_MISC_ADDR_DECODE_MODE_CLRMSK (0xFFFFFFF8U)
#define RGX_CR_SLC3_CTRL_MISC_ADDR_DECODE_MODE_LINEAR (0x00000000U)
#define RGX_CR_SLC3_CTRL_MISC_ADDR_DECODE_MODE_IN_PAGE_HASH (0x00000001U)
#define RGX_CR_SLC3_CTRL_MISC_ADDR_DECODE_MODE_FIXED_PVR_HASH (0x00000002U)
#define RGX_CR_SLC3_CTRL_MISC_ADDR_DECODE_MODE_SCRAMBLE_PVR_HASH (0x00000003U)
#define RGX_CR_SLC3_CTRL_MISC_ADDR_DECODE_MODE_WEAVED_HASH (0x00000004U)

/*
    Register RGX_CR_SLC3_SCRAMBLE
*/
#define RGX_CR_SLC3_SCRAMBLE (0xE208U)
#define RGX_CR_SLC3_SCRAMBLE_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_SLC3_SCRAMBLE_BITS_SHIFT (0U)
#define RGX_CR_SLC3_SCRAMBLE_BITS_CLRMSK (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_SLC3_SCRAMBLE2
*/
#define RGX_CR_SLC3_SCRAMBLE2 (0xE210U)
#define RGX_CR_SLC3_SCRAMBLE2_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_SLC3_SCRAMBLE2_BITS_SHIFT (0U)
#define RGX_CR_SLC3_SCRAMBLE2_BITS_CLRMSK (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_SLC3_SCRAMBLE3
*/
#define RGX_CR_SLC3_SCRAMBLE3 (0xE218U)
#define RGX_CR_SLC3_SCRAMBLE3_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_SLC3_SCRAMBLE3_BITS_SHIFT (0U)
#define RGX_CR_SLC3_SCRAMBLE3_BITS_CLRMSK (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_SLC3_SCRAMBLE4
*/
#define RGX_CR_SLC3_SCRAMBLE4 (0xE260U)
#define RGX_CR_SLC3_SCRAMBLE4_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_SLC3_SCRAMBLE4_BITS_SHIFT (0U)
#define RGX_CR_SLC3_SCRAMBLE4_BITS_CLRMSK (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_CR_SLC3_STATUS
*/
#define RGX_CR_SLC3_STATUS (0xE220U)
#define RGX_CR_SLC3_STATUS_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_CR_SLC3_STATUS_WRITES1_SHIFT (48U)
#define RGX_CR_SLC3_STATUS_WRITES1_CLRMSK (IMG_UINT64_C(0x0000FFFFFFFFFFFF))
#define RGX_CR_SLC3_STATUS_WRITES0_SHIFT (32U)
#define RGX_CR_SLC3_STATUS_WRITES0_CLRMSK (IMG_UINT64_C(0xFFFF0000FFFFFFFF))
#define RGX_CR_SLC3_STATUS_READS1_SHIFT (16U)
#define RGX_CR_SLC3_STATUS_READS1_CLRMSK (IMG_UINT64_C(0xFFFFFFFF0000FFFF))
#define RGX_CR_SLC3_STATUS_READS0_SHIFT (0U)
#define RGX_CR_SLC3_STATUS_READS0_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFF0000))

/*
    Register RGX_CR_SLC3_IDLE
*/
#define RGX_CR_SLC3_IDLE (0xE228U)
#define RGX_CR_SLC3_IDLE_MASKFULL (IMG_UINT64_C(0x00000000000FFFFF))
#define RGX_CR_SLC3_IDLE_ORDERQ_DUST2_SHIFT (18U)
#define RGX_CR_SLC3_IDLE_ORDERQ_DUST2_CLRMSK (0xFFF3FFFFU)
#define RGX_CR_SLC3_IDLE_MMU_SHIFT (17U)
#define RGX_CR_SLC3_IDLE_MMU_CLRMSK (0xFFFDFFFFU)
#define RGX_CR_SLC3_IDLE_MMU_EN (0x00020000U)
#define RGX_CR_SLC3_IDLE_RDI_SHIFT (16U)
#define RGX_CR_SLC3_IDLE_RDI_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_SLC3_IDLE_RDI_EN (0x00010000U)
#define RGX_CR_SLC3_IDLE_IMGBV4_SHIFT (12U)
#define RGX_CR_SLC3_IDLE_IMGBV4_CLRMSK (0xFFFF0FFFU)
#define RGX_CR_SLC3_IDLE_CACHE_BANKS_SHIFT (4U)
#define RGX_CR_SLC3_IDLE_CACHE_BANKS_CLRMSK (0xFFFFF00FU)
#define RGX_CR_SLC3_IDLE_ORDERQ_DUST_SHIFT (2U)
#define RGX_CR_SLC3_IDLE_ORDERQ_DUST_CLRMSK (0xFFFFFFF3U)
#define RGX_CR_SLC3_IDLE_ORDERQ_JONES_SHIFT (1U)
#define RGX_CR_SLC3_IDLE_ORDERQ_JONES_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SLC3_IDLE_ORDERQ_JONES_EN (0x00000002U)
#define RGX_CR_SLC3_IDLE_XBAR_SHIFT (0U)
#define RGX_CR_SLC3_IDLE_XBAR_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SLC3_IDLE_XBAR_EN (0x00000001U)

/*
    Register RGX_CR_SLC3_FAULT_STOP_STATUS
*/
#define RGX_CR_SLC3_FAULT_STOP_STATUS (0xE248U)
#define RGX_CR_SLC3_FAULT_STOP_STATUS_MASKFULL \
	(IMG_UINT64_C(0x0000000000001FFF))
#define RGX_CR_SLC3_FAULT_STOP_STATUS_BIF_SHIFT (0U)
#define RGX_CR_SLC3_FAULT_STOP_STATUS_BIF_CLRMSK (0xFFFFE000U)

/*
    Register RGX_CR_VDM_CONTEXT_STORE_MODE
*/
#define RGX_CR_VDM_CONTEXT_STORE_MODE (0xF048U)
#define RGX_CR_VDM_CONTEXT_STORE_MODE_MASKFULL \
	(IMG_UINT64_C(0x0000000000000003))
#define RGX_CR_VDM_CONTEXT_STORE_MODE_MODE_SHIFT (0U)
#define RGX_CR_VDM_CONTEXT_STORE_MODE_MODE_CLRMSK (0xFFFFFFFCU)
#define RGX_CR_VDM_CONTEXT_STORE_MODE_MODE_INDEX (0x00000000U)
#define RGX_CR_VDM_CONTEXT_STORE_MODE_MODE_INSTANCE (0x00000001U)
#define RGX_CR_VDM_CONTEXT_STORE_MODE_MODE_LIST (0x00000002U)

/*
    Register RGX_CR_CONTEXT_MAPPING0
*/
#define RGX_CR_CONTEXT_MAPPING0 (0xF078U)
#define RGX_CR_CONTEXT_MAPPING0_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_CONTEXT_MAPPING0_2D_SHIFT (24U)
#define RGX_CR_CONTEXT_MAPPING0_2D_CLRMSK (0x00FFFFFFU)
#define RGX_CR_CONTEXT_MAPPING0_CDM_SHIFT (16U)
#define RGX_CR_CONTEXT_MAPPING0_CDM_CLRMSK (0xFF00FFFFU)
#define RGX_CR_CONTEXT_MAPPING0_3D_SHIFT (8U)
#define RGX_CR_CONTEXT_MAPPING0_3D_CLRMSK (0xFFFF00FFU)
#define RGX_CR_CONTEXT_MAPPING0_TA_SHIFT (0U)
#define RGX_CR_CONTEXT_MAPPING0_TA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_CONTEXT_MAPPING1
*/
#define RGX_CR_CONTEXT_MAPPING1 (0xF080U)
#define RGX_CR_CONTEXT_MAPPING1_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_CONTEXT_MAPPING1_HOST_SHIFT (8U)
#define RGX_CR_CONTEXT_MAPPING1_HOST_CLRMSK (0xFFFF00FFU)
#define RGX_CR_CONTEXT_MAPPING1_TLA_SHIFT (0U)
#define RGX_CR_CONTEXT_MAPPING1_TLA_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_CONTEXT_MAPPING2
*/
#define RGX_CR_CONTEXT_MAPPING2 (0xF088U)
#define RGX_CR_CONTEXT_MAPPING2_MASKFULL (IMG_UINT64_C(0x0000000000FFFFFF))
#define RGX_CR_CONTEXT_MAPPING2_ALIST0_SHIFT (16U)
#define RGX_CR_CONTEXT_MAPPING2_ALIST0_CLRMSK (0xFF00FFFFU)
#define RGX_CR_CONTEXT_MAPPING2_TE0_SHIFT (8U)
#define RGX_CR_CONTEXT_MAPPING2_TE0_CLRMSK (0xFFFF00FFU)
#define RGX_CR_CONTEXT_MAPPING2_VCE0_SHIFT (0U)
#define RGX_CR_CONTEXT_MAPPING2_VCE0_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_CONTEXT_MAPPING3
*/
#define RGX_CR_CONTEXT_MAPPING3 (0xF090U)
#define RGX_CR_CONTEXT_MAPPING3_MASKFULL (IMG_UINT64_C(0x0000000000FFFFFF))
#define RGX_CR_CONTEXT_MAPPING3_ALIST1_SHIFT (16U)
#define RGX_CR_CONTEXT_MAPPING3_ALIST1_CLRMSK (0xFF00FFFFU)
#define RGX_CR_CONTEXT_MAPPING3_TE1_SHIFT (8U)
#define RGX_CR_CONTEXT_MAPPING3_TE1_CLRMSK (0xFFFF00FFU)
#define RGX_CR_CONTEXT_MAPPING3_VCE1_SHIFT (0U)
#define RGX_CR_CONTEXT_MAPPING3_VCE1_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_BIF_JONES_OUTSTANDING_READ
*/
#define RGX_CR_BIF_JONES_OUTSTANDING_READ (0xF098U)
#define RGX_CR_BIF_JONES_OUTSTANDING_READ_MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_BIF_JONES_OUTSTANDING_READ_COUNTER_SHIFT (0U)
#define RGX_CR_BIF_JONES_OUTSTANDING_READ_COUNTER_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_BIF_BLACKPEARL_OUTSTANDING_READ
*/
#define RGX_CR_BIF_BLACKPEARL_OUTSTANDING_READ (0xF0A0U)
#define RGX_CR_BIF_BLACKPEARL_OUTSTANDING_READ_MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_BIF_BLACKPEARL_OUTSTANDING_READ_COUNTER_SHIFT (0U)
#define RGX_CR_BIF_BLACKPEARL_OUTSTANDING_READ_COUNTER_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_BIF_DUST_OUTSTANDING_READ
*/
#define RGX_CR_BIF_DUST_OUTSTANDING_READ (0xF0A8U)
#define RGX_CR_BIF_DUST_OUTSTANDING_READ_MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_BIF_DUST_OUTSTANDING_READ_COUNTER_SHIFT (0U)
#define RGX_CR_BIF_DUST_OUTSTANDING_READ_COUNTER_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_JONES_FIX
*/
#define RGX_CR_JONES_FIX (0xF0C0U)
#define RGX_CR_JONES_FIX__ROGUE3__MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_JONES_FIX_MASKFULL (IMG_UINT64_C(0x000000000000FFFF))
#define RGX_CR_JONES_FIX_DISABLE_SHIFT (0U)
#define RGX_CR_JONES_FIX_DISABLE_CLRMSK (0xFFFF0000U)

/*
    Register RGX_CR_CONTEXT_MAPPING4
*/
#define RGX_CR_CONTEXT_MAPPING4 (0xF210U)
#define RGX_CR_CONTEXT_MAPPING4_MASKFULL (IMG_UINT64_C(0x0000FFFFFFFFFFFF))
#define RGX_CR_CONTEXT_MAPPING4_3D_MMU_STACK_SHIFT (40U)
#define RGX_CR_CONTEXT_MAPPING4_3D_MMU_STACK_CLRMSK \
	(IMG_UINT64_C(0xFFFF00FFFFFFFFFF))
#define RGX_CR_CONTEXT_MAPPING4_3D_UFSTACK_SHIFT (32U)
#define RGX_CR_CONTEXT_MAPPING4_3D_UFSTACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF00FFFFFFFF))
#define RGX_CR_CONTEXT_MAPPING4_3D_FSTACK_SHIFT (24U)
#define RGX_CR_CONTEXT_MAPPING4_3D_FSTACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFF00FFFFFF))
#define RGX_CR_CONTEXT_MAPPING4_TA_MMU_STACK_SHIFT (16U)
#define RGX_CR_CONTEXT_MAPPING4_TA_MMU_STACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFF00FFFF))
#define RGX_CR_CONTEXT_MAPPING4_TA_UFSTACK_SHIFT (8U)
#define RGX_CR_CONTEXT_MAPPING4_TA_UFSTACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF00FF))
#define RGX_CR_CONTEXT_MAPPING4_TA_FSTACK_SHIFT (0U)
#define RGX_CR_CONTEXT_MAPPING4_TA_FSTACK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFF00))

/*
    Register RGX_CR_MULTICORE_GPU
*/
#define RGX_CR_MULTICORE_GPU (0xF300U)
#define RGX_CR_MULTICORE_GPU_MASKFULL (IMG_UINT64_C(0x000000000000007F))
#define RGX_CR_MULTICORE_GPU_CAPABILITY_FRAGMENT_SHIFT (6U)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_FRAGMENT_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_FRAGMENT_EN (0x00000040U)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_GEOMETRY_SHIFT (5U)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_GEOMETRY_CLRMSK (0xFFFFFFDFU)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_GEOMETRY_EN (0x00000020U)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_COMPUTE_SHIFT (4U)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_COMPUTE_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_COMPUTE_EN (0x00000010U)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_PRIMARY_SHIFT (3U)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_PRIMARY_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_MULTICORE_GPU_CAPABILITY_PRIMARY_EN (0x00000008U)
#define RGX_CR_MULTICORE_GPU_ID_SHIFT (0U)
#define RGX_CR_MULTICORE_GPU_ID_CLRMSK (0xFFFFFFF8U)

/*
    Register RGX_CR_MULTICORE_SYSTEM
*/
#define RGX_CR_MULTICORE_SYSTEM (0xF308U)
#define RGX_CR_MULTICORE_SYSTEM_MASKFULL (IMG_UINT64_C(0x000000000000000F))
#define RGX_CR_MULTICORE_SYSTEM_GPU_COUNT_SHIFT (0U)
#define RGX_CR_MULTICORE_SYSTEM_GPU_COUNT_CLRMSK (0xFFFFFFF0U)

/*
    Register RGX_CR_MULTICORE_FRAGMENT_CTRL_COMMON
*/
#define RGX_CR_MULTICORE_FRAGMENT_CTRL_COMMON (0xF310U)
#define RGX_CR_MULTICORE_FRAGMENT_CTRL_COMMON_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MULTICORE_FRAGMENT_CTRL_COMMON_WORKLOAD_TYPE_SHIFT (30U)
#define RGX_CR_MULTICORE_FRAGMENT_CTRL_COMMON_WORKLOAD_TYPE_CLRMSK (0x3FFFFFFFU)
#define RGX_CR_MULTICORE_FRAGMENT_CTRL_COMMON_WORKLOAD_EXECUTE_COUNT_SHIFT (8U)
#define RGX_CR_MULTICORE_FRAGMENT_CTRL_COMMON_WORKLOAD_EXECUTE_COUNT_CLRMSK \
	(0xC00000FFU)
#define RGX_CR_MULTICORE_FRAGMENT_CTRL_COMMON_GPU_ENABLE_SHIFT (0U)
#define RGX_CR_MULTICORE_FRAGMENT_CTRL_COMMON_GPU_ENABLE_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_MULTICORE_GEOMETRY_CTRL_COMMON
*/
#define RGX_CR_MULTICORE_GEOMETRY_CTRL_COMMON (0xF320U)
#define RGX_CR_MULTICORE_GEOMETRY_CTRL_COMMON_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MULTICORE_GEOMETRY_CTRL_COMMON_WORKLOAD_TYPE_SHIFT (30U)
#define RGX_CR_MULTICORE_GEOMETRY_CTRL_COMMON_WORKLOAD_TYPE_CLRMSK (0x3FFFFFFFU)
#define RGX_CR_MULTICORE_GEOMETRY_CTRL_COMMON_WORKLOAD_EXECUTE_COUNT_SHIFT (8U)
#define RGX_CR_MULTICORE_GEOMETRY_CTRL_COMMON_WORKLOAD_EXECUTE_COUNT_CLRMSK \
	(0xC00000FFU)
#define RGX_CR_MULTICORE_GEOMETRY_CTRL_COMMON_GPU_ENABLE_SHIFT (0U)
#define RGX_CR_MULTICORE_GEOMETRY_CTRL_COMMON_GPU_ENABLE_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_MULTICORE_COMPUTE_CTRL_COMMON
*/
#define RGX_CR_MULTICORE_COMPUTE_CTRL_COMMON (0xF330U)
#define RGX_CR_MULTICORE_COMPUTE_CTRL_COMMON_MASKFULL \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_CR_MULTICORE_COMPUTE_CTRL_COMMON_WORKLOAD_TYPE_SHIFT (30U)
#define RGX_CR_MULTICORE_COMPUTE_CTRL_COMMON_WORKLOAD_TYPE_CLRMSK (0x3FFFFFFFU)
#define RGX_CR_MULTICORE_COMPUTE_CTRL_COMMON_WORKLOAD_EXECUTE_COUNT_SHIFT (8U)
#define RGX_CR_MULTICORE_COMPUTE_CTRL_COMMON_WORKLOAD_EXECUTE_COUNT_CLRMSK \
	(0xC00000FFU)
#define RGX_CR_MULTICORE_COMPUTE_CTRL_COMMON_GPU_ENABLE_SHIFT (0U)
#define RGX_CR_MULTICORE_COMPUTE_CTRL_COMMON_GPU_ENABLE_CLRMSK (0xFFFFFF00U)

/*
    Register RGX_CR_ECC_RAM_ERR_INJ
*/
#define RGX_CR_ECC_RAM_ERR_INJ (0xF340U)
#define RGX_CR_ECC_RAM_ERR_INJ_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_ECC_RAM_ERR_INJ_SLC_SIDEKICK_SHIFT (4U)
#define RGX_CR_ECC_RAM_ERR_INJ_SLC_SIDEKICK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_ECC_RAM_ERR_INJ_SLC_SIDEKICK_EN (0x00000010U)
#define RGX_CR_ECC_RAM_ERR_INJ_USC_SHIFT (3U)
#define RGX_CR_ECC_RAM_ERR_INJ_USC_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_ECC_RAM_ERR_INJ_USC_EN (0x00000008U)
#define RGX_CR_ECC_RAM_ERR_INJ_TPU_MCU_L0_SHIFT (2U)
#define RGX_CR_ECC_RAM_ERR_INJ_TPU_MCU_L0_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_ECC_RAM_ERR_INJ_TPU_MCU_L0_EN (0x00000004U)
#define RGX_CR_ECC_RAM_ERR_INJ_RASCAL_SHIFT (1U)
#define RGX_CR_ECC_RAM_ERR_INJ_RASCAL_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_ECC_RAM_ERR_INJ_RASCAL_EN (0x00000002U)
#define RGX_CR_ECC_RAM_ERR_INJ_MARS_SHIFT (0U)
#define RGX_CR_ECC_RAM_ERR_INJ_MARS_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_ECC_RAM_ERR_INJ_MARS_EN (0x00000001U)

/*
    Register RGX_CR_ECC_RAM_INIT_KICK
*/
#define RGX_CR_ECC_RAM_INIT_KICK (0xF348U)
#define RGX_CR_ECC_RAM_INIT_KICK_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_ECC_RAM_INIT_KICK_SLC_SIDEKICK_SHIFT (4U)
#define RGX_CR_ECC_RAM_INIT_KICK_SLC_SIDEKICK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_ECC_RAM_INIT_KICK_SLC_SIDEKICK_EN (0x00000010U)
#define RGX_CR_ECC_RAM_INIT_KICK_USC_SHIFT (3U)
#define RGX_CR_ECC_RAM_INIT_KICK_USC_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_ECC_RAM_INIT_KICK_USC_EN (0x00000008U)
#define RGX_CR_ECC_RAM_INIT_KICK_TPU_MCU_L0_SHIFT (2U)
#define RGX_CR_ECC_RAM_INIT_KICK_TPU_MCU_L0_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_ECC_RAM_INIT_KICK_TPU_MCU_L0_EN (0x00000004U)
#define RGX_CR_ECC_RAM_INIT_KICK_RASCAL_SHIFT (1U)
#define RGX_CR_ECC_RAM_INIT_KICK_RASCAL_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_ECC_RAM_INIT_KICK_RASCAL_EN (0x00000002U)
#define RGX_CR_ECC_RAM_INIT_KICK_MARS_SHIFT (0U)
#define RGX_CR_ECC_RAM_INIT_KICK_MARS_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_ECC_RAM_INIT_KICK_MARS_EN (0x00000001U)

/*
    Register RGX_CR_ECC_RAM_INIT_DONE
*/
#define RGX_CR_ECC_RAM_INIT_DONE (0xF350U)
#define RGX_CR_ECC_RAM_INIT_DONE_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_CR_ECC_RAM_INIT_DONE_SLC_SIDEKICK_SHIFT (4U)
#define RGX_CR_ECC_RAM_INIT_DONE_SLC_SIDEKICK_CLRMSK (0xFFFFFFEFU)
#define RGX_CR_ECC_RAM_INIT_DONE_SLC_SIDEKICK_EN (0x00000010U)
#define RGX_CR_ECC_RAM_INIT_DONE_USC_SHIFT (3U)
#define RGX_CR_ECC_RAM_INIT_DONE_USC_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_ECC_RAM_INIT_DONE_USC_EN (0x00000008U)
#define RGX_CR_ECC_RAM_INIT_DONE_TPU_MCU_L0_SHIFT (2U)
#define RGX_CR_ECC_RAM_INIT_DONE_TPU_MCU_L0_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_ECC_RAM_INIT_DONE_TPU_MCU_L0_EN (0x00000004U)
#define RGX_CR_ECC_RAM_INIT_DONE_RASCAL_SHIFT (1U)
#define RGX_CR_ECC_RAM_INIT_DONE_RASCAL_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_ECC_RAM_INIT_DONE_RASCAL_EN (0x00000002U)
#define RGX_CR_ECC_RAM_INIT_DONE_MARS_SHIFT (0U)
#define RGX_CR_ECC_RAM_INIT_DONE_MARS_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_ECC_RAM_INIT_DONE_MARS_EN (0x00000001U)

/*
    Register RGX_CR_SAFETY_EVENT_ENABLE
*/
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE (0xF390U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__MASKFULL \
	(IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_LOCKUP_SHIFT (7U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_LOCKUP_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_LOCKUP_EN (0x00000080U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__CPU_PAGE_FAULT_SHIFT (6U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__CPU_PAGE_FAULT_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__CPU_PAGE_FAULT_EN (0x00000040U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__SAFE_COMPUTE_FAIL_SHIFT (5U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__SAFE_COMPUTE_FAIL_CLRMSK \
	(0xFFFFFFDFU)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__SAFE_COMPUTE_FAIL_EN (0x00000020U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__WATCHDOG_TIMEOUT_SHIFT (4U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__WATCHDOG_TIMEOUT_CLRMSK \
	(0xFFFFFFEFU)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__WATCHDOG_TIMEOUT_EN (0x00000010U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__TRP_FAIL_SHIFT (3U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__TRP_FAIL_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__TRP_FAIL_EN (0x00000008U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_FW_SHIFT (2U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_FW_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_FW_EN (0x00000004U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_GPU_SHIFT (1U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_GPU_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_GPU_EN (0x00000002U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_PAGE_FAULT_SHIFT (0U)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_PAGE_FAULT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_PAGE_FAULT_EN (0x00000001U)

/*
    Register RGX_CR_SAFETY_EVENT_STATUS
*/
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE (0xF398U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__MASKFULL \
	(IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__GPU_LOCKUP_SHIFT (7U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__GPU_LOCKUP_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__GPU_LOCKUP_EN (0x00000080U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__CPU_PAGE_FAULT_SHIFT (6U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__CPU_PAGE_FAULT_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__CPU_PAGE_FAULT_EN (0x00000040U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__SAFE_COMPUTE_FAIL_SHIFT (5U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__SAFE_COMPUTE_FAIL_CLRMSK \
	(0xFFFFFFDFU)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__SAFE_COMPUTE_FAIL_EN (0x00000020U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__WATCHDOG_TIMEOUT_SHIFT (4U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__WATCHDOG_TIMEOUT_CLRMSK \
	(0xFFFFFFEFU)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__WATCHDOG_TIMEOUT_EN (0x00000010U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__TRP_FAIL_SHIFT (3U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__TRP_FAIL_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__TRP_FAIL_EN (0x00000008U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__FAULT_FW_SHIFT (2U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__FAULT_FW_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__FAULT_FW_EN (0x00000004U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__FAULT_GPU_SHIFT (1U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__FAULT_GPU_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__FAULT_GPU_EN (0x00000002U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__GPU_PAGE_FAULT_SHIFT (0U)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__GPU_PAGE_FAULT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SAFETY_EVENT_STATUS__ROGUEXE__GPU_PAGE_FAULT_EN (0x00000001U)

/*
    Register RGX_CR_SAFETY_EVENT_CLEAR
*/
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE (0xF3A0U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__MASKFULL \
	(IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__GPU_LOCKUP_SHIFT (7U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__GPU_LOCKUP_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__GPU_LOCKUP_EN (0x00000080U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__CPU_PAGE_FAULT_SHIFT (6U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__CPU_PAGE_FAULT_CLRMSK (0xFFFFFFBFU)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__CPU_PAGE_FAULT_EN (0x00000040U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__SAFE_COMPUTE_FAIL_SHIFT (5U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__SAFE_COMPUTE_FAIL_CLRMSK \
	(0xFFFFFFDFU)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__SAFE_COMPUTE_FAIL_EN (0x00000020U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__WATCHDOG_TIMEOUT_SHIFT (4U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__WATCHDOG_TIMEOUT_CLRMSK \
	(0xFFFFFFEFU)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__WATCHDOG_TIMEOUT_EN (0x00000010U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__TRP_FAIL_SHIFT (3U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__TRP_FAIL_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__TRP_FAIL_EN (0x00000008U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__FAULT_FW_SHIFT (2U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__FAULT_FW_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__FAULT_FW_EN (0x00000004U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__FAULT_GPU_SHIFT (1U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__FAULT_GPU_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__FAULT_GPU_EN (0x00000002U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__GPU_PAGE_FAULT_SHIFT (0U)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__GPU_PAGE_FAULT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_SAFETY_EVENT_CLEAR__ROGUEXE__GPU_PAGE_FAULT_EN (0x00000001U)

/*
    Register RGX_CR_FAULT_FW_STATUS
*/
#define RGX_CR_FAULT_FW_STATUS (0xF3B0U)
#define RGX_CR_FAULT_FW_STATUS_MASKFULL (IMG_UINT64_C(0x0000000000010001))
#define RGX_CR_FAULT_FW_STATUS_CPU_CORRECT_SHIFT (16U)
#define RGX_CR_FAULT_FW_STATUS_CPU_CORRECT_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_FAULT_FW_STATUS_CPU_CORRECT_EN (0x00010000U)
#define RGX_CR_FAULT_FW_STATUS_CPU_DETECT_SHIFT (0U)
#define RGX_CR_FAULT_FW_STATUS_CPU_DETECT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FAULT_FW_STATUS_CPU_DETECT_EN (0x00000001U)

/*
    Register RGX_CR_FAULT_FW_CLEAR
*/
#define RGX_CR_FAULT_FW_CLEAR (0xF3B8U)
#define RGX_CR_FAULT_FW_CLEAR_MASKFULL (IMG_UINT64_C(0x0000000000010001))
#define RGX_CR_FAULT_FW_CLEAR_CPU_CORRECT_SHIFT (16U)
#define RGX_CR_FAULT_FW_CLEAR_CPU_CORRECT_CLRMSK (0xFFFEFFFFU)
#define RGX_CR_FAULT_FW_CLEAR_CPU_CORRECT_EN (0x00010000U)
#define RGX_CR_FAULT_FW_CLEAR_CPU_DETECT_SHIFT (0U)
#define RGX_CR_FAULT_FW_CLEAR_CPU_DETECT_CLRMSK (0xFFFFFFFEU)
#define RGX_CR_FAULT_FW_CLEAR_CPU_DETECT_EN (0x00000001U)

/*
    Register RGX_CR_MTS_SAFETY_EVENT_ENABLE
*/
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE (0xF3D8U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__MASKFULL \
	(IMG_UINT64_C(0x00000000000000FF))
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_LOCKUP_SHIFT (7U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_LOCKUP_CLRMSK (0xFFFFFF7FU)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_LOCKUP_EN (0x00000080U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__CPU_PAGE_FAULT_SHIFT (6U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__CPU_PAGE_FAULT_CLRMSK \
	(0xFFFFFFBFU)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__CPU_PAGE_FAULT_EN (0x00000040U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__SAFE_COMPUTE_FAIL_SHIFT (5U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__SAFE_COMPUTE_FAIL_CLRMSK \
	(0xFFFFFFDFU)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__SAFE_COMPUTE_FAIL_EN \
	(0x00000020U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__WATCHDOG_TIMEOUT_SHIFT (4U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__WATCHDOG_TIMEOUT_CLRMSK \
	(0xFFFFFFEFU)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__WATCHDOG_TIMEOUT_EN \
	(0x00000010U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__TRP_FAIL_SHIFT (3U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__TRP_FAIL_CLRMSK (0xFFFFFFF7U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__TRP_FAIL_EN (0x00000008U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_FW_SHIFT (2U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_FW_CLRMSK (0xFFFFFFFBU)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_FW_EN (0x00000004U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_GPU_SHIFT (1U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_GPU_CLRMSK (0xFFFFFFFDU)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__FAULT_GPU_EN (0x00000002U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_PAGE_FAULT_SHIFT (0U)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_PAGE_FAULT_CLRMSK \
	(0xFFFFFFFEU)
#define RGX_CR_MTS_SAFETY_EVENT_ENABLE__ROGUEXE__GPU_PAGE_FAULT_EN (0x00000001U)

#endif /* RGX_CR_DEFS_KM_H */
/*****************************************************************************
 End of file (rgx_cr_defs_km.h)
*****************************************************************************/
