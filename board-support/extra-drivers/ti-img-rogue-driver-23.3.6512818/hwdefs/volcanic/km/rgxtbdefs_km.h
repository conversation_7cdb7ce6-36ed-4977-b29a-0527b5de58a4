/*************************************************************************/ /*!
@Title          Hardware definition file rgxtbdefs_km.h
@Brief          The file contains auto-generated hardware definitions without
                BVNC-specific compile time conditionals.
@Copyright      Copyright (c) Imagination Technologies Ltd. All Rights Reserved
@License        Dual MIT/GPLv2

The contents of this file are subject to the MIT license as set out below.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

Alternatively, the contents of this file may be used under the terms of
the GNU General Public License Version 2 ("GPL") in which case the provisions
of GPL are applicable instead of those above.

If you wish to allow use of your version of this file only under the terms of
GPL, and not to allow others to use your version of this file under the terms
of the MIT license, indicate your decision by deleting the provisions above
and replace them with the notice and other provisions required by GPL as set
out in the file called "GPL-COPYING" included in this distribution. If you do
not delete the provisions above, a recipient may use your version of this file
under the terms of either the MIT license or GPL.

This License is also included in this distribution in the file called
"MIT-COPYING".

EXCEPT AS OTHERWISE STATED IN A NEGOTIATED AGREEMENT: (A) THE SOFTWARE IS
PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
PURPOSE AND NONINFRINGEMENT; AND (B) IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ /**************************************************************************/

/*               ****   Autogenerated C -- do not edit    ****               */

/*
 */

#ifndef RGXTBDEFS_KM_H
#define RGXTBDEFS_KM_H

#include "img_types.h"
#include "img_defs.h"

#define RGXTBDEFS_KM_REVISION 1

/*
    Register RGX_TB_SOFT_RESET
*/
#define RGX_TB_SOFT_RESET (0x0000U)
#define RGX_TB_SOFT_RESET_MASKFULL (IMG_UINT64_C(0x00000000FFFF0107))
#define RGX_TB_SOFT_RESET_SPU_SHIFT (16U)
#define RGX_TB_SOFT_RESET_SPU_CLRMSK (IMG_UINT64_C(0xFFFFFFFF0000FFFF))
#define RGX_TB_SOFT_RESET_JONES_SHIFT (8U)
#define RGX_TB_SOFT_RESET_JONES_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFEFF))
#define RGX_TB_SOFT_RESET_JONES_EN (IMG_UINT64_C(0x0000000000000100))
#define RGX_TB_SOFT_RESET_SYS_SHIFT (2U)
#define RGX_TB_SOFT_RESET_SYS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_TB_SOFT_RESET_SYS_EN (IMG_UINT64_C(0x0000000000000004))
#define RGX_TB_SOFT_RESET_MEM_SHIFT (1U)
#define RGX_TB_SOFT_RESET_MEM_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_TB_SOFT_RESET_MEM_EN (IMG_UINT64_C(0x0000000000000002))
#define RGX_TB_SOFT_RESET_CORE_SHIFT (0U)
#define RGX_TB_SOFT_RESET_CORE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_SOFT_RESET_CORE_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_TB_PCI_MASTER
*/
#define RGX_TB_PCI_MASTER (0x0008U)
#define RGX_TB_PCI_MASTER_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_TB_PCI_MASTER_MODE_SHIFT (0U)
#define RGX_TB_PCI_MASTER_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_PCI_MASTER_MODE_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_TB_MEM_ARBITER
*/
#define RGX_TB_MEM_ARBITER (0x0088U)
#define RGX_TB_MEM_ARBITER_MASKFULL (IMG_UINT64_C(0x0000000000010F11))
#define RGX_TB_MEM_ARBITER_LIMIT_BW_SHIFT (16U)
#define RGX_TB_MEM_ARBITER_LIMIT_BW_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFEFFFF))
#define RGX_TB_MEM_ARBITER_LIMIT_BW_EN (IMG_UINT64_C(0x0000000000010000))
#define RGX_TB_MEM_ARBITER_PRI_SKEW_SHIFT (8U)
#define RGX_TB_MEM_ARBITER_PRI_SKEW_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFF0FF))
#define RGX_TB_MEM_ARBITER_PRI_RNW_SHIFT (4U)
#define RGX_TB_MEM_ARBITER_PRI_RNW_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFEF))
#define RGX_TB_MEM_ARBITER_PRI_RNW_EN (IMG_UINT64_C(0x0000000000000010))
#define RGX_TB_MEM_ARBITER_ENABLE_SHIFT (0U)
#define RGX_TB_MEM_ARBITER_ENABLE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_MEM_ARBITER_ENABLE_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_TB_QOS_RD_LATENCY
*/
#define RGX_TB_QOS_RD_LATENCY (0x0090U)
#define RGX_TB_QOS_RD_LATENCY_MASKFULL (IMG_UINT64_C(0xFFFF3FFF3FFF3FFF))
#define RGX_TB_QOS_RD_LATENCY_DIST_SHIFT (62U)
#define RGX_TB_QOS_RD_LATENCY_DIST_CLRMSK (IMG_UINT64_C(0x3FFFFFFFFFFFFFFF))
#define RGX_TB_QOS_RD_LATENCY_MAX_15_SHIFT (48U)
#define RGX_TB_QOS_RD_LATENCY_MAX_15_CLRMSK (IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_TB_QOS_RD_LATENCY_MIN_15_SHIFT (32U)
#define RGX_TB_QOS_RD_LATENCY_MIN_15_CLRMSK (IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_TB_QOS_RD_LATENCY_MAX_0_SHIFT (16U)
#define RGX_TB_QOS_RD_LATENCY_MAX_0_CLRMSK (IMG_UINT64_C(0xFFFFFFFFC000FFFF))
#define RGX_TB_QOS_RD_LATENCY_MIN_0_SHIFT (0U)
#define RGX_TB_QOS_RD_LATENCY_MIN_0_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFC000))

/*
    Register RGX_TB_QOS_WR_LATENCY
*/
#define RGX_TB_QOS_WR_LATENCY (0x0098U)
#define RGX_TB_QOS_WR_LATENCY_MASKFULL (IMG_UINT64_C(0xFFFF3FFF3FFF3FFF))
#define RGX_TB_QOS_WR_LATENCY_DIST_SHIFT (62U)
#define RGX_TB_QOS_WR_LATENCY_DIST_CLRMSK (IMG_UINT64_C(0x3FFFFFFFFFFFFFFF))
#define RGX_TB_QOS_WR_LATENCY_MAX_15_SHIFT (48U)
#define RGX_TB_QOS_WR_LATENCY_MAX_15_CLRMSK (IMG_UINT64_C(0xC000FFFFFFFFFFFF))
#define RGX_TB_QOS_WR_LATENCY_MIN_15_SHIFT (32U)
#define RGX_TB_QOS_WR_LATENCY_MIN_15_CLRMSK (IMG_UINT64_C(0xFFFFC000FFFFFFFF))
#define RGX_TB_QOS_WR_LATENCY_MAX_0_SHIFT (16U)
#define RGX_TB_QOS_WR_LATENCY_MAX_0_CLRMSK (IMG_UINT64_C(0xFFFFFFFFC000FFFF))
#define RGX_TB_QOS_WR_LATENCY_MIN_0_SHIFT (0U)
#define RGX_TB_QOS_WR_LATENCY_MIN_0_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFC000))

/*
    Register RGX_TB_MAX_ID_OUTSTANDING
*/
#define RGX_TB_MAX_ID_OUTSTANDING (0x00B0U)
#define RGX_TB_MAX_ID_OUTSTANDING_MASKFULL (IMG_UINT64_C(0x000003FF03FF03FF))
#define RGX_TB_MAX_ID_OUTSTANDING_RD_WR_SHIFT (32U)
#define RGX_TB_MAX_ID_OUTSTANDING_RD_WR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFC00FFFFFFFF))
#define RGX_TB_MAX_ID_OUTSTANDING_WRITE_SHIFT (16U)
#define RGX_TB_MAX_ID_OUTSTANDING_WRITE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFC00FFFF))
#define RGX_TB_MAX_ID_OUTSTANDING_READ_SHIFT (0U)
#define RGX_TB_MAX_ID_OUTSTANDING_READ_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFC00))

/*
    Register RGX_TB_COHERENT_MEM_REGION
*/
#define RGX_TB_COHERENT_MEM_REGION (0x00C0U)
#define RGX_TB_COHERENT_MEM_REGION_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_TB_COHERENT_MEM_REGION_START_ADDR_SHIFT (12U)
#define RGX_TB_COHERENT_MEM_REGION_START_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))

/*
    Register RGX_TB_LMA_MEM_REGION
*/
#define RGX_TB_LMA_MEM_REGION (0x00C8U)
#define RGX_TB_LMA_MEM_REGION_MASKFULL (IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_LMA_MEM_REGION_SIZE_SHIFT (0U)
#define RGX_TB_LMA_MEM_REGION_SIZE_CLRMSK (IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_UMA_MEM_REGION
*/
#define RGX_TB_UMA_MEM_REGION (0x00D0U)
#define RGX_TB_UMA_MEM_REGION_MASKFULL (IMG_UINT64_C(0x000000FFFFFFF000))
#define RGX_TB_UMA_MEM_REGION_START_ADDR_SHIFT (12U)
#define RGX_TB_UMA_MEM_REGION_START_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000FFF))

/*
    Register RGX_TB_SYSTEM_STATUS
*/
#define RGX_TB_SYSTEM_STATUS (0x00E0U)
#define RGX_TB_SYSTEM_STATUS_MASKFULL (IMG_UINT64_C(0xFFFFFFFF3FF700FF))
#define RGX_TB_SYSTEM_STATUS_SPU_ISON_SHIFT (48U)
#define RGX_TB_SYSTEM_STATUS_SPU_ISON_CLRMSK (IMG_UINT64_C(0x0000FFFFFFFFFFFF))
#define RGX_TB_SYSTEM_STATUS_SPU_POWER_SHIFT (32U)
#define RGX_TB_SYSTEM_STATUS_SPU_POWER_CLRMSK (IMG_UINT64_C(0xFFFF0000FFFFFFFF))
#define RGX_TB_SYSTEM_STATUS_DAVY_ISON_SHIFT (29U)
#define RGX_TB_SYSTEM_STATUS_DAVY_ISON_CLRMSK (IMG_UINT64_C(0xFFFFFFFFDFFFFFFF))
#define RGX_TB_SYSTEM_STATUS_DAVY_ISON_EN (IMG_UINT64_C(0x0000000020000000))
#define RGX_TB_SYSTEM_STATUS_DAVY_POWER_SHIFT (28U)
#define RGX_TB_SYSTEM_STATUS_DAVY_POWER_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFEFFFFFFF))
#define RGX_TB_SYSTEM_STATUS_DAVY_POWER_EN (IMG_UINT64_C(0x0000000010000000))
#define RGX_TB_SYSTEM_STATUS_CHEST_ENOLA_ISON_SHIFT (27U)
#define RGX_TB_SYSTEM_STATUS_CHEST_ENOLA_ISON_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF7FFFFFF))
#define RGX_TB_SYSTEM_STATUS_CHEST_ENOLA_ISON_EN \
	(IMG_UINT64_C(0x0000000008000000))
#define RGX_TB_SYSTEM_STATUS_CHEST_ENOLA_POWER_SHIFT (26U)
#define RGX_TB_SYSTEM_STATUS_CHEST_ENOLA_POWER_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFBFFFFFF))
#define RGX_TB_SYSTEM_STATUS_CHEST_ENOLA_POWER_EN \
	(IMG_UINT64_C(0x0000000004000000))
#define RGX_TB_SYSTEM_STATUS_HOST_POWER_EVENT_ABORT_SHIFT (25U)
#define RGX_TB_SYSTEM_STATUS_HOST_POWER_EVENT_ABORT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFDFFFFFF))
#define RGX_TB_SYSTEM_STATUS_HOST_POWER_EVENT_ABORT_EN \
	(IMG_UINT64_C(0x0000000002000000))
#define RGX_TB_SYSTEM_STATUS_HOST_POWER_EVENT_COMPLETE_SHIFT (24U)
#define RGX_TB_SYSTEM_STATUS_HOST_POWER_EVENT_COMPLETE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFEFFFFFF))
#define RGX_TB_SYSTEM_STATUS_HOST_POWER_EVENT_COMPLETE_EN \
	(IMG_UINT64_C(0x0000000001000000))
#define RGX_TB_SYSTEM_STATUS_GPU_STATE_SHIFT (20U)
#define RGX_TB_SYSTEM_STATUS_GPU_STATE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFF0FFFFF))
#define RGX_TB_SYSTEM_STATUS_SYSTEM_IRQ_SHIFT (18U)
#define RGX_TB_SYSTEM_STATUS_SYSTEM_IRQ_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFBFFFF))
#define RGX_TB_SYSTEM_STATUS_SYSTEM_IRQ_EN (IMG_UINT64_C(0x0000000000040000))
#define RGX_TB_SYSTEM_STATUS_TRIGGER_SHIFT (17U)
#define RGX_TB_SYSTEM_STATUS_TRIGGER_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFDFFFF))
#define RGX_TB_SYSTEM_STATUS_TRIGGER_EN (IMG_UINT64_C(0x0000000000020000))
#define RGX_TB_SYSTEM_STATUS_HMMU_IRQ_SHIFT (16U)
#define RGX_TB_SYSTEM_STATUS_HMMU_IRQ_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFEFFFF))
#define RGX_TB_SYSTEM_STATUS_HMMU_IRQ_EN (IMG_UINT64_C(0x0000000000010000))
#define RGX_TB_SYSTEM_STATUS_IRQ_SHIFT (0U)
#define RGX_TB_SYSTEM_STATUS_IRQ_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFF00))

/*
    Register RGX_TB_SYSTEM_CONFIG
*/
#define RGX_TB_SYSTEM_CONFIG (0x00F0U)
#define RGX_TB_SYSTEM_CONFIG_MASKFULL (IMG_UINT64_C(0x0000000007007737))
#define RGX_TB_SYSTEM_CONFIG_SOC_AXI_FEATURE_SHIFT (24U)
#define RGX_TB_SYSTEM_CONFIG_SOC_AXI_FEATURE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFF8FFFFFF))
#define RGX_TB_SYSTEM_CONFIG_ICE_CABLEIF256_SHIFT (14U)
#define RGX_TB_SYSTEM_CONFIG_ICE_CABLEIF256_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFBFFF))
#define RGX_TB_SYSTEM_CONFIG_ICE_CABLEIF256_EN \
	(IMG_UINT64_C(0x0000000000004000))
#define RGX_TB_SYSTEM_CONFIG_VELOCE_TBX_SHIFT (13U)
#define RGX_TB_SYSTEM_CONFIG_VELOCE_TBX_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFDFFF))
#define RGX_TB_SYSTEM_CONFIG_VELOCE_TBX_EN (IMG_UINT64_C(0x0000000000002000))
#define RGX_TB_SYSTEM_CONFIG_VELOCE_ICE_SHIFT (12U)
#define RGX_TB_SYSTEM_CONFIG_VELOCE_ICE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFEFFF))
#define RGX_TB_SYSTEM_CONFIG_VELOCE_ICE_EN (IMG_UINT64_C(0x0000000000001000))
#define RGX_TB_SYSTEM_CONFIG_CADENCE_AVIP_SHIFT (10U)
#define RGX_TB_SYSTEM_CONFIG_CADENCE_AVIP_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFBFF))
#define RGX_TB_SYSTEM_CONFIG_CADENCE_AVIP_EN (IMG_UINT64_C(0x0000000000000400))
#define RGX_TB_SYSTEM_CONFIG_CADENCE_TBA_SHIFT (9U)
#define RGX_TB_SYSTEM_CONFIG_CADENCE_TBA_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFDFF))
#define RGX_TB_SYSTEM_CONFIG_CADENCE_TBA_EN (IMG_UINT64_C(0x0000000000000200))
#define RGX_TB_SYSTEM_CONFIG_CADENCE_ICE_SHIFT (8U)
#define RGX_TB_SYSTEM_CONFIG_CADENCE_ICE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFEFF))
#define RGX_TB_SYSTEM_CONFIG_CADENCE_ICE_EN (IMG_UINT64_C(0x0000000000000100))
#define RGX_TB_SYSTEM_CONFIG_EMU_UMA_SHIFT (5U)
#define RGX_TB_SYSTEM_CONFIG_EMU_UMA_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFDF))
#define RGX_TB_SYSTEM_CONFIG_EMU_UMA_EN (IMG_UINT64_C(0x0000000000000020))
#define RGX_TB_SYSTEM_CONFIG_EMU_BUILD_SHIFT (4U)
#define RGX_TB_SYSTEM_CONFIG_EMU_BUILD_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFEF))
#define RGX_TB_SYSTEM_CONFIG_EMU_BUILD_EN (IMG_UINT64_C(0x0000000000000010))
#define RGX_TB_SYSTEM_CONFIG_NS_NOC_SHIFT (2U)
#define RGX_TB_SYSTEM_CONFIG_NS_NOC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFB))
#define RGX_TB_SYSTEM_CONFIG_NS_NOC_EN (IMG_UINT64_C(0x0000000000000004))
#define RGX_TB_SYSTEM_CONFIG_IMG_NOC_SHIFT (1U)
#define RGX_TB_SYSTEM_CONFIG_IMG_NOC_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_TB_SYSTEM_CONFIG_IMG_NOC_EN (IMG_UINT64_C(0x0000000000000002))
#define RGX_TB_SYSTEM_CONFIG_TB_NONCOHERENT_SHIFT (0U)
#define RGX_TB_SYSTEM_CONFIG_TB_NONCOHERENT_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_SYSTEM_CONFIG_TB_NONCOHERENT_EN \
	(IMG_UINT64_C(0x0000000000000001))

#define RGX_TB_DOMAINSPLIT_TYPE_RESERVED_SHIFT (9U)
#define RGX_TB_DOMAINSPLIT_TYPE_RESERVED_CLRMSK (0xFFFF01FFU)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU7_SHIFT (8U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU7_CLRMSK (0xFFFFFEFFU)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU7_EN (0x00000100U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU6_SHIFT (7U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU6_CLRMSK (0xFFFFFF7FU)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU6_EN (0x00000080U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU5_SHIFT (6U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU5_CLRMSK (0xFFFFFFBFU)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU5_EN (0x00000040U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU4_SHIFT (5U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU4_CLRMSK (0xFFFFFFDFU)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU4_EN (0x00000020U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU3_SHIFT (4U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU3_CLRMSK (0xFFFFFFEFU)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU3_EN (0x00000010U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU2_SHIFT (3U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU2_CLRMSK (0xFFFFFFF7U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU2_EN (0x00000008U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU1_SHIFT (2U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU1_CLRMSK (0xFFFFFFFBU)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU1_EN (0x00000004U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU0_SHIFT (1U)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU0_CLRMSK (0xFFFFFFFDU)
#define RGX_TB_DOMAINSPLIT_TYPE_SPU0_EN (0x00000002U)
#define RGX_TB_DOMAINSPLIT_TYPE_JONES_SHIFT (0U)
#define RGX_TB_DOMAINSPLIT_TYPE_JONES_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_DOMAINSPLIT_TYPE_JONES_EN (0x00000001U)

#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_MASK (0xFFFFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER31_SHIFT (31U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER31_CLRMSK (0x7FFFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER31_EN (0x80000000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER30_SHIFT (30U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER30_CLRMSK (0xBFFFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER30_EN (0x40000000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER29_SHIFT (29U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER29_CLRMSK (0xDFFFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER29_EN (0x20000000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER28_SHIFT (28U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER28_CLRMSK (0xEFFFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER28_EN (0x10000000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER27_SHIFT (27U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER27_CLRMSK (0xF7FFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER27_EN (0x08000000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER26_SHIFT (26U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER26_CLRMSK (0xFBFFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER26_EN (0x04000000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER25_SHIFT (25U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER25_CLRMSK (0xFDFFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER25_EN (0x02000000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER24_SHIFT (24U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER24_CLRMSK (0xFEFFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER24_EN (0x01000000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER23_SHIFT (23U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER23_CLRMSK (0xFF7FFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER23_EN (0x00800000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER22_SHIFT (22U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER22_CLRMSK (0xFFBFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER22_EN (0x00400000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER21_SHIFT (21U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER21_CLRMSK (0xFFDFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER21_EN (0x00200000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER20_SHIFT (20U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER20_CLRMSK (0xFFEFFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER20_EN (0x00100000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER19_SHIFT (19U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER19_CLRMSK (0xFFF7FFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER19_EN (0x00080000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER18_SHIFT (18U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER18_CLRMSK (0xFFFBFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER18_EN (0x00040000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER17_SHIFT (17U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER17_CLRMSK (0xFFFDFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER17_EN (0x00020000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER16_SHIFT (16U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER16_CLRMSK (0xFFFEFFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER16_EN (0x00010000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER15_SHIFT (15U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER15_CLRMSK (0xFFFF7FFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER15_EN (0x00008000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER14_SHIFT (14U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER14_CLRMSK (0xFFFFBFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER14_EN (0x00004000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER13_SHIFT (13U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER13_CLRMSK (0xFFFFDFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER13_EN (0x00002000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER12_SHIFT (12U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER12_CLRMSK (0xFFFFEFFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER12_EN (0x00001000U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER11_SHIFT (11U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER11_CLRMSK (0xFFFFF7FFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER11_EN (0x00000800U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER10_SHIFT (10U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER10_CLRMSK (0xFFFFFBFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER10_EN (0x00000400U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER9_SHIFT (9U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER9_CLRMSK (0xFFFFFDFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER9_EN (0x00000200U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER8_SHIFT (8U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER8_CLRMSK (0xFFFFFEFFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER8_EN (0x00000100U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER7_SHIFT (7U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER7_CLRMSK (0xFFFFFF7FU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER7_EN (0x00000080U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER6_SHIFT (6U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER6_CLRMSK (0xFFFFFFBFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER6_EN (0x00000040U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER5_SHIFT (5U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER5_CLRMSK (0xFFFFFFDFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER5_EN (0x00000020U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER4_SHIFT (4U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER4_CLRMSK (0xFFFFFFEFU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER4_EN (0x00000010U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER3_SHIFT (3U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER3_CLRMSK (0xFFFFFFF7U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER3_EN (0x00000008U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER2_SHIFT (2U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER2_CLRMSK (0xFFFFFFFBU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER2_EN (0x00000004U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER1_SHIFT (1U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER1_CLRMSK (0xFFFFFFFDU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER1_EN (0x00000002U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER0_SHIFT (0U)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER0_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_DOMAINSPLIT_CLUSTER_TYPE_CLUSTER0_EN (0x00000001U)

#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC31_SHIFT (31U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC31_CLRMSK (0x7FFFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC31_EN (0x80000000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC30_SHIFT (30U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC30_CLRMSK (0xBFFFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC30_EN (0x40000000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC29_SHIFT (29U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC29_CLRMSK (0xDFFFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC29_EN (0x20000000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC28_SHIFT (28U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC28_CLRMSK (0xEFFFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC28_EN (0x10000000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC27_SHIFT (27U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC27_CLRMSK (0xF7FFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC27_EN (0x08000000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC26_SHIFT (26U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC26_CLRMSK (0xFBFFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC26_EN (0x04000000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC25_SHIFT (25U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC25_CLRMSK (0xFDFFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC25_EN (0x02000000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC24_SHIFT (24U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC24_CLRMSK (0xFEFFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC24_EN (0x01000000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC23_SHIFT (23U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC23_CLRMSK (0xFF7FFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC23_EN (0x00800000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC22_SHIFT (22U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC22_CLRMSK (0xFFBFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC22_EN (0x00400000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC21_SHIFT (21U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC21_CLRMSK (0xFFDFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC21_EN (0x00200000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC20_SHIFT (20U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC20_CLRMSK (0xFFEFFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC20_EN (0x00100000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC19_SHIFT (19U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC19_CLRMSK (0xFFF7FFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC19_EN (0x00080000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC18_SHIFT (18U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC18_CLRMSK (0xFFFBFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC18_EN (0x00040000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC17_SHIFT (17U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC17_CLRMSK (0xFFFDFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC17_EN (0x00020000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC16_SHIFT (16U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC16_CLRMSK (0xFFFEFFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC16_EN (0x00010000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC15_SHIFT (15U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC15_CLRMSK (0xFFFF7FFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC15_EN (0x00008000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC14_SHIFT (14U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC14_CLRMSK (0xFFFFBFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC14_EN (0x00004000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC13_SHIFT (13U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC13_CLRMSK (0xFFFFDFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC13_EN (0x00002000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC12_SHIFT (12U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC12_CLRMSK (0xFFFFEFFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC12_EN (0x00001000U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC11_SHIFT (11U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC11_CLRMSK (0xFFFFF7FFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC11_EN (0x00000800U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC10_SHIFT (10U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC10_CLRMSK (0xFFFFFBFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC10_EN (0x00000400U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC9_SHIFT (9U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC9_CLRMSK (0xFFFFFDFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC9_EN (0x00000200U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC8_SHIFT (8U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC8_CLRMSK (0xFFFFFEFFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC8_EN (0x00000100U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC7_SHIFT (7U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC7_CLRMSK (0xFFFFFF7FU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC7_EN (0x00000080U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC6_SHIFT (6U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC6_CLRMSK (0xFFFFFFBFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC6_EN (0x00000040U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC5_SHIFT (5U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC5_CLRMSK (0xFFFFFFDFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC5_EN (0x00000020U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC4_SHIFT (4U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC4_CLRMSK (0xFFFFFFEFU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC4_EN (0x00000010U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC3_SHIFT (3U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC3_CLRMSK (0xFFFFFFF7U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC3_EN (0x00000008U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC2_SHIFT (2U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC2_CLRMSK (0xFFFFFFFBU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC2_EN (0x00000004U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC1_SHIFT (1U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC1_CLRMSK (0xFFFFFFFDU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC1_EN (0x00000002U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC0_SHIFT (0U)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC0_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_DOMAINSPLIT_RAC_TYPE_RAC0_EN (0x00000001U)

/*
    Register RGX_TB_HOST_POWER_EVENT
*/
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3 (0x0100U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__MASKFULL \
	(IMG_UINT64_C(0xFFFFFFFF0000FF03))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_SHIFT (32U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLRMSK \
	(IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER31_SHIFT \
	(63U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER31_CLRMSK \
	(IMG_UINT64_C(0x7fffffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER30_SHIFT \
	(62U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER30_CLRMSK \
	(IMG_UINT64_C(0xbfffffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER29_SHIFT \
	(61U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER29_CLRMSK \
	(IMG_UINT64_C(0xdfffffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER28_SHIFT \
	(60U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER28_CLRMSK \
	(IMG_UINT64_C(0xefffffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER27_SHIFT \
	(59U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER27_CLRMSK \
	(IMG_UINT64_C(0xf7ffffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER26_SHIFT \
	(58U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER26_CLRMSK \
	(IMG_UINT64_C(0xfbffffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER25_SHIFT \
	(57U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER25_CLRMSK \
	(IMG_UINT64_C(0xfdffffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER24_SHIFT \
	(56U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER24_CLRMSK \
	(IMG_UINT64_C(0xfeffffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER23_SHIFT \
	(55U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER23_CLRMSK \
	(IMG_UINT64_C(0xff7fffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER22_SHIFT \
	(54U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER22_CLRMSK \
	(IMG_UINT64_C(0xffbfffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER21_SHIFT \
	(53U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER21_CLRMSK \
	(IMG_UINT64_C(0xffdfffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER20_SHIFT \
	(52U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER20_CLRMSK \
	(IMG_UINT64_C(0xffefffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER19_SHIFT \
	(51U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER19_CLRMSK \
	(IMG_UINT64_C(0xfff7ffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER18_SHIFT \
	(50U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER18_CLRMSK \
	(IMG_UINT64_C(0xfffbffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER17_SHIFT \
	(49U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER17_CLRMSK \
	(IMG_UINT64_C(0xfffdffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER16_SHIFT \
	(48U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER16_CLRMSK \
	(IMG_UINT64_C(0xfffeffffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER15_SHIFT \
	(47U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER15_CLRMSK \
	(IMG_UINT64_C(0xffff7fffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER14_SHIFT \
	(46U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER14_CLRMSK \
	(IMG_UINT64_C(0xffffbfffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER13_SHIFT \
	(45U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER13_CLRMSK \
	(IMG_UINT64_C(0xffffdfffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER12_SHIFT \
	(44U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER12_CLRMSK \
	(IMG_UINT64_C(0xffffefffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER11_SHIFT \
	(43U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER11_CLRMSK \
	(IMG_UINT64_C(0xfffff7ffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER10_SHIFT \
	(42U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER10_CLRMSK \
	(IMG_UINT64_C(0xfffffbffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER9_SHIFT \
	(41U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER9_CLRMSK \
	(IMG_UINT64_C(0xfffffdffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER8_SHIFT \
	(40U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER8_CLRMSK \
	(IMG_UINT64_C(0xfffffeffffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER7_SHIFT \
	(39U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER7_CLRMSK \
	(IMG_UINT64_C(0xffffff7fffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER6_SHIFT \
	(38U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER6_CLRMSK \
	(IMG_UINT64_C(0xffffffbfffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER5_SHIFT \
	(37U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER5_CLRMSK \
	(IMG_UINT64_C(0xffffffdfffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER4_SHIFT \
	(36U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER4_CLRMSK \
	(IMG_UINT64_C(0xffffffefffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER3_SHIFT \
	(35U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER3_CLRMSK \
	(IMG_UINT64_C(0xfffffff7ffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER2_SHIFT \
	(34U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER2_CLRMSK \
	(IMG_UINT64_C(0xfffffffbffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER1_SHIFT \
	(33U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER1_CLRMSK \
	(IMG_UINT64_C(0xfffffffdffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER0_SHIFT \
	(32U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__DOMAIN_CLUSTER_CLUSTER0_CLRMSK \
	(IMG_UINT64_C(0xfffffffeffffffff))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__GPU_MASK_SHIFT (8U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__GPU_MASK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFF00FF))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__REQ_SHIFT (1U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__REQ_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFD))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__REQ_EN \
	(IMG_UINT64_C(0x0000000000000002))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__TYPE_SHIFT (0U)
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__TYPE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__TYPE_POWER_DOWN \
	(IMG_UINT64_C(0x0000000000000000))
#define RGX_TB_HOST_POWER_EVENT__POWER_ISLAND_GEQ3__TYPE_POWER_UP \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_TB_GPU_CONTROL
*/
#define RGX_TB_GPU_CONTROL (0x0110U)
#define RGX_TB_GPU_CONTROL_MASKFULL (IMG_UINT64_C(0x0000000000000007))
#define RGX_TB_GPU_CONTROL_FW_LOG_DISABLE_SHIFT (2U)
#define RGX_TB_GPU_CONTROL_FW_LOG_DISABLE_CLRMSK (0xFFFFFFFBU)
#define RGX_TB_GPU_CONTROL_FW_LOG_DISABLE_EN (0x00000004U)
#define RGX_TB_GPU_CONTROL_DXT_BC_ENABLE_SHIFT (1U)
#define RGX_TB_GPU_CONTROL_DXT_BC_ENABLE_CLRMSK (0xFFFFFFFDU)
#define RGX_TB_GPU_CONTROL_DXT_BC_ENABLE_EN (0x00000002U)
#define RGX_TB_GPU_CONTROL_ASTC_ENABLE_SHIFT (0U)
#define RGX_TB_GPU_CONTROL_ASTC_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_GPU_CONTROL_ASTC_ENABLE_EN (0x00000001U)

/*
    Register RGX_TB_HOST_POWER_EVENT_JONES
*/
#define RGX_TB_HOST_POWER_EVENT_JONES__POWER_ISLAND_GEQ2_AND_POWER_ISLAND_LEQ5 \
	(0x0118U)
#define RGX_TB_HOST_POWER_EVENT_JONES__POWER_ISLAND_GEQ2_AND_POWER_ISLAND_LEQ5__MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_TB_HOST_POWER_EVENT_JONES__POWER_ISLAND_GEQ2_AND_POWER_ISLAND_LEQ5__DOMAIN_SHIFT \
	(0U)
#define RGX_TB_HOST_POWER_EVENT_JONES__POWER_ISLAND_GEQ2_AND_POWER_ISLAND_LEQ5__DOMAIN_CLRMSK \
	(0xFFFFFFFEU)
#define RGX_TB_HOST_POWER_EVENT_JONES__POWER_ISLAND_GEQ2_AND_POWER_ISLAND_LEQ5__DOMAIN_EN \
	(0x00000001U)

/*
    Register RGX_TB_HOST_POWER_EVENT_TEXAS
*/
#define RGX_TB_HOST_POWER_EVENT_TEXAS__POWER_ISLAND_GEQ3_AND_POWER_ISLAND_LEQ5 \
	(0x0168U)
#define RGX_TB_HOST_POWER_EVENT_TEXAS__POWER_ISLAND_GEQ3_AND_POWER_ISLAND_LEQ5__MASKFULL \
	(IMG_UINT64_C(0x000000000000FFFF))
#define RGX_TB_HOST_POWER_EVENT_TEXAS__POWER_ISLAND_GEQ3_AND_POWER_ISLAND_LEQ5__DOMAIN_SHIFT \
	(0U)
#define RGX_TB_HOST_POWER_EVENT_TEXAS__POWER_ISLAND_GEQ3_AND_POWER_ISLAND_LEQ5__DOMAIN_CLRMSK \
	(0xFFFF0000U)

/*
    Register RGX_TB_HOST_POWER_EVENT_CHEST_ENOLA
*/
#define RGX_TB_HOST_POWER_EVENT_CHEST_ENOLA__POWER_ISLAND_GEQ3_AND_POWER_ISLAND_LEQ5 \
	(0x0170U)
#define RGX_TB_HOST_POWER_EVENT_CHEST_ENOLA__POWER_ISLAND_GEQ3_AND_POWER_ISLAND_LEQ5__MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_TB_HOST_POWER_EVENT_CHEST_ENOLA__POWER_ISLAND_GEQ3_AND_POWER_ISLAND_LEQ5__DOMAIN_SHIFT \
	(0U)
#define RGX_TB_HOST_POWER_EVENT_CHEST_ENOLA__POWER_ISLAND_GEQ3_AND_POWER_ISLAND_LEQ5__DOMAIN_CLRMSK \
	(0xFFFFFFFEU)
#define RGX_TB_HOST_POWER_EVENT_CHEST_ENOLA__POWER_ISLAND_GEQ3_AND_POWER_ISLAND_LEQ5__DOMAIN_EN \
	(0x00000001U)

/*
    Register RGX_TB_TRIGGER_SHORT
*/
#define RGX_TB_TRIGGER_SHORT (0x1500U)
#define RGX_TB_TRIGGER_SHORT_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_TB_TRIGGER_SHORT_TIMEOUT_SHIFT (0U)
#define RGX_TB_TRIGGER_SHORT_TIMEOUT_CLRMSK (0x00000000U)

/*
    Register RGX_TB_TRIGGER_MEDIUM
*/
#define RGX_TB_TRIGGER_MEDIUM (0x1508U)
#define RGX_TB_TRIGGER_MEDIUM_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_TB_TRIGGER_MEDIUM_TIMEOUT_SHIFT (0U)
#define RGX_TB_TRIGGER_MEDIUM_TIMEOUT_CLRMSK (0x00000000U)

/*
    Register RGX_TB_TRIGGER_HIGH
*/
#define RGX_TB_TRIGGER_HIGH (0x1510U)
#define RGX_TB_TRIGGER_HIGH_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_TB_TRIGGER_HIGH_TIMEOUT_SHIFT (0U)
#define RGX_TB_TRIGGER_HIGH_TIMEOUT_CLRMSK (0x00000000U)

/*
    Register RGX_TB_TRIGGER_MAX
*/
#define RGX_TB_TRIGGER_MAX (0x1518U)
#define RGX_TB_TRIGGER_MAX_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_TB_TRIGGER_MAX_TIMEOUT_SHIFT (0U)
#define RGX_TB_TRIGGER_MAX_TIMEOUT_CLRMSK (0x00000000U)

/*
    Register RGX_TB_RDATA_CORRUPT_ENABLE
*/
#define RGX_TB_RDATA_CORRUPT_ENABLE (0x1560U)
#define RGX_TB_RDATA_CORRUPT_ENABLE_MASKFULL (IMG_UINT64_C(0x0000000000000001))
#define RGX_TB_RDATA_CORRUPT_ENABLE_ENABLE_SHIFT (0U)
#define RGX_TB_RDATA_CORRUPT_ENABLE_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_RDATA_CORRUPT_ENABLE_ENABLE_EN (0x00000001U)

/*
    Register RGX_TB_RDATA_CORRUPT_MASK
*/
#define RGX_TB_RDATA_CORRUPT_MASK (0x1568U)
#define RGX_TB_RDATA_CORRUPT_MASK_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_TB_RDATA_CORRUPT_MASK_MMU_SHIFT (31U)
#define RGX_TB_RDATA_CORRUPT_MASK_MMU_CLRMSK (0x7FFFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_MMU_EN (0x80000000U)
#define RGX_TB_RDATA_CORRUPT_MASK_UPS_SHIFT (30U)
#define RGX_TB_RDATA_CORRUPT_MASK_UPS_CLRMSK (0xBFFFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_UPS_EN (0x40000000U)
#define RGX_TB_RDATA_CORRUPT_MASK_FBM_SHIFT (29U)
#define RGX_TB_RDATA_CORRUPT_MASK_FBM_CLRMSK (0xDFFFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_FBM_EN (0x20000000U)
#define RGX_TB_RDATA_CORRUPT_MASK_TUL_SHIFT (28U)
#define RGX_TB_RDATA_CORRUPT_MASK_TUL_CLRMSK (0xEFFFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_TUL_EN (0x10000000U)
#define RGX_TB_RDATA_CORRUPT_MASK_SHR_SHIFT (27U)
#define RGX_TB_RDATA_CORRUPT_MASK_SHR_CLRMSK (0xF7FFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_SHR_EN (0x08000000U)
#define RGX_TB_RDATA_CORRUPT_MASK_FBA_SHIFT (26U)
#define RGX_TB_RDATA_CORRUPT_MASK_FBA_CLRMSK (0xFBFFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_FBA_EN (0x04000000U)
#define RGX_TB_RDATA_CORRUPT_MASK_VDM_SHIFT (25U)
#define RGX_TB_RDATA_CORRUPT_MASK_VDM_CLRMSK (0xFDFFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_VDM_EN (0x02000000U)
#define RGX_TB_RDATA_CORRUPT_MASK_USC_L2_SHIFT (24U)
#define RGX_TB_RDATA_CORRUPT_MASK_USC_L2_CLRMSK (0xFEFFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_USC_L2_EN (0x01000000U)
#define RGX_TB_RDATA_CORRUPT_MASK_PDS_SHIFT (23U)
#define RGX_TB_RDATA_CORRUPT_MASK_PDS_CLRMSK (0xFF7FFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_PDS_EN (0x00800000U)
#define RGX_TB_RDATA_CORRUPT_MASK_PDSRW_SHIFT (22U)
#define RGX_TB_RDATA_CORRUPT_MASK_PDSRW_CLRMSK (0xFFBFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_PDSRW_EN (0x00400000U)
#define RGX_TB_RDATA_CORRUPT_MASK_TPF_SHIFT (21U)
#define RGX_TB_RDATA_CORRUPT_MASK_TPF_CLRMSK (0xFFDFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_TPF_EN (0x00200000U)
#define RGX_TB_RDATA_CORRUPT_MASK_SHF_SHIFT (20U)
#define RGX_TB_RDATA_CORRUPT_MASK_SHF_CLRMSK (0xFFEFFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_SHF_EN (0x00100000U)
#define RGX_TB_RDATA_CORRUPT_MASK_AMC_SHIFT (19U)
#define RGX_TB_RDATA_CORRUPT_MASK_AMC_CLRMSK (0xFFF7FFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_AMC_EN (0x00080000U)
#define RGX_TB_RDATA_CORRUPT_MASK_RAC_SHIFT (18U)
#define RGX_TB_RDATA_CORRUPT_MASK_RAC_CLRMSK (0xFFFBFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_RAC_EN (0x00040000U)
#define RGX_TB_RDATA_CORRUPT_MASK_VCE_RTC_SHIFT (17U)
#define RGX_TB_RDATA_CORRUPT_MASK_VCE_RTC_CLRMSK (0xFFFDFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_VCE_RTC_EN (0x00020000U)
#define RGX_TB_RDATA_CORRUPT_MASK_ISP_SHIFT (16U)
#define RGX_TB_RDATA_CORRUPT_MASK_ISP_CLRMSK (0xFFFEFFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_ISP_EN (0x00010000U)
#define RGX_TB_RDATA_CORRUPT_MASK_PPP_SHIFT (15U)
#define RGX_TB_RDATA_CORRUPT_MASK_PPP_CLRMSK (0xFFFF7FFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_PPP_EN (0x00008000U)
#define RGX_TB_RDATA_CORRUPT_MASK_IPF_SHIFT (14U)
#define RGX_TB_RDATA_CORRUPT_MASK_IPF_CLRMSK (0xFFFFBFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_IPF_EN (0x00004000U)
#define RGX_TB_RDATA_CORRUPT_MASK_VCE_SHIFT (13U)
#define RGX_TB_RDATA_CORRUPT_MASK_VCE_CLRMSK (0xFFFFDFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_VCE_EN (0x00002000U)
#define RGX_TB_RDATA_CORRUPT_MASK_PBE_SHIFT (12U)
#define RGX_TB_RDATA_CORRUPT_MASK_PBE_CLRMSK (0xFFFFEFFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_PBE_EN (0x00001000U)
#define RGX_TB_RDATA_CORRUPT_MASK_TCU_SHIFT (11U)
#define RGX_TB_RDATA_CORRUPT_MASK_TCU_CLRMSK (0xFFFFF7FFU)
#define RGX_TB_RDATA_CORRUPT_MASK_TCU_EN (0x00000800U)
#define RGX_TB_RDATA_CORRUPT_MASK_MCU_SHIFT (10U)
#define RGX_TB_RDATA_CORRUPT_MASK_MCU_CLRMSK (0xFFFFFBFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_MCU_EN (0x00000400U)
#define RGX_TB_RDATA_CORRUPT_MASK_RPM_SHIFT (9U)
#define RGX_TB_RDATA_CORRUPT_MASK_RPM_CLRMSK (0xFFFFFDFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_RPM_EN (0x00000200U)
#define RGX_TB_RDATA_CORRUPT_MASK_RTU_SHIFT (8U)
#define RGX_TB_RDATA_CORRUPT_MASK_RTU_CLRMSK (0xFFFFFEFFU)
#define RGX_TB_RDATA_CORRUPT_MASK_RTU_EN (0x00000100U)
#define RGX_TB_RDATA_CORRUPT_MASK_TILING_SHIFT (7U)
#define RGX_TB_RDATA_CORRUPT_MASK_TILING_CLRMSK (0xFFFFFF7FU)
#define RGX_TB_RDATA_CORRUPT_MASK_TILING_EN (0x00000080U)
#define RGX_TB_RDATA_CORRUPT_MASK_META_DMA_SHIFT (6U)
#define RGX_TB_RDATA_CORRUPT_MASK_META_DMA_CLRMSK (0xFFFFFFBFU)
#define RGX_TB_RDATA_CORRUPT_MASK_META_DMA_EN (0x00000040U)
#define RGX_TB_RDATA_CORRUPT_MASK_META_SHIFT (5U)
#define RGX_TB_RDATA_CORRUPT_MASK_META_CLRMSK (0xFFFFFFDFU)
#define RGX_TB_RDATA_CORRUPT_MASK_META_EN (0x00000020U)
#define RGX_TB_RDATA_CORRUPT_MASK_CDM_SHIFT (4U)
#define RGX_TB_RDATA_CORRUPT_MASK_CDM_CLRMSK (0xFFFFFFEFU)
#define RGX_TB_RDATA_CORRUPT_MASK_CDM_EN (0x00000010U)
#define RGX_TB_RDATA_CORRUPT_MASK_PM_SHIFT (3U)
#define RGX_TB_RDATA_CORRUPT_MASK_PM_CLRMSK (0xFFFFFFF7U)
#define RGX_TB_RDATA_CORRUPT_MASK_PM_EN (0x00000008U)
#define RGX_TB_RDATA_CORRUPT_MASK_TDM_SHIFT (2U)
#define RGX_TB_RDATA_CORRUPT_MASK_TDM_CLRMSK (0xFFFFFFFBU)
#define RGX_TB_RDATA_CORRUPT_MASK_TDM_EN (0x00000004U)
#define RGX_TB_RDATA_CORRUPT_MASK_DCE_SHIFT (1U)
#define RGX_TB_RDATA_CORRUPT_MASK_DCE_CLRMSK (0xFFFFFFFDU)
#define RGX_TB_RDATA_CORRUPT_MASK_DCE_EN (0x00000002U)
#define RGX_TB_RDATA_CORRUPT_MASK_IPP_SHIFT (0U)
#define RGX_TB_RDATA_CORRUPT_MASK_IPP_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_RDATA_CORRUPT_MASK_IPP_EN (0x00000001U)

/*
    Register RGX_TB_RDATA_CORRUPT_FREQ
*/
#define RGX_TB_RDATA_CORRUPT_FREQ (0x1570U)
#define RGX_TB_RDATA_CORRUPT_FREQ_MASKFULL (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_TB_RDATA_CORRUPT_FREQ_FREQ_SHIFT (0U)
#define RGX_TB_RDATA_CORRUPT_FREQ_FREQ_CLRMSK (0x00000000U)

/*
    Register RGX_TB_TRUSTED_DEVICE
*/
#define RGX_TB_TRUSTED_DEVICE (0x2000U)
#define RGX_TB_TRUSTED_DEVICE_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_TB_TRUSTED_DEVICE_ALLOW_SECURE_READS_SHIFT (4U)
#define RGX_TB_TRUSTED_DEVICE_ALLOW_SECURE_READS_CLRMSK (0xFFFFFFEFU)
#define RGX_TB_TRUSTED_DEVICE_ALLOW_SECURE_READS_EN (0x00000010U)
#define RGX_TB_TRUSTED_DEVICE_HWCONFIG_SHIFT (2U)
#define RGX_TB_TRUSTED_DEVICE_HWCONFIG_CLRMSK (0xFFFFFFF3U)
#define RGX_TB_TRUSTED_DEVICE_OSID_DISABLE_SHIFT (1U)
#define RGX_TB_TRUSTED_DEVICE_OSID_DISABLE_CLRMSK (0xFFFFFFFDU)
#define RGX_TB_TRUSTED_DEVICE_OSID_DISABLE_EN (0x00000002U)
#define RGX_TB_TRUSTED_DEVICE_ENABLE_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_TRUSTED_DEVICE_ENABLE_EN (0x00000001U)

/*
    Register RGX_TB_TRUSTED_DEVICE_FAULT_STATUS
*/
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS (0x2008U)
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_MASKFULL \
	(IMG_UINT64_C(0x01FFFFFFFFFF1771))
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_AXPROT_SHIFT (56U)
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_AXPROT_CLRMSK \
	(IMG_UINT64_C(0xFEFFFFFFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_AXPROT_EN \
	(IMG_UINT64_C(0x0100000000000000))
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_ADDR_SHIFT (16U)
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFF0000000000FFFF))
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_RNW_SHIFT (12U)
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_RNW_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFEFFF))
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_RNW_EN \
	(IMG_UINT64_C(0x0000000000001000))
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_OS_ID_SHIFT (8U)
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_OS_ID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFF8FF))
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_BANK_SHIFT (4U)
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_BANK_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFF8F))
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_VALID_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_VALID_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_TRUSTED_DEVICE_FAULT_STATUS_VALID_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_TB_TRUSTED_DEVICE_FAULT_CLEAR
*/
#define RGX_TB_TRUSTED_DEVICE_FAULT_CLEAR (0x2010U)
#define RGX_TB_TRUSTED_DEVICE_FAULT_CLEAR_MASKFULL \
	(IMG_UINT64_C(0x0000000000000001))
#define RGX_TB_TRUSTED_DEVICE_FAULT_CLEAR_PULSE_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_FAULT_CLEAR_PULSE_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_TRUSTED_DEVICE_FAULT_CLEAR_PULSE_EN (0x00000001U)

/*
    Register group: RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS, with 8 repeats
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS_REPEATCOUNT (8U)
/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS0
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS0 (0x2018U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS0_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS0_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS0_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS1
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS1 (0x2020U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS1_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS1_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS1_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS2
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS2 (0x2028U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS2_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS2_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS2_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS3
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS3 (0x2030U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS3_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS3_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS3_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS4
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS4 (0x2038U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS4_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS4_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS4_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS5
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS5 (0x2040U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS5_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS5_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS5_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS6
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS6 (0x2048U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS6_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS6_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS6_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS7
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS7 (0x2050U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS7_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS7_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MIN_OS7_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register group: RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS, with 8 repeats
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS_REPEATCOUNT (8U)
/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS0
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS0 (0x2058U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS0_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS0_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS0_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS1
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS1 (0x2060U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS1_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS1_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS1_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS2
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS2 (0x2068U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS2_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS2_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS2_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS3
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS3 (0x2070U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS3_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS3_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS3_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS4
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS4 (0x2078U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS4_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS4_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS4_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS5
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS5 (0x2080U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS5_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS5_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS5_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS6
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS6 (0x2088U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS6_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS6_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS6_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS7
*/
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS7 (0x2090U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS7_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS7_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION0_MAX_OS7_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register group: RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS, with 8 repeats
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS_REPEATCOUNT (8U)
/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS0
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS0 (0x2098U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS0_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS0_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS0_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS1
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS1 (0x20A0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS1_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS1_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS1_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS2
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS2 (0x20A8U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS2_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS2_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS2_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS3
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS3 (0x20B0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS3_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS3_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS3_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS4
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS4 (0x20B8U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS4_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS4_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS4_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS5
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS5 (0x20C0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS5_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS5_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS5_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS6
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS6 (0x20C8U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS6_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS6_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS6_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS7
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS7 (0x20D0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS7_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS7_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MIN_OS7_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register group: RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS, with 8 repeats
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS_REPEATCOUNT (8U)
/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS0
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS0 (0x20D8U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS0_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS0_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS0_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS1
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS1 (0x20E0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS1_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS1_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS1_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS2
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS2 (0x20E8U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS2_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS2_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS2_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS3
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS3 (0x20F0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS3_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS3_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS3_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS4
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS4 (0x20F8U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS4_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS4_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS4_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS5
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS5 (0x2100U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS5_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS5_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS5_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS6
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS6 (0x2108U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS6_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS6_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS6_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS7
*/
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS7 (0x2110U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS7_MASKFULL \
	(IMG_UINT64_C(0x000000FFFFFFFFFF))
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS7_ADDR_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_REGION1_MAX_OS7_ADDR_CLRMSK \
	(IMG_UINT64_C(0xFFFFFF0000000000))

/*
    Register RGX_TB_BW_LIMITER
*/
#define RGX_TB_BW_LIMITER (0x2118U)
#define RGX_TB_BW_LIMITER_MASKFULL (IMG_UINT64_C(0x00000000007707FF))
#define RGX_TB_BW_LIMITER_DROPN_EXT_SHIFT (20U)
#define RGX_TB_BW_LIMITER_DROPN_EXT_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFF8FFFFF))
#define RGX_TB_BW_LIMITER_PERIOD_EXT_SHIFT (16U)
#define RGX_TB_BW_LIMITER_PERIOD_EXT_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFF8FFFF))
#define RGX_TB_BW_LIMITER_DROPN_SHIFT (6U)
#define RGX_TB_BW_LIMITER_DROPN_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFF83F))
#define RGX_TB_BW_LIMITER_PERIOD_SHIFT (1U)
#define RGX_TB_BW_LIMITER_PERIOD_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFC1))
#define RGX_TB_BW_LIMITER_ENABLE_SHIFT (0U)
#define RGX_TB_BW_LIMITER_ENABLE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_BW_LIMITER_ENABLE_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_TB_TRUSTED_DEVICE_ACECONFIG
*/
#define RGX_TB_TRUSTED_DEVICE_ACECONFIG (0x2120U)
#define RGX_TB_TRUSTED_DEVICE_ACECONFIG_MASKFULL \
	(IMG_UINT64_C(0x00000000000001FF))
#define RGX_TB_TRUSTED_DEVICE_ACECONFIG_OSID_SECURITY_SHIFT (1U)
#define RGX_TB_TRUSTED_DEVICE_ACECONFIG_OSID_SECURITY_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFE01))
#define RGX_TB_TRUSTED_DEVICE_ACECONFIG_ENABLE_SHIFT (0U)
#define RGX_TB_TRUSTED_DEVICE_ACECONFIG_ENABLE_CLRMSK \
	(IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_TRUSTED_DEVICE_ACECONFIG_ENABLE_EN \
	(IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_TB_DRAM_CROSSBAR
*/
#define RGX_TB_DRAM_CROSSBAR (0x2128U)
#define RGX_TB_DRAM_CROSSBAR_MASKFULL (IMG_UINT64_C(0x0000000000003301))
#define RGX_TB_DRAM_CROSSBAR_CHANNELS_SHIFT (12U)
#define RGX_TB_DRAM_CROSSBAR_CHANNELS_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFCFFF))
#define RGX_TB_DRAM_CROSSBAR_SEL_MODE_SHIFT (8U)
#define RGX_TB_DRAM_CROSSBAR_SEL_MODE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFCFF))
#define RGX_TB_DRAM_CROSSBAR_ENABLE_SHIFT (0U)
#define RGX_TB_DRAM_CROSSBAR_ENABLE_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_DRAM_CROSSBAR_ENABLE_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_TB_LOCKUP
*/
#define RGX_TB_LOCKUP (0x2130U)
#define RGX_TB_LOCKUP_MASKFULL (IMG_UINT64_C(0xFFFFFFFF00000001))
#define RGX_TB_LOCKUP_CYCLES_BEFORE_SHIFT (32U)
#define RGX_TB_LOCKUP_CYCLES_BEFORE_CLRMSK (IMG_UINT64_C(0x00000000FFFFFFFF))
#define RGX_TB_LOCKUP_ENABLE_DETECT_SHIFT (0U)
#define RGX_TB_LOCKUP_ENABLE_DETECT_CLRMSK (IMG_UINT64_C(0xFFFFFFFFFFFFFFFE))
#define RGX_TB_LOCKUP_ENABLE_DETECT_EN (IMG_UINT64_C(0x0000000000000001))

/*
    Register RGX_TB_SOC_TIMER
*/
#define RGX_TB_SOC_TIMER (0x2140U)
#define RGX_TB_SOC_TIMER_MASKFULL (IMG_UINT64_C(0xFFFFFFFFFFFFFFFF))
#define RGX_TB_SOC_TIMER_COUNT_SHIFT (0U)
#define RGX_TB_SOC_TIMER_COUNT_CLRMSK (IMG_UINT64_C(0x0000000000000000))

/*
    Register RGX_TB_PROGRAMABLE_CLK_DIV
*/
#define RGX_TB_PROGRAMABLE_CLK_DIV (0x2150U)
#define RGX_TB_PROGRAMABLE_CLK_DIV_MASKFULL (IMG_UINT64_C(0x0000000000000FFF))
#define RGX_TB_PROGRAMABLE_CLK_DIV_MODE_SHIFT (11U)
#define RGX_TB_PROGRAMABLE_CLK_DIV_MODE_CLRMSK (0xFFFFF7FFU)
#define RGX_TB_PROGRAMABLE_CLK_DIV_MODE_EN (0x00000800U)
#define RGX_TB_PROGRAMABLE_CLK_DIV_PROFILE_SEL_SHIFT (9U)
#define RGX_TB_PROGRAMABLE_CLK_DIV_PROFILE_SEL_CLRMSK (0xFFFFF9FFU)
#define RGX_TB_PROGRAMABLE_CLK_DIV_DIV_SHIFT (5U)
#define RGX_TB_PROGRAMABLE_CLK_DIV_DIV_CLRMSK (0xFFFFFE1FU)
#define RGX_TB_PROGRAMABLE_CLK_DIV_DELAY_SHIFT (1U)
#define RGX_TB_PROGRAMABLE_CLK_DIV_DELAY_CLRMSK (0xFFFFFFE1U)
#define RGX_TB_PROGRAMABLE_CLK_DIV_EVENT_SHIFT (0U)
#define RGX_TB_PROGRAMABLE_CLK_DIV_EVENT_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_PROGRAMABLE_CLK_DIV_EVENT_EN (0x00000001U)

/*
    Register RGX_TB_GPIO_FREQ_CTRL
*/
#define RGX_TB_GPIO_FREQ_CTRL (0x2160U)
#define RGX_TB_GPIO_FREQ_CTRL_MASKFULL (IMG_UINT64_C(0x000000000000001F))
#define RGX_TB_GPIO_FREQ_CTRL_COUNT_SHIFT (1U)
#define RGX_TB_GPIO_FREQ_CTRL_COUNT_CLRMSK (0xFFFFFFE1U)
#define RGX_TB_GPIO_FREQ_CTRL_ENABLE_SHIFT (0U)
#define RGX_TB_GPIO_FREQ_CTRL_ENABLE_CLRMSK (0xFFFFFFFEU)
#define RGX_TB_GPIO_FREQ_CTRL_ENABLE_EN (0x00000001U)

/*
    Register RGX_TB_GPIO_MODE
*/
#define RGX_TB_GPIO_MODE (0x2170U)
#define RGX_TB_GPIO_MODE_MASKFULL (IMG_UINT64_C(0x0000000000000003))
#define RGX_TB_GPIO_MODE_PROTOCOL_SHIFT (0U)
#define RGX_TB_GPIO_MODE_PROTOCOL_CLRMSK (0xFFFFFFFCU)

#endif /* RGXTBDEFS_KM_H */
/*****************************************************************************
 End of file (rgxtbdefs_km.h)
*****************************************************************************/
