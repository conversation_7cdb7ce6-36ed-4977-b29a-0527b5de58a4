#
# Jailhouse, a Linux-based partitioning hypervisor
#
# Copyright (c) Siemens AG, 2013-2017
# Copyright (c) <PERSON>, 2014
#
# Authors: <AUTHORS>
#  <PERSON> <<EMAIL>>
#
# This work is licensed under the terms of the GNU GPL, version 2.  See
# the COPYING file in the top-level directory.
#
# asm-defines.h generation code derived from linux/Kbuild:
#
# Copyright (c) Linux kernel developers, 2014
#

include $(ALWAYS_COMPAT_MK)

-include $(GEN_CONFIG_MK)

LINUXINCLUDE := -I$(src)/arch/$(SRCARCH)/include \
		-I$(src)/arch/$(SRCARCH)/include/generated \
		-I$(src)/include \
		-I$(src)/../include/arch/$(SRCARCH) \
		-I$(src)/../include
KBUILD_AFLAGS := -D__ASSEMBLY__ -fno-PIE
KBUILD_CFLAGS := -g -Os -Werror -Wall -Wextra -Wno-unused-parameter \
		 -Wstrict-prototypes -Wtype-limits \
		 -Wmissing-declarations -Wmissing-prototypes \
		 -Wnested-externs -Wshadow -Wredundant-decls \
		 -Wundef -Wdeprecated \
		 -fno-strict-aliasing -fno-pic -fno-common \
		 -fno-stack-protector -fno-builtin-ffsl -ffreestanding \
		 -D__LINUX_COMPILER_TYPES_H

include $(src)/arch/$(SRCARCH)/Makefile

ifneq ($(wildcard $(INC_CONFIG_H)),)
KBUILD_CFLAGS += -include $(INC_CONFIG_H)
endif

CORE_OBJECTS = setup.o printk.o paging.o control.o lib.o mmio.o pci.o ivshmem.o
CORE_OBJECTS += uart.o uart-8250.o

ifdef CONFIG_JAILHOUSE_GCOV
CORE_OBJECTS += gcov.o
endif
ccflags-$(CONFIG_JAILHOUSE_GCOV) += -fprofile-arcs -ftest-coverage
clean-files += *.gcda arch/*/.*.gcda

CLEAN_DIRS := arch/$(SRCARCH)/include/generated

ifeq ($(shell test $(VERSION) -ge 5 && test $(PATCHLEVEL) -ge 4 && echo 1),1)
clean-files += $(CLEAN_DIRS)
else
clean-dirs += $(CLEAN_DIRS)
endif

define sed-y
	"/^=>/{s:=>#\(.*\):/* \1 */:; \
	s:^=>\([^ ]*\) [\$$#]*\([-0-9]*\) \(.*\):#define \1 \2 /* \3 */:; \
	s:^=>\([^ ]*\) [\$$#]*\([^ ]*\) \(.*\):#define \1 \2 /* \3 */:; \
	s:=>::; p;}"
endef

quiet_cmd_defines = GEN     $@
define cmd_defines
	(set -e; \
	 echo "#ifndef _GENERATED_ASM_DEFINES_H"; \
	 echo "#define _GENERATED_ASM_DEFINES_H"; \
	 echo "/*"; \
	 echo " * This file is autogenerated. If you need to change it,"; \
	 echo " * edit arch/$(SRCARCH)/asm-defines.c instead."; \
	 echo " *"; \
	 echo " * ALL MANUAL CHANGES TO THIS FILE WILL BE LOST!"; \
	 echo " */"; \
	 echo ""; \
	 sed -ne $(sed-y) $<; \
	 echo ""; \
	 echo "#endif" ) > $@
endef

ASM_DEFINES_H := arch/$(SRCARCH)/include/generated/asm/asm-defines.h

targets := $(ASM_DEFINES_H) arch/$(SRCARCH)/asm-defines.s

$(obj)/arch/$(SRCARCH)/asm-defines.s: $(src)/arch/$(SRCARCH)/asm-defines.c FORCE
	$(call if_changed_dep,cc_s_c)

$(obj)/$(ASM_DEFINES_H): $(obj)/arch/$(SRCARCH)/asm-defines.s
	$(Q)mkdir -p $(dir $@)
	$(call cmd,defines)

# Do not generate files by creating dependencies if we are cleaning up
ifeq ($(filter %/Makefile.clean,$(MAKEFILE_LIST)),)
$(obj)/arch/$(SRCARCH): $(obj)/$(ASM_DEFINES_H)
endif

always-y :=

subdir-y := arch/$(SRCARCH)

define BUILD_JAILHOUSE_template
always-y += jailhouse$(1).bin

$$(obj)/arch/$$(SRCARCH)/lib$(1).a: $$(obj)/arch/$$(SRCARCH)
	@true

hypervisor$(1)-y := arch/$$(SRCARCH)/lib$(1).a $$(CORE_OBJECTS) hypervisor.lds
targets += $$(hypervisor$(1)-y)

HYPERVISOR$(1)_OBJS = $$(addprefix $$(obj)/,$$(hypervisor$(1)-y))

LDFLAGS_hypervisor$(1).o := --whole-archive -T

targets += hypervisor$(1).o
$$(obj)/hypervisor$(1).o: $$(src)/hypervisor.lds $$(HYPERVISOR$(1)_OBJS) FORCE
	$$(call if_changed,ld)

OBJCOPYFLAGS_jailhouse$(1).bin := -O binary -R .eh_frame

targets += jailhouse$(1).bin
$$(obj)/jailhouse$(1).bin: $$(obj)/hypervisor$(1).o FORCE
	$$(call if_changed,objcopy)
endef

ifneq ($(BUILD_VARIANTS),)
$(foreach variant,$(BUILD_VARIANTS),\
	$(eval $(call BUILD_JAILHOUSE_template,-$(variant))))
else
$(eval $(call BUILD_JAILHOUSE_template,))
endif
