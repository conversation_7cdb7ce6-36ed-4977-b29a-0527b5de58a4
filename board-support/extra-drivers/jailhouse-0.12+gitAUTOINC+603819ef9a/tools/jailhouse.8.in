'\" t
.\"     Title: jailhouse
.\"    Author: [see the "Authors" section]
.\"      Date: 14/04/2018
.\"    Manual: Jailhouse Manual
.\"    Source: Git 0.8
.\"  Language: English
.\"
.TH "JAILHOUSE" "8" "14/04/2018" "Jailhouse ${VERSION}" "Jailhouse Manual"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
jailhouse \- the lightweight partitioning hypervisor
.SH "SYNOPSIS"
.sp
.nf
\fIjailhouse\fR <command> [<args>]
.fi
.sp
.SH "DESCRIPTION"
.sp
Jailhouse is a partitioning Hypervisor based on Linux\&. It is able to run bare-metal applications or (adapted) operating systems besides Linux\&. For this purpose, it configures CPU and device virtualization features of the hardware platform in a way that none of these domains, called "cells" here, can interfere with each other in an unacceptable way\&.
.sp
Jailhouse is optimized for simplicity rather than feature richness\&. Unlike full-featured Linux-based hypervisors like KVM or Xen, Jailhouse does not support overcommitment of resources like CPUs, RAM or devices\&. It performs no scheduling and only virtualizes those resources in software, that are essential for a platform and cannot be partitioned in hardware\&.
.sp
Once Jailhouse is activated, it runs bare-metal, i\&.e\&. it takes full control over the hardware and needs no external support\&. However, in contrast to other bare-metal hypervisors, it is loaded and configured by a normal Linux system\&. Its management interface is based on Linux infrastructure\&. So you boot Linux first, then you enable Jailhouse and finally you split off parts of the system's resources and assign them to additional cells\&.
.SH "USAGE FLOW"
.sp
Once the jailhouse\&.ko driver is active in the kernel, Jailhouse has to be enabled with the following command:
.sp
.RS
\fIjailhouse enable\fR <sysconfig.cell>
.sp
This activates the hypervisor and wraps the executing Linux execution environment into a cell called the "root cell"\&.  It is then  possible to create and tear down cells with jailhouse cell commands\&.  <sysconfig.cell> is a Jailhouse binary configuration file that describe all present hardware but the hardware devices destined to future cells\&.
.sp
.RE
Jailhouse enabled, then it is possible to create and terminate cells with the following set of commands:
.sp
.RS 4
.nf
\fIjailhouse cell create\fR -name <cellname> <cellconfig.cell>
\fIjailhouse cell load\fR -name <cellname> <args>
\fIjailhouse cell start\fR -name <cellname>
\fIjailhouse cell destroy\fR -name <cellname>
.fi
.RE
.sp
To terminate jailhouse alltogether, all cells must be destroyed and then hypervisor itself terminated with:
.sp
.RS
\fIjailhouse disable\fR
.sp
This unwraps the root cell into a bare metal environment\&. The jalhouse\&.ko driver can be unloaded once Jailhouse has been disabled\&.
.RE
.SH "JAILHOUSE COMMANDS"
.sp
.PP
\fBjailhouse-cell\fR(8)
.PP
\fBjailhouse-console\fR(8)
.PP
\fBjailhouse-disable\fR(8)
.PP
\fBjailhouse-enable\fR(8)
.PP
\fBjailhouse-hardware\fR(8)
.SH "SEE ALSO"
jailhouse-cell(8) jailhouse-enable(8) jailhouse.ko(8)
.SH "AUTHORS"
.sp
Jailhouse was started by Jan Kiszka\&. Contributions have come from the Jailhouse mailing list <\m[blue]\fBjailhouse\-dev@googlegroups\&.com\fR\m[]\&\s-2\u\d\s+2>\&.
.sp
If you have a clone of jailhouse\&.git itself, the output of \fBgit-shortlog\fR(1) and \fBgit-blame\fR(1) can show you the authors for specific parts of the project\&.
.SH "REPORTING BUGS"
.sp
Report bugs to the Jailhouse mailing list <\m[blue]\fBjailhouse\-dev@googlegroups\&.com\fR\m[]\&\s-2\u\d\s+2> where the development and maintenance is primarily done\&. You do not have to be subscribed to the list to send a message there\&.
