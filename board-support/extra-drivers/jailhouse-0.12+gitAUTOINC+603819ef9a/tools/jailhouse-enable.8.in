'\" t
.\"     Title: jailhouse
.\"    Author: [see the "Authors" section]
.\"      Date: 14/04/2018
.\"    Manual: Jailhouse Manual
.\"    Source: Git 0.8
.\"  Language: English
.\"
.TH "JAILHOUSE-ENABLE" "8" "14/04/2018" "Jailhouse ${VERSION}" "Jailhouse Manual"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
jailhouse-enable \- start the lightweight partitioning hypervisor and wraps the running Linux into the root-cell
.SH "SYNOPSIS"
.sp
.nf
\fIjailhouse enable\fR <sysconfig.cell>
.fi
.sp
.SH "DESCRIPTION"
Once the jailhouse\&.ko driver is active in the kernel, Jailhouse has to be enabled:
.sp
.RS
\fIjailhouse enable\fR <sysconfig.cell>
.sp
<sysconfig.cell> is a Jailhouse binary configuration file that describe all present hardware or the necessary hardware for the root cell to be operational\&. This binary configuration file is obtained by compiling a config file in C language format. On x86, the following command can be used to generate a C language configuration file that represent all known hardware:
.sp
.RS
\fIjailhouse config create\fR <sysconfig.c>
.sp
From this file, the system administrator can remove all hardware that should be dedicated to future cells. Simplest way to compile this file into a <sysconfig.cell> is to copy it in <path to configs/x86/ directory> and launch a build\&.
.RE
.sp
.RE
.PP
.RE
.SH "SEE ALSO"
jailhouse(8) jailhouse-cell(8) jailhouse.ko(8)
.SH "AUTHORS"
.sp
Jailhouse was started by Jan Kiszka\&. Contributions have come from the Jailhouse mailing list <\m[blue]\fBjailhouse\-dev@googlegroups\&.com\fR\m[]\&\s-2\u\d\s+2>\&.
.sp
If you have a clone of jailhouse\&.git itself, the output of \fBgit-shortlog\fR(1) and \fBgit-blame\fR(1) can show you the authors for specific parts of the project\&.
.SH "REPORTING BUGS"
.sp
Report bugs to the Jailhouse mailing list <\m[blue]\fBjailhouse\-dev@googlegroups\&.com\fR\m[]\&\s-2\u\d\s+2> where the development and maintenance is primarily done\&. You do not have to be subscribed to the list to send a message there\&.
