'\" t
.\"     Title: jailhouse
.\"    Author: [see the "Authors" section]
.\"      Date: 14/04/2018
.\"    Manual: Jailhouse Manual
.\"    Source: Git 0.8
.\"  Language: English
.\"
.TH "JAILHOUSE-CELL" "8" "14/04/2018" "Jailhouse ${VERSION}" "Jailhouse Manual"
.\" -----------------------------------------------------------------
.\" * Define some portability stuff
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" http://bugs.debian.org/507673
.\" http://lists.gnu.org/archive/html/groff/2009-02/msg00013.html
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "NAME"
jailhouse-cell \- controlling cells
.SH "SYNOPSIS"
.sp
.nf
\fIjailhouse\fR cell [collect | create | destroy | linux | load | shutdown | start | stats] [<args>]
.fi
.sp
.SH "DESCRIPTION"
.sp
.PP
\fBjailhouse cell load\fR { ID | [--name] NAME }  { <image_information> } ...
.RS 4
.sp
Where <image_information> is { IMAGE | { -s | --string } "STRING" } [-a | --address ADDRESS]}
.RE
.RS 4
.sp
Valid forms are:
.sp
    # loads inmate\&.bin (offset 0 assumed)
    jailhouse cell load foocell inmate\&.bin
.sp
    # same as above with explicit location
    jailhouse cell load foocell inmate\&.bin -a 0
.sp
    # loads three binary objects (in order)
    jailhouse cell load foocell \\
        inmate\&.bin \\
        sharedobject\&.so -a 0x1000000 \\
        ramfs\&.bin -a 0x2000000
.RE
.RS 4
.sp
If no address is specified (e.g., inmate\&.bin in the above example), the
address will default to 0x0.
.sp
The last example, loads in the order specified, three binary objects,
the first one at offset 0, the second one at 0x1000000\&.
Should inmate.bin be larger than 0x1000000, the upper part will be overridden
by sharedobject\&.so\&.
.sp
Whatever load order, execution starts in the cell at offset 0 unless otherwise
specified in the cell config (cpu_reset_address).
.sp
This multi-image loading capability can be used to patch images and
pass parameters to the image\&. The following explains how parameters are passed
with the inmate library\&.
.sp
The inmate library assumes a command line string to be located at a fixed
location. On all architectures, this is offset 0x1000 (see
inmates/lib/ARCH/inmate\&.lds[.S])
.RE
.RS 4
.sp
The command line string capacity is defined during compile time by CMDLINE_BUFFER_SIZE
in inmates/lib/cmdline\&.c or by defining a non weak instance of CMDLINE_BUFFER()\&.
Please note that capacity includes trailing \\0.
.sp
Here is an example to pass parameters stored in the file commandline.txt to the
inmate inmate\&.bin:
.sp
    CMDLINE_OFFSET=0x1000
    jailhouse cell load foocell \\
        inmate\&.bin \\
        commandline\&.txt -a $CMDLINE_OFFSET \\
        sharedobject\&.so -a 0x1000000 \\
        ramfs\&.bin -a 0x2000000
.sp
This command patches inmate.bin at offset 0x1000 that corresponds to the "char
*cmdline" location as controlled by the link script for inmates\&.
.sp
To be more practical and avoid using a text file, there is an image-as-string
option:
.sp
    CMDLINE_OFFSET=0x1000
    jailhouse cell load foocell \\
        inmate\&.bin \\
        -s "<command line parameters here>" -a $CMDLINE_OFFSET \\
        sharedobject\&.so -a 0x1000000 \\
        ramfs\&.bin -a 0x2000000
.sp

.RE

.SH "SEE ALSO"
jailhouse(8) jailhouse-enable(8) jailhouse.ko(8)
.SH "AUTHORS"
.sp
Jailhouse was started by Jan Kiszka\&. Contributions have come from the Jailhouse mailing list <\m[blue]\fBjailhouse\-dev@googlegroups\&.com\fR\m[]\&\s-2\u\d\s+2>\&.
.sp
If you have a clone of jailhouse\&.git itself, the output of \fBgit-shortlog\fR(1) and \fBgit-blame\fR(1) can show you the authors for specific parts of the project\&.
.SH "REPORTING BUGS"
.sp
Report bugs to the Jailhouse mailing list <\m[blue]\fBjailhouse\-dev@googlegroups\&.com\fR\m[]\&\s-2\u\d\s+2> where the development and maintenance is primarily done\&. You do not have to be subscribed to the list to send a message there\&.
