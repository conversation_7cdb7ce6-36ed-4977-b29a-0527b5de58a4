#!/bin/bash
#
# Jailhouse, a Linux-based partitioning hypervisor
#
# Copyright (c) Siemens AG, 2014
#
# Authors: <AUTHORS>
#
# This work is licensed under the terms of the GNU GPL, version 2.  See
# the COPYING file in the top-level directory.
#

if [ "$ARCH" == "" ]; then
	ARCH=x86
fi

INCLUDE_ARM_COMMON=
if [ "$ARCH" == "arm" ] || [ "$ARCH" == "arm64" ]; then
	INCLUDE_ARM_COMMON=-Ihypervisor/arch/arm-common/include
fi

CFLAGS="-fno-builtin-ffsl -Wall -Wstrict-prototypes -Wtype-limits \
	-Wmissing-declarations -Wmissing-prototypes \
	-Ihypervisor/arch/$ARCH/include -Ihypervisor/include \
	-Iinclude/arch/$ARCH -Iinclude \
	$INCLUDE_ARM_COMMON $EXTRA_CFLAGS"

test_compile()
{
	header=`basename $2`
	prepend=
	case $header in
	cell-config.h|console.h|header.h|hypercall.h)
		prepend="#include <jailhouse/types.h>"
		;;
	jailhouse_hypercall.h)
		# only included directly on arm64
		if [ "$ARCH" != "arm64" ]; then
			return;
		fi
		prepend="#define __ASSEMBLY__
			 #include <jailhouse/types.h>"
		;;
	bitops.h)
		if [ "$1" == "asm" ]; then
			# must be included by jailhouse/bitops.h only
			return
		fi
		;;
	ivshmem.h)
		if [ "$1" == "asm" ]; then
			# must be included by jailhouse/ivshmem.h only
			return
		fi
		;;
	traps.h)
		if [ "$2" == "hypervisor/arch/arm-common/include/asm/traps.h" ]; then
			# must be included by arm{,64}/include/asm/traps.h only
			return
		fi
		;;
	esac

	echo "$prepend" > .header_check.tmp.c
	echo "#include <$1/$header>" >> .header_check.tmp.c

	if ! ${CROSS_COMPILE}gcc -c -o .header_check.tmp.o .header_check.tmp.c $CFLAGS; then
		exit 1;
	fi
	echo $1/$header - OK
}

for header in hypervisor/include/jailhouse/*.h; do
	test_compile jailhouse $header
done

for header in hypervisor/arch/$ARCH/include/asm/*.h; do
	test_compile asm $header
done

if [ "$ARCH" == "arm" -o "$ARCH" == "arm64" ]; then
	for header in hypervisor/arch/arm-common/include/asm/*.h; do
		test_compile asm $header
	done
fi

for header in include/jailhouse/*.h; do
	test_compile jailhouse $header
done

for header in include/arch/$ARCH/asm/*.h; do
	test_compile asm $header
done

rm -f .header_check.tmp.[oc]
