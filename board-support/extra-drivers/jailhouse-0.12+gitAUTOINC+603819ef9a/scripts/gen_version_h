#!/bin/sh
#
# Jailhouse, a Linux-based partitioning hypervisor
#
# Copyright (c) Siemens AG, 2014
#
# Authors: <AUTHORS>
#
# This work is licensed under the terms of the GNU GPL, version 2.  See
# the COPYING file in the top-level directory.
#

echo "/* Auto-generated - leave alone and don't commit! */"
echo ""

cd "$1" > /dev/null

if ! git rev-parse 2>/dev/null; then
	version="`cat VERSION`"
else
	describe="`git describe --long --dirty --match "v[0-9].[0-9]*"`"
	version="`echo $describe | sed -e 's/\([^-]*\)-\(.*\)/\1 (\2)/'`"
fi

cd - > /dev/null

echo "#define JAILHOUSE_VERSION	\"$version\""
