#!/bin/bash
#
# Jailhouse, a Linux-based partitioning hypervisor
#
# Copyright (c) OTH Regensburg, 2019
#
# Authors: <AUTHORS>
#
# This work is licensed under the terms of the GNU GPL, version 2.  See
# the COPYING file in the top-level directory.

CELL_CONFIG_H=$1

function find_defines() {
	header=$1
	prefix=$2
	search="#define\s\+${prefix}\(\S*\)\s\+\(\S*\).*"
	replace="        '\1'\t: \2,"

	grep $prefix $header | sed -e "s/$search/$replace/"
}

PCI_CAP_IDS=$(find_defines $CELL_CONFIG_H PCI_CAP_ID_)
PCI_EXT_CAP_IDS=$(find_defines $CELL_CONFIG_H PCI_EXT_CAP_ID_)

cat <<END
# This file is autogenerated. If you need to change it, edit
# scripts/gen_pci_defs.sh instead.

from .extendedenum import ExtendedEnum


class PCI_CAP_ID(ExtendedEnum):
    _ids = {
${PCI_CAP_IDS}
    }


class PCI_EXT_CAP_ID(ExtendedEnum):
    _ids = {
${PCI_EXT_CAP_IDS}
    }
END
