/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "AM62x" --package "ALW" --part "Default" --product "K3-Respart-Tool@0.5"
 * @versions {"tool":"1.19.0+3426"}
 */

/**
 * Import the modules used in this configuration.
 */
const A53_2            = scripting.addModule("/modules/am62x/A53_2");
const M4_0             = scripting.addModule("/modules/am62x/M4_0");
const MAIN_0_R5_1      = scripting.addModule("/modules/am62x/MAIN_0_R5_1");
const resourceSharing  = scripting.addModule("/modules/resourceSharing", {}, false);
const resourceSharing1 = resourceSharing.addInstance();
const resourceSharing2 = resourceSharing.addInstance();
const resourceSharing3 = resourceSharing.addInstance();
const resourceSharing4 = resourceSharing.addInstance();
const resourceSharing5 = resourceSharing.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
A53_2.allocOrder                                          = 1;
A53_2.Compare_event_Interrupt_Router_count                = 16;
A53_2.DMASS_Interrupt_aggregator_Global_events_count      = 512;
A53_2.DMASS_Packet_DMA_Rings_for_SA2UL_Tx_channel1_count  = 8;
A53_2.DMASS_Packet_DMA_Rings_for_SA2UL_Rx_channel2_count  = 8;
A53_2.DMASS_Packet_DMA_Rings_for_SA2UL_Rx_channel3_count  = 8;
A53_2.DMASS_Packet_DMA_SA2UL_Tx_channel1_count            = 1;
A53_2.DMASS_Packet_DMA_SA2UL_Rx_channel2_count            = 1;
A53_2.DMASS_Packet_DMA_SA2UL_Rx_channel3_count            = 1;
A53_2.DMASS_Packet_DMA_virt_id_range_start                = 2;
A53_2.DMASS_Packet_DMA_virt_id_range_count                = 2;
A53_2.DMASS_Packet_DMA_Rings_for_CPSW_Tx_channel_count    = 64;
A53_2.DMASS_Packet_DMA_Rings_for_CPSW_Rx_channel_count    = 16;
A53_2.Block_Copy_DMA_Block_copy_channel_count             = 18;
A53_2.Block_Copy_DMA_Split_TR_Tx_channel_count            = 12;
A53_2.Block_Copy_DMA_Split_TR_Rx_channel_count            = 18;
A53_2.DMASS_Packet_DMA_Free_Tx_channel_count              = 10;
A53_2.DMASS_Packet_DMA_Free_Rx_channel_count              = 10;
A53_2.DMASS_Packet_DMA_CPSW_Tx_channel_count              = 8;
A53_2.DMASS_Packet_DMA_CPSW_Rx_channel_count              = 1;
A53_2.DMASS_Packet_DMA_SA2UL_Tx_channel0_count            = 1;
A53_2.DMASS_Packet_DMA_SA2UL_Rx_channel0_count            = 1;
A53_2.DMASS_Packet_DMA_SA2UL_Rx_channel1_count            = 1;
A53_2.DMASS_Packet_DMA_Rings_for_SA2UL_Rx_channel0_count  = 8;
A53_2.DMASS_Packet_DMA_Rings_for_SA2UL_Tx_channel0_count  = 8;
A53_2.DMASS_Packet_DMA_Rings_for_SA2UL_Rx_channel1_count  = 8;
A53_2.MAIN_GPIO_Interrupt_Router_count                    = 16;
A53_2.DMASS_Interrupt_aggregator_Virtual_interrupts_count = 35;
A53_2.WKUP_MCU_GPIO_Interrupt_Router_count                = 2;

M4_0.allocOrder                                          = 3;
M4_0.Block_Copy_DMA_Block_copy_channel_count             = 2;
M4_0.Block_Copy_DMA_Split_TR_Rx_channel_count            = 2;
M4_0.Block_Copy_DMA_Split_TR_Tx_channel_count            = 2;
M4_0.DMASS_Packet_DMA_Free_Tx_channel_count              = 3;
M4_0.DMASS_Packet_DMA_Free_Rx_channel_count              = 3;
M4_0.Compare_event_Interrupt_Router_count                = 22;
M4_0.MAIN_GPIO_Interrupt_Router_count                    = 2;
M4_0.WKUP_MCU_GPIO_Interrupt_Router_count                = 4;
M4_0.DMASS_Interrupt_aggregator_Global_events_count      = 128;
M4_0.DMASS_Interrupt_aggregator_Virtual_interrupts_count = 8;

MAIN_0_R5_1.allocOrder                                          = 2;
MAIN_0_R5_1.DMASS_Interrupt_aggregator_Global_events_count      = 256;
MAIN_0_R5_1.shareResource                                       = "MAIN_0_R5_0";
MAIN_0_R5_1.Block_Copy_DMA_Block_copy_channel_count             = 6;
MAIN_0_R5_1.Block_Copy_DMA_Split_TR_Rx_channel_count            = 6;
MAIN_0_R5_1.Block_Copy_DMA_Split_TR_Tx_channel_count            = 6;
MAIN_0_R5_1.DMASS_Packet_DMA_Free_Tx_channel_count              = 3;
MAIN_0_R5_1.DMASS_Packet_DMA_Free_Rx_channel_count              = 3;
MAIN_0_R5_1.Compare_event_Interrupt_Router_count                = 4;
MAIN_0_R5_1.WKUP_MCU_GPIO_Interrupt_Router_count                = 2;
MAIN_0_R5_1.DMASS_Interrupt_aggregator_Virtual_interrupts_count = 35;

resourceSharing1.$name              = "modules_resourceSharing0";
resourceSharing1.resourceName       = "DMASS Packet DMA CPSW Tx channel";
resourceSharing1.sharedFromHostName = "A53_2";
resourceSharing1.sharedToHostName   = "MAIN_0_R5_1";

resourceSharing2.$name              = "modules_resourceSharing1";
resourceSharing2.resourceName       = "DMASS Packet DMA Rings for CPSW Rx channel";
resourceSharing2.sharedFromHostName = "A53_2";
resourceSharing2.sharedToHostName   = "MAIN_0_R5_1";

resourceSharing3.$name              = "modules_resourceSharing2";
resourceSharing3.resourceName       = "DMASS Packet DMA CPSW Rx flows";
resourceSharing3.sharedFromHostName = "A53_2";
resourceSharing3.sharedToHostName   = "MAIN_0_R5_1";

resourceSharing4.$name              = "modules_resourceSharing3";
resourceSharing4.sharedFromHostName = "A53_2";
resourceSharing4.resourceName       = "DMASS Packet DMA Rings for CPSW Tx channel";
resourceSharing4.sharedToHostName   = "MAIN_0_R5_1";

resourceSharing5.$name              = "modules_resourceSharing4";
resourceSharing5.resourceName       = "DMASS Packet DMA CPSW Rx channel";
resourceSharing5.sharedFromHostName = "A53_2";
resourceSharing5.sharedToHostName   = "MAIN_0_R5_1";
