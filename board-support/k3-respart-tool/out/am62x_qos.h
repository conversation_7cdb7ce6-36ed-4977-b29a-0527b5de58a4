
/* Keystone3 Quality of service endpoint definitions
 * Auto generated by K3 Resource Partitioning Tool
 */

#define QOS_0	(0 << 0)
#define QOS_1	(1 << 0)
#define QOS_2	(2 << 0)
#define QOS_3	(3 << 0)
#define QOS_4	(4 << 0)
#define QOS_5	(5 << 0)
#define QOS_6	(6 << 0)
#define QOS_7	(7 << 0)

#define ORDERID_0	(0 << 4)
#define ORDERID_1	(1 << 4)
#define ORDERID_2	(2 << 4)
#define ORDERID_3	(3 << 4)
#define ORDERID_4	(4 << 4)
#define ORDERID_5	(5 << 4)
#define ORDERID_6	(6 << 4)
#define ORDERID_7	(7 << 4)
#define ORDERID_8	(8 << 4)
#define ORDERID_9	(9 << 4)
#define ORDERID_10	(10 << 4)
#define ORDERID_11	(11 << 4)
#define ORDERID_12	(12 << 4)
#define ORDERID_13	(13 << 4)
#define ORDERID_14	(14 << 4)
#define ORDERID_15	(15 << 4)

#define ASEL_0	(0 << 8)
#define ASEL_1	(1 << 8)
#define ASEL_2	(2 << 8)
#define ASEL_3	(3 << 8)
#define ASEL_4	(4 << 8)
#define ASEL_5	(5 << 8)
#define ASEL_6	(6 << 8)
#define ASEL_7	(7 << 8)
#define ASEL_8	(8 << 8)
#define ASEL_9	(9 << 8)
#define ASEL_10	(10 << 8)
#define ASEL_11	(11 << 8)
#define ASEL_12	(12 << 8)
#define ASEL_13	(13 << 8)
#define ASEL_14	(14 << 8)
#define ASEL_15	(15 << 8)

#define EPRIORITY_0	(0 << 12)
#define EPRIORITY_1	(1 << 12)
#define EPRIORITY_2	(2 << 12)
#define EPRIORITY_3	(3 << 12)
#define EPRIORITY_4	(4 << 12)
#define EPRIORITY_5	(5 << 12)
#define EPRIORITY_6	(6 << 12)
#define EPRIORITY_7	(7 << 12)

#define VIRTID_0	(0 << 16)
#define VIRTID_1	(1 << 16)
#define VIRTID_2	(2 << 16)
#define VIRTID_3	(3 << 16)
#define VIRTID_4	(4 << 16)
#define VIRTID_5	(5 << 16)
#define VIRTID_6	(6 << 16)
#define VIRTID_7	(7 << 16)
#define VIRTID_8	(8 << 16)
#define VIRTID_9	(9 << 16)
#define VIRTID_10	(10 << 16)
#define VIRTID_11	(11 << 16)
#define VIRTID_12	(12 << 16)
#define VIRTID_13	(13 << 16)
#define VIRTID_14	(14 << 16)
#define VIRTID_15	(15 << 16)

#define ATYPE_0	(0 << 28)
#define ATYPE_1	(1 << 28)
#define ATYPE_2	(2 << 28)
#define ATYPE_3	(3 << 28)

#define ICSS_M_MAIN_0_PR1_MST_VBUSP0	0x45D04000
#define ICSS_M_MAIN_0_PR1_MST_VBUSP1	0x45D04400
#define PULSAR_UL_WKUP_0_CPU0_RMST	0x45D14000
#define PULSAR_UL_WKUP_0_CPU0_WMST	0x45D14400
#define PULSAR_UL_WKUP_0_CPU0_PMST	0x45D14800
#define BLAZAR_MCU_0_VBUSP_M	0x45D18000
#define SAM62_A53_512KB_WRAP_MAIN_0_A53_QUAD_WRAP_CBA_AXI_R	0x45D20400
#define SAM62_A53_512KB_WRAP_MAIN_0_A53_QUAD_WRAP_CBA_AXI_W	0x45D20800
#define K3_GPU_AXE116M_MAIN_0_K3_GPU_M_VBUSM_W	0x45D20C00
#define K3_GPU_AXE116M_MAIN_0_K3_GPU_M_VBUSM_R	0x45D21000
#define DEBUGSS_K3_WRAP_CV0_MAIN_0_VBUSMW	0x45D21800
#define DEBUGSS_K3_WRAP_CV0_MAIN_0_VBUSMR	0x45D21C00
#define GIC500SS_1_4_MAIN_0_MEM_WR_VBUSM	0x45D22000
#define GIC500SS_1_4_MAIN_0_MEM_RD_VBUSM	0x45D22400
#define EMMCSD8SS_MAIN_0_EMMCSDSS_RD	0x45D22800
#define EMMCSD8SS_MAIN_0_EMMCSDSS_WR	0x45D22C00
#define EMMCSD4SS_MAIN_0_EMMCSDSS_RD	0x45D23000
#define EMMCSD4SS_MAIN_0_EMMCSDSS_WR	0x45D23400
#define EMMCSD4SS_MAIN_1_EMMCSDSS_WR	0x45D23800
#define EMMCSD4SS_MAIN_1_EMMCSDSS_RD	0x45D23C00
#define USB2SS_16FFC_MAIN_0_MSTW0	0x45D24000
#define USB2SS_16FFC_MAIN_0_MSTR0	0x45D24400
#define USB2SS_16FFC_MAIN_1_MSTR0	0x45D24800
#define USB2SS_16FFC_MAIN_1_MSTW0	0x45D24C00
#define K3_DSS_UL_MAIN_0_VBUSM_DMA	0x45D25000
#define SA3SS_AM62_MAIN_0_CTXCACH_EXT_DMA	0x45D25400
