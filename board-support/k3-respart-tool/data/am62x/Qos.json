[{"asel": true, "atype": false, "baseAddress": "0x45D18000", "channelCount": 1, "deviceName": "BLAZAR_MCU_0", "endpointName": "BLAZAR_MCU_0_VBUSP_M", "epriority": true, "groupCount": 0, "name": "VBUSP_M", "orderId": false, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D21800", "channelCount": 1, "deviceName": "DEBUGSS_K3_WRAP_CV0_MAIN_0", "endpointName": "DEBUGSS_K3_WRAP_CV0_MAIN_0_VBUSMW", "epriority": true, "groupCount": 0, "name": "VBUSMW", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D21C00", "channelCount": 1, "deviceName": "DEBUGSS_K3_WRAP_CV0_MAIN_0", "endpointName": "DEBUGSS_K3_WRAP_CV0_MAIN_0_VBUSMR", "epriority": true, "groupCount": 0, "name": "VBUSMR", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D23000", "channelCount": 1, "deviceName": "EMMCSD4SS_MAIN_0", "endpointName": "EMMCSD4SS_MAIN_0_EMMCSDSS_RD", "epriority": true, "groupCount": 0, "name": "EMMCSDSS_RD", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D23400", "channelCount": 1, "deviceName": "EMMCSD4SS_MAIN_0", "endpointName": "EMMCSD4SS_MAIN_0_EMMCSDSS_WR", "epriority": true, "groupCount": 0, "name": "EMMCSDSS_WR", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D23800", "channelCount": 1, "deviceName": "EMMCSD4SS_MAIN_1", "endpointName": "EMMCSD4SS_MAIN_1_EMMCSDSS_WR", "epriority": true, "groupCount": 0, "name": "EMMCSDSS_WR", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D23C00", "channelCount": 1, "deviceName": "EMMCSD4SS_MAIN_1", "endpointName": "EMMCSD4SS_MAIN_1_EMMCSDSS_RD", "epriority": true, "groupCount": 0, "name": "EMMCSDSS_RD", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D22800", "channelCount": 1, "deviceName": "EMMCSD8SS_MAIN_0", "endpointName": "EMMCSD8SS_MAIN_0_EMMCSDSS_RD", "epriority": true, "groupCount": 0, "name": "EMMCSDSS_RD", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D22C00", "channelCount": 1, "deviceName": "EMMCSD8SS_MAIN_0", "endpointName": "EMMCSD8SS_MAIN_0_EMMCSDSS_WR", "epriority": true, "groupCount": 0, "name": "EMMCSDSS_WR", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D22000", "channelCount": 1, "deviceName": "GIC500SS_1_4_MAIN_0", "endpointName": "GIC500SS_1_4_MAIN_0_MEM_WR_VBUSM", "epriority": true, "groupCount": 0, "name": "MEM_WR_VBUSM", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D22400", "channelCount": 1, "deviceName": "GIC500SS_1_4_MAIN_0", "endpointName": "GIC500SS_1_4_MAIN_0_MEM_RD_VBUSM", "epriority": true, "groupCount": 0, "name": "MEM_RD_VBUSM", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D04000", "channelCount": 1, "deviceName": "ICSS_M_MAIN_0", "endpointName": "ICSS_M_MAIN_0_PR1_MST_VBUSP0", "epriority": true, "groupCount": 0, "name": "PR1_MST_VBUSP0", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D04400", "channelCount": 1, "deviceName": "ICSS_M_MAIN_0", "endpointName": "ICSS_M_MAIN_0_PR1_MST_VBUSP1", "epriority": true, "groupCount": 0, "name": "PR1_MST_VBUSP1", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D25000", "channelCount": 4, "deviceName": "K3_DSS_UL_MAIN_0", "endpointName": "K3_DSS_UL_MAIN_0_VBUSM_DMA", "epriority": false, "groupCount": 0, "name": "VBUSM_DMA", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D20C00", "channelCount": 6, "deviceName": "K3_GPU_AXE116M_MAIN_0", "endpointName": "K3_GPU_AXE116M_MAIN_0_K3_GPU_M_VBUSM_W", "epriority": true, "groupCount": 0, "name": "K3_GPU_M_VBUSM_W", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D21000", "channelCount": 6, "deviceName": "K3_GPU_AXE116M_MAIN_0", "endpointName": "K3_GPU_AXE116M_MAIN_0_K3_GPU_M_VBUSM_R", "epriority": true, "groupCount": 0, "name": "K3_GPU_M_VBUSM_R", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D14000", "channelCount": 1, "deviceName": "PULSAR_UL_WKUP_0", "endpointName": "PULSAR_UL_WKUP_0_CPU0_RMST", "epriority": true, "groupCount": 0, "name": "CPU0_RMST", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D14400", "channelCount": 1, "deviceName": "PULSAR_UL_WKUP_0", "endpointName": "PULSAR_UL_WKUP_0_CPU0_WMST", "epriority": true, "groupCount": 0, "name": "CPU0_WMST", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D14800", "channelCount": 1, "deviceName": "PULSAR_UL_WKUP_0", "endpointName": "PULSAR_UL_WKUP_0_CPU0_PMST", "epriority": true, "groupCount": 0, "name": "CPU0_PMST", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D25400", "channelCount": 1, "deviceName": "SA3SS_AM62_MAIN_0", "endpointName": "SA3SS_AM62_MAIN_0_CTXCACH_EXT_DMA", "epriority": true, "groupCount": 0, "name": "CTXCACH_EXT_DMA", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D20400", "channelCount": 1, "deviceName": "SAM62_A53_512KB_WRAP_MAIN_0", "endpointName": "SAM62_A53_512KB_WRAP_MAIN_0_A53_QUAD_WRAP_CBA_AXI_R", "epriority": true, "groupCount": 0, "name": "A53_QUAD_WRAP_CBA_AXI_R", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D20800", "channelCount": 1, "deviceName": "SAM62_A53_512KB_WRAP_MAIN_0", "endpointName": "SAM62_A53_512KB_WRAP_MAIN_0_A53_QUAD_WRAP_CBA_AXI_W", "epriority": true, "groupCount": 0, "name": "A53_QUAD_WRAP_CBA_AXI_W", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D24000", "channelCount": 1, "deviceName": "USB2SS_16FFC_MAIN_0", "endpointName": "USB2SS_16FFC_MAIN_0_MSTW0", "epriority": true, "groupCount": 0, "name": "MSTW0", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D24400", "channelCount": 1, "deviceName": "USB2SS_16FFC_MAIN_0", "endpointName": "USB2SS_16FFC_MAIN_0_MSTR0", "epriority": true, "groupCount": 0, "name": "MSTR0", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D24800", "channelCount": 1, "deviceName": "USB2SS_16FFC_MAIN_1", "endpointName": "USB2SS_16FFC_MAIN_1_MSTR0", "epriority": true, "groupCount": 0, "name": "MSTR0", "orderId": true, "qos": false, "virtId": false}, {"asel": true, "atype": false, "baseAddress": "0x45D24C00", "channelCount": 1, "deviceName": "USB2SS_16FFC_MAIN_1", "endpointName": "USB2SS_16FFC_MAIN_1_MSTW0", "epriority": true, "groupCount": 0, "name": "MSTW0", "orderId": true, "qos": false, "virtId": false}]