[{"groupName": "Block Copy DMA", "resources": [{"deviceName": "AM62X_DEV_DMASS0_BCDMA_0", "subtypeName": "RESASG_SUBTYPE_BCDMA_BLOCK_COPY_CHAN", "utype": "Block Copy DMA Block copy channel"}, {"deviceName": "AM62X_DEV_DMASS0_BCDMA_0", "subtypeName": "RESASG_SUBTYPE_BCDMA_RING_BLOCK_COPY_CHAN", "utype": "Block Copy DMA Rings for Block copy channel", "copyFromUtype": "Block Copy DMA Block copy channel"}, {"deviceName": "AM62X_DEV_DMASS0_BCDMA_0", "subtypeName": "RESASG_SUBTYPE_BCDMA_SPLIT_TR_RX_CHAN", "utype": "Block Copy DMA Split TR Rx channel"}, {"deviceName": "AM62X_DEV_DMASS0_BCDMA_0", "subtypeName": "RESASG_SUBTYPE_BCDMA_RING_SPLIT_TR_RX_CHAN", "utype": "Block Copy DMA Rings for Split TR Rx channel", "copyFromUtype": "Block Copy DMA Split TR Rx channel"}, {"deviceName": "AM62X_DEV_DMASS0_BCDMA_0", "subtypeName": "RESASG_SUBTYPE_BCDMA_SPLIT_TR_TX_CHAN", "utype": "Block Copy DMA Split TR Tx channel"}, {"deviceName": "AM62X_DEV_DMASS0_BCDMA_0", "subtypeName": "RESASG_SUBTYPE_BCDMA_RING_SPLIT_TR_TX_CHAN", "utype": "Block Copy DMA Rings for Split TR Tx channel", "copyFromUtype": "Block Copy DMA Split TR Tx channel"}, {"deviceName": "AM62X_DEV_DMASS0_BCDMA_0", "subtypeName": "RESASG_SUBTYPE_GLOBAL_EVENT_TRIGGER", "utype": "Block Copy DMA Global event trigger"}, {"deviceName": "AM62X_DEV_DMASS0_BCDMA_0", "subtypeName": "RESASG_SUBTYPE_UDMAP_GLOBAL_CONFIG", "utype": "Block Copy DMA Global config"}]}, {"groupName": "Block copy DMA Interrupt aggregator events", "resources": [{"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_BCDMA_CHAN_DATA_COMPLETION_OES", "utype": "Block copy DMA BC channel data completion event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_BCDMA_CHAN_RING_COMPLETION_OES", "utype": "Block copy DMA BC channel ring completion event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_BCDMA_CHAN_ERROR_OES", "utype": "Block copy DMA BC channel error event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_BCDMA_RX_CHAN_DATA_COMPLETION_OES", "utype": "Block copy DMA Rx channel data completion event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_BCDMA_RX_CHAN_RING_COMPLETION_OES", "utype": "Block copy DMA Rx channel ring completion event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_BCDMA_RX_CHAN_ERROR_OES", "utype": "Block copy DMA Rx channel error event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_BCDMA_TX_CHAN_DATA_COMPLETION_OES", "utype": "Block copy DMA Tx channel data completion event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_BCDMA_TX_CHAN_RING_COMPLETION_OES", "utype": "Block copy DMA Tx channel ring completion event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_BCDMA_TX_CHAN_ERROR_OES", "utype": "Block copy DMA Tx channel error event"}]}, {"groupName": "DMASS Packet DMA", "resources": [{"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_UNMAPPED_TX_CHAN", "utype": "DMASS Packet DMA Free Tx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_UNMAPPED_RX_CHAN", "utype": "DMASS Packet DMA Free Rx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_CPSW_TX_CHAN", "utype": "DMASS Packet DMA CPSW Tx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_CPSW_RX_CHAN", "utype": "DMASS Packet DMA CPSW Rx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_SAUL_TX_0_CHAN", "utype": "DMASS Packet DMA SA2UL Tx channel0"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_SAUL_TX_1_CHAN", "utype": "DMASS Packet DMA SA2UL Tx channel1"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_SAUL_RX_0_CHAN", "utype": "DMASS Packet DMA SA2UL Rx channel0"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_SAUL_RX_1_CHAN", "utype": "DMASS Packet DMA SA2UL Rx channel1"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_SAUL_RX_2_CHAN", "utype": "DMASS Packet DMA SA2UL Rx channel2"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_SAUL_RX_3_CHAN", "utype": "DMASS Packet DMA SA2UL Rx channel3"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_UDMAP_GLOBAL_CONFIG", "utype": "DMASS DMASS UDMA global config"}]}, {"groupName": "DMASS Packet DMA ring accelerator", "resources": [{"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_UNMAPPED_TX_CHAN", "utype": "DMASS Packet DMA Free rings for Tx channel", "copyFromUtype": "DMASS Packet DMA Free Tx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_UNMAPPED_RX_CHAN", "utype": "DMASS Packet DMA Free rings for Rx channel", "copyFromUtype": "DMASS Packet DMA Free Rx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_FLOW_UNMAPPED_RX_CHAN", "utype": "DMASS Packet DMA Free flows for Rx channel", "copyFromUtype": "DMASS Packet DMA Free Rx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_CPSW_TX_CHAN", "utype": "DMASS Packet DMA Rings for CPSW Tx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_CPSW_RX_CHAN", "utype": "DMASS Packet DMA Rings for CPSW Rx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_FLOW_CPSW_RX_CHAN", "utype": "DMASS Packet DMA CPSW Rx flows", "copyFromUtype": "DMASS Packet DMA Rings for CPSW Rx channel"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_SAUL_TX_0_CHAN", "utype": "DMASS Packet DMA Rings for SA2UL Tx channel0"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_SAUL_TX_1_CHAN", "utype": "DMASS Packet DMA Rings for SA2UL Tx channel1"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_SAUL_RX_0_CHAN", "utype": "DMASS Packet DMA Rings for SA2UL Rx channel0"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_FLOW_SAUL_RX_0_CHAN", "utype": "DMASS Packet DMA SA2UL Rx channel0 flows", "copyFromUtype": "DMASS Packet DMA Rings for SA2UL Rx channel0"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_SAUL_RX_1_CHAN", "utype": "DMASS Packet DMA Rings for SA2UL Rx channel1"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_FLOW_SAUL_RX_1_CHAN", "utype": "DMASS Packet DMA SA2UL Rx channel1 flows", "copyFromUtype": "DMASS Packet DMA Rings for SA2UL Rx channel1"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_SAUL_RX_2_CHAN", "utype": "DMASS Packet DMA Rings for SA2UL Rx channel2"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_FLOW_SAUL_RX_2_CHAN", "utype": "DMASS Packet DMA SA2UL Rx channel2 flows", "copyFromUtype": "DMASS Packet DMA Rings for SA2UL Rx channel2"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_RING_SAUL_RX_3_CHAN", "utype": "DMASS Packet DMA Rings for SA2UL Rx channel3"}, {"deviceName": "AM62X_DEV_DMASS0_PKTDMA_0", "subtypeName": "RESASG_SUBTYPE_PKTDMA_FLOW_SAUL_RX_3_CHAN", "utype": "DMASS Packet DMA SA2UL Rx channel3 flows", "copyFromUtype": "DMASS Packet DMA Rings for SA2UL Rx channel3"}, {"deviceName": "AM62X_DEV_DMASS0_RINGACC_0", "subtypeName": "RESASG_SUBTYPE_RA_ERROR_OES", "utype": "DMASS Packet DMA Ring accelerator error event"}, {"deviceName": "AM62X_DEV_DMASS0_RINGACC_0", "subtypeName": "RESASG_SUBTYPE_RA_VIRTID", "utype": "DMASS Packet DMA virt_id range", "autoAlloc": false}]}, {"groupName": "DMASS Packet DMA Interrupt aggregator events", "resources": [{"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_PKTDMA_TX_FLOW_COMPLETION_OES", "utype": "DMASS Packet DMA Tx flow completion event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_PKTDMA_TX_CHAN_ERROR_OES", "utype": "DMASS Packet DMA Tx channel error event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_PKTDMA_RX_FLOW_COMPLETION_OES", "utype": "DMASS Packet DMA Rx flow completion event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_PKTDMA_RX_CHAN_ERROR_OES", "utype": "DMASS Packet DMA Rx channel error event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_PKTDMA_RX_FLOW_STARVATION_OES", "utype": "DMASS Packet DMA Rx flow starvation event"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_PKTDMA_RX_FLOW_FIREWALL_OES", "utype": "DMASS Packet DMA Rx flow firewall event"}]}, {"groupName": "Interrupt aggregators and routers", "resources": [{"deviceName": "AM62X_DEV_CMP_EVENT_INTROUTER0", "subtypeName": "RESASG_SUBTYPE_IR_OUTPUT", "utype": "Compare event Interrupt Router"}, {"deviceName": "AM62X_DEV_MAIN_GPIOMUX_INTROUTER0", "subtypeName": "RESASG_SUBTYPE_IR_OUTPUT", "utype": "MAIN GPIO Interrupt Router", "resRange": [{"resStart": 0, "resCount": 16, "restrictHosts": ["A53_0", "A53_1", "A53_2", "A53_3", "A53_4", "MAIN_0_R5_0", "MAIN_0_R5_1", "MAIN_0_R5_2", "MAIN_0_R5_3"]}, {"resStart": 16, "resCount": 6, "restrictHosts": ["ICSSG_0"]}, {"resStart": 34, "resCount": 2, "restrictHosts": ["M4_0"]}]}, {"deviceName": "AM62X_DEV_WKUP_MCU_GPIOMUX_INTROUTER0", "subtypeName": "RESASG_SUBTYPE_IR_OUTPUT", "utype": "WKUP MCU GPIO Interrupt Router", "resRange": [{"resStart": 0, "resCount": 4, "restrictHosts": ["A53_0", "A53_1", "A53_2", "A53_3", "A53_4", "MAIN_0_R5_0", "MAIN_0_R5_1", "MAIN_0_R5_2", "MAIN_0_R5_3"]}, {"resStart": 4, "resCount": 4, "restrictHosts": ["M4_0"]}, {"resStart": 12, "resCount": 1, "restrictHosts": ["ICSSG_0"]}]}, {"deviceName": "AM62X_DEV_TIMESYNC_EVENT_ROUTER0", "subtypeName": "RESASG_SUBTYPE_IR_OUTPUT", "utype": "Timesync Interrupt Router"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_VINT", "utype": "DMASS Interrupt aggregator Virtual interrupts", "resRange": [{"resStart": 5, "resCount": 35, "restrictHosts": ["A53_0", "A53_1", "A53_2", "A53_3", "A53_4"]}, {"resStart": 44, "resCount": 35, "restrictHosts": ["MAIN_0_R5_0", "MAIN_0_R5_1", "MAIN_0_R5_2", "MAIN_0_R5_3"]}, {"resStart": 80, "resCount": 4, "restrictHosts": ["ICSSG_0"]}, {"resStart": 140, "resCount": 12, "restrictHosts": ["HSM"]}, {"resStart": 168, "resCount": 8, "restrictHosts": ["M4_0"]}]}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_GLOBAL_EVENT_SEVT", "utype": "DMASS Interrupt aggregator Global events"}, {"deviceName": "AM62X_DEV_DMASS0_INTAGGR_0", "subtypeName": "RESASG_SUBTYPE_IA_TIMERMGR_EVT_OES", "utype": "DMASS timer manager event"}]}]