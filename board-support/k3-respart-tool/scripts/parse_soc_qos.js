const args = require("yargs")
	.options({
		doc: {
			alias: "document",
			describe: "Path to SOC JSON file",
			demandOption: true,
			type: "string",
		},
		soc: {
			describe: "Soc name",
			demandOption: true,
			type: "string",
		},
	})
	.help()
	.alias("help", "h").argv;

const qosFields = [
	{
		name:  "QOS",
		max:  8,
		shift:  0,
	},
	{
		name:  "ORDERID",
		max:  16,
		shift:  4,
	},
	{
		name:  "ASEL",
		max:  16,
		shift:  8,
	},
	{
		name:  "EPRIORITY",
		max:  8,
		shift:  12,
	},
	{
		name:  "VIRTID",
		max:  16,
		shift:  16,
	},
	{
		name:  "ATYPE",
		max:  4,
		shift:  28,
	},
];
var qos_header = `
/* Keystone3 Quality of service endpoint definitions
 * Auto generated by K3 Resource Partitioning Tool
 */

`;
function to32Bit(str) {
	var t = "";
	for (var idx = str.length - 1; idx >= 0; idx--) {
		t += str[idx];
		if (t.length === 8) break;
	}

	t = t.split("").reverse().join("");

	return "0x" + t;
}

function grp_count_add(qosArray, base, offset) {

	base = parseInt(base);
	var ep_base = (base + offset) & ~0xff;

	qosArray.forEach((qos) => {
		var qos_base = parseInt(qos.baseAddress);
		if (ep_base == qos_base) {
			qos.groupCount++;
		}
	});
}

function createQosArray(path) {
	var fs = require("fs");
	var qosArray = fs.readFileSync(path).toString();

	qosArray = JSON.parse(qosArray);
	var allData = qosArray;
	qosArray = qosArray.qos.cbass_qos_mmr;

	var finalData = [];

	qosArray.forEach((q) => {

		if (q.control_region && q.control_region == "modss_dmsc_qos_regs")
			return;
		if (q.master_inst.match(/.*navss.*/g))
			return;

		finalData.push({
			deviceName: q.master_inst.toUpperCase(),
			endpointName: q.master_inst.toUpperCase() + "_" + q.master_intf.toUpperCase(),
			name: q.master_intf.toUpperCase(),
			baseAddress: to32Bit(q.mmr_base_addr),
			channelCount: q.channel_count,
			groupCount: 0,

			asel: q.asel_capable,
			atype: q.atype_capable,
			epriority: q.epriority_capable,
			orderId: q.orderid_capable,
			qos: q.qos_capable,
			virtId: q.virtid_capable,
		});
	});

	// set group Count

	var qosRegs = [];

	allData.ip_instances.forEach((i) => {
		if (i.regions) {
			i.regions.forEach((r) => {
				if (r.design_name.match(/qos_regs/g))
					qosRegs.push(r);
			});
		}
	});

	qosRegs.forEach((q) => {
		var base = to32Bit(q.base);
		q.registers.forEach((r) => {
			var name = r.name.split("_");
			if (name.length >= 2) {
				if (name[name.length - 1] === "map1" && name[name.length - 2] === "grp") {
					grp_count_add(finalData, base, r.offset);
				}
			}
		});
	});

	return finalData;
}

function createOutputFile(arr, soc) {
	var fs = require("fs");

	// Make json string from object
	var jsonString = JSON.stringify(arr);

	// write the data to file
	var dir = process.argv[1].substring(0, process.argv[1].lastIndexOf("/"));

	var path = dir + "/../data/" + soc + "/Qos.json";

	fs.writeFile(path, jsonString, (err) => {
		if (err) throw err;
	});
}

function createQosHeader(arr, soc) {
	var fs = require("fs");
	var dir = process.argv[1].substring(0, process.argv[1].lastIndexOf("/"));
	var path = dir + "/../out/" + soc + "_qos.h";
	var qos_data = "";

	qos_data += qos_header;
	qosFields.forEach((f) => {
		for (var i = 0; i < f.max; i++) {
			qos_data += "#define " + f.name + "_" + i + "\t" + "(" + i + " << " + f.shift + ")\n";
		}
		qos_data += "\n";
	});

	arr.forEach((qos) => {
		qos_data += "#define " + qos.endpointName + "\t" + qos.baseAddress + "\n";
	});

	fs.writeFile(path, qos_data, (err) => {
		if (err) throw err;
	});
}

var qosInfo = createQosArray(args.doc);

createOutputFile(qosInfo, args.soc);
createQosHeader(qosInfo, args.soc);
