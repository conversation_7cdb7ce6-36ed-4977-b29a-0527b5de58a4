%%{
	var utils = system.getScript("/scripts/utils.js");
	const deviceSelected = utils.deviceSelected;
	const devData = utils.devData;
	const socName = utils.socName;
	const qos = utils.qos;
	const bwlimiter = utils.bwlimiter;

	var uniqueEndPoint = _.uniq(utils.endPoint());
%%}

/*
 * `socName` Quality of Service (QoS) Configuration Data
 * Auto generated from K3 Resource Partitioning tool
 */
#include <common.h>
#include <asm/arch/hardware.h>
#include "common.h"

struct k3_qos_data `socName`_qos_data[] = {
%if (system.modules["/modules/qosConfig"]) {
%	for (let inst of system.modules["/modules/qosConfig"].$instances) {
	/* `inst.$name` - `inst.qosdev.length` endpoints, `inst.chan.length` channels */
%		_.each(inst.qosdev,(e) => {
%			var n = inst.deviceName + "_" + e;
%			for (var c = 0; c < inst.chan.length; c++) {
%				var channel = inst.chan[c];
	{
		.reg = `qos[n].endpointName` + 0x100 + 0x4 * `channel`,
		.val = `utils.getQosValue(inst)`,
	},
%			}
%		})

%	}
%}

	/* Following registers set 1:1 mapping for orderID MAP1/MAP2
	 * remap registers. orderID x is remapped to orderID x again
	 * This is to ensure orderID from MAP register is unchanged
	 */
%_.each(uniqueEndPoint,(u) => {
%	var grp_cnt = qos[u].groupCount;

	/* `qos[u].endpointName` - `grp_cnt` groups */
%	for(var g = 0 ; g < grp_cnt ; g++ ){
%		var map1_offset = 0x0 + g * 0x8;
%		var map2_offset = 0x4 + g * 0x8;
	{
		.reg = `qos[u].endpointName` + `map1_offset`,
		.val = 0x76543210,
	},
	{
		.reg = `qos[u].endpointName` + `map2_offset`,
		.val = 0xfedcba98,
	},
%	}
%})
%if (system.modules["/modules/bwlimitersConfig"]) {

	/*
	 * Following sections program the BW limiters
	 */
%	for (let inst of system.modules["/modules/bwlimitersConfig"].$instances) {
%		if (bwlimiter[inst.deviceName].readLimiter) {

	/* `inst.$name` : RD_BW_ENABLE = `inst.bw_enable`, RD_TXN_ENABLE = `inst.txn_enable` */
%			var offset = 0;
%			var ctrl_unit = 1;
%		}
%		if (bwlimiter[inst.deviceName].writeLimiter) {

	/* `inst.$name` : WR_BW_ENABLE = `inst.bw_enable`, WR_TXN_ENABLE = `inst.txn_enable` */
%			var offset = 0x100;
%			var ctrl_unit = 2;
%		}
%		var ctrl = 0;
%		if (inst.bw_enable) {
%			ctrl = ctrl + 1 * ctrl_unit;
	{
		.reg = `inst.deviceName` + 0x100 + `utils.decimalToHexadecimal(offset)`,
		.val = `inst.limitCIR`,
	},
	{
		.reg = `inst.deviceName` + 0x104 + `utils.decimalToHexadecimal(offset)`,
		.val = `inst.limitPIR`,
	},
	{
		.reg = `inst.deviceName` + 0x108 + `utils.decimalToHexadecimal(offset)`,
		.val = `inst.limitBurst`,
	},
%		}
%		if (inst.txn_enable) {
%			ctrl = ctrl + 4 * ctrl_unit;
	{
		.reg = `inst.deviceName` + 0x300 + `utils.decimalToHexadecimal(offset)`,
		.val = `inst.limitTxn`,
	},
%		}
	{
		.reg = `inst.deviceName` + 0x4,
		.val = `utils.decimalToHexadecimal(ctrl)`,
	},
%	}
%}
};

uint32_t `socName`_qos_count = sizeof(`socName`_qos_data) / sizeof(`socName`_qos_data[0]);
