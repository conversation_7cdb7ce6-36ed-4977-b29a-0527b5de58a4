%%{
	const utils = system.getScript("/scripts/utils.js");
	const {allocateAndSort} = system.getScript("/scripts/allocation.js");
    const remRes = utils.tifsResRem;
	const resources = utils.resources;

	var allocation = allocateAndSort(true,true);
	var rmConfigEntries = 0;
	var tifsConfigEntries = 0;

	_.each(allocation, a => {
		rmConfigEntries += a.length;
        var utype = a[0].utype;
        if(!(resources[utype].subtypeName in remRes))
        {
            tifsConfigEntries += a.length;
        }
	})
%%}
/*
 * K3 System Firmware Resource Management Board Config Data
 * Auto generated from K3 Resource Partitioning tool
 *
 *
 * Copyright (C) 2019-2024 Texas Instruments Incorporated - https://www.ti.com/
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef SYSFW_IMG_CFG_H
#define SYSFW_IMG_CFG_H

#define BOARDCFG_RM_RESASG_ENTRIES			`rmConfigEntries`
#define BOARDCFG_TIFS_RM_RESASG_ENTRIES		`tifsConfigEntries`

#endif /* SYSFW_IMG_CFG_H */
