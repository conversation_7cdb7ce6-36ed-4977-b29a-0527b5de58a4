%%{
	const utils = system.getScript("/scripts/utils.js");
	const {allocateAndSort} = system.getScript("/scripts/allocation.js");

	const deviceSelected = utils.deviceSelected;
	const devData = utils.devData;
	const socName = utils.socName;
	const resources = utils.resources;
	const hosts = utils.hosts;
%%}
/*
 * K3 System Firmware Resource Management Configuration Data
 * Auto generated from K3 Resource Partitioning tool
 *
 * Copyright (C) 2019-2024 Texas Instruments Incorporated - https://www.ti.com/
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "common.h"
#include <hosts.h>
#include <devices.h>
#include <resasg_types.h>

const struct boardcfg_rm_local `socName`_boardcfg_rm_data = {
	.rm_boardcfg = {
		/* boardcfg_abi_rev */
		.rev = {
			.boardcfg_abi_maj = 0x0,
			.boardcfg_abi_min = 0x1,
		},

		/* boardcfg_rm_host_cfg */
		.host_cfg = {
			.subhdr = {
				.magic = BOARDCFG_RM_HOST_CFG_MAGIC_NUM,
				.size = sizeof (struct boardcfg_rm_host_cfg),
			},
			.host_cfg_entries = {
%		_.each(hosts, (host) => {
%			var moduleName = "/modules/" + socName + "/" + host.hostName;
%			if (system.modules[moduleName]) {
%				var inst = system.modules[moduleName].$static;
				{
					.host_id = HOST_ID_`host.hostName`,
					.allowed_atype = `utils.decimalToBinary(utils.setBit(inst.allowedAtype,3))`,
					.allowed_qos = `utils.decimalToHexadecimal(utils.setBit(inst.allowedqos,8))`,
					.allowed_orderid = `utils.toHexa(utils.unsignedToBinary(inst.allowedorderid))`,
					.allowed_priority = `utils.decimalToHexadecimal(utils.setBit(inst.allowedpriority,8))`,
					.allowed_sched_priority = `utils.decimalToHexadecimal(utils.setBit(inst.allowedschedpriority,4))`,
				},
%			}
%		})
			}
		},

		/* boardcfg_rm_resasg */
		.resasg = {
			.subhdr = {
				.magic = BOARDCFG_RM_RESASG_MAGIC_NUM,
				.size = sizeof (struct boardcfg_rm_resasg),
			},
			.resasg_entries_size =
				BOARDCFG_RM_RESASG_ENTRIES *
				sizeof (struct boardcfg_rm_resasg_entry),
			.reserved = 0,
			/* .resasg_entries is set via boardcfg_rm_local */
		},
	},

	/* This is actually part of .resasg */
	.resasg_entries = {
%	var allocation = allocateAndSort(true,true);
%	_.each(allocation,(all) => {
%	var utype = all[0].utype;
		/* `utype` */
%	_.each(all,(entry) => {
		{
			.start_resource = `entry.start`,
			.num_resource = `entry.count`,
			.type = RESASG_UTYPE (`resources[utype].deviceName`,
					`resources[utype].subtypeName`),
			.host_id = HOST_ID_`entry.hostName`,
		},
%		})
%	})
	},
};
